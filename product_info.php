<?php
/*

  $Id$
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com
  Copyright (c) 2010 osCommerce
  Released under the GNU General Public License

*/

require('includes/application_top.php');

if (!isset($_GET['products_id'])) {
  tep_redirect(tep_href_link(FILENAME_DEFAULT));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PRODUCT_INFO);

$owl_prod_info_slider = true;
$show_owl_carousel_two = true;

require(DIR_WS_MODULES . 'prod_info_functions.php');

$products_id = (int)$_GET['products_id'];
if (!is_numeric($products_id)) {
  $products_id = 0;
}

// recently viewed
if ($visitors_id) {
  $dup_check_query = tep_db_query("select products_id, recently_viewed_id from recently_viewed where visitors_id = '" . $visitors_id . "' order by time desc");
  $vis_count = 0;

  while ($dup_check = tep_db_fetch_array($dup_check_query)) {
    if ($dup_check['products_id'] == $products_id) {
      $duplicate = true;
    }
    $vis_count++;
    $last_recenty_viewed_id = $dup_check['recently_viewed_id'];
  }

  if ($customer_id) {
    $insert_cid = (int)$customer_id;
  } else {
    $insert_cid = 0;
  }

  if ($duplicate) { // update just the time
    tep_db_query("update recently_viewed set customers_id = '" . $insert_cid . "', time = '" . time() . "' where visitors_id = '" . $visitors_id . "' and products_id = '" . $products_id . "'");
  } else {
    if ($vis_count > 10) { // overwrite last id
      tep_db_query("update recently_viewed set customers_id = '" . $insert_cid . "', products_id = '" . $products_id . "', time = '" . time() . "' where recently_viewed_id = '" . $last_recenty_viewed_id . "'");
    } else { // insert
      tep_db_query("insert into recently_viewed (visitors_id, customers_id, products_id, time) values ('" . $visitors_id . "', '" . $insert_cid . "', '" . $products_id . "', '" . time() . "')");
    }
  }
}


$product_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p left join " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c on p.products_id = p2c.products_id left join " . TABLE_CATEGORIES . " c on p2c.categories_id = c.categories_id, " . TABLE_PRODUCTS_TO_STORES . " p2s, " . TABLE_CATEGORIES_TO_STORES . " c2s where p.products_id = '" . $products_id . "' and p.products_id = p2s.products_id and p2s.store_cg = '1' and c.categories_id = c2s.categories_id and c2s.store_cg = '1'");

$product_check = tep_db_fetch_array($product_check_query);

if ($product_check['total'] < 1) {

  // header("HTTP/1.1 404 Not Found");
  // header("Status: 404 Not Found");
  
  // Return a 410 Gone status code
  header("HTTP/1.1 410 Gone");
  $page_not_found = true;

} else {

  $product_info_query = tep_db_query("select p.products_id, pd.products_name, pd.products_description, pd.products_read_more, p.products_model, p.products_quantity, p.products_max_order, p.products_image, p.products_status, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id, p.products_free_shipping, p.products_vat_deductable, p.products_earn_points, p.products_subimage1, p.products_subimage2, p.products_subimage3, p.products_subimage4, p.products_subimage5, p.products_subimage6, p.products_subimage7, p.review_average, pd.products_tasting_notes, pd.products_taste_test, pd.products_awards, p.products_bundle, p.sold_in_bundle_only, p.products_paypal, p.hide_navigation, p.products_cigar_ring_gauge, p.products_cigar_length, p.products_cigar_smoke_time, p.products_event_date from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = '" . $products_id . "' and pd.products_id = p.products_id");

  $product_info = tep_db_fetch_array($product_info_query);

  if ($product_info['products_free_shipping'] == '4' || $product_info['products_free_shipping'] == '5') {
    $lighter_or_gas_in_cart = true; // also in application top shopping cart code - for shipping countdown
  }

  if ($product_info['products_free_shipping'] == '14') {
    $hide_delivery_countdown = true; // also in application top shopping cart code - for shipping countdown
  }

  //if ($product_info['hide_navigation'] == '1') {
  //  $exclusive_product = 1;
  //  if (tep_session_is_registered('customer_id')) {
  //    $newsletter_query = tep_db_query("select customers_newsletter from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
  //    $newsletter = tep_db_fetch_array($newsletter_query);
  //    if ($newsletter['customers_newsletter'] == '1') {
  //      $exclusive_product = 2;
  //    }
  //  }
  //}


  tep_db_query("update " . TABLE_PRODUCTS_DESCRIPTION . " set products_viewed = products_viewed+1 where products_id = '" . $products_id . "' and language_id = '" . (int)$languages_id . "'");

  if ($new_price = tep_get_products_special_price($product_info['products_id'])) {
    $products_price =
      '<div class="prod_info_special_price">
	    <div class="prod_info_special_price_container">
		<div class="online_price">Online Exclusive Price:<strong>' . $currencies->display_price($new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</strong></div>
		<div class="reg_price">Regular Price:<strong>' . $currencies->display_price($product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id'])) . '</strong></div>';
    if ($customer_id == '12794') {
      $amount_saved = ($product_info['products_price'] - $new_price);
      $percentage_saved = round((($product_info['products_price'] - $new_price) / $product_info['products_price']) * 100);
      $products_price .= '<div class="saving_price">You Save: <strong>' . $currencies->display_price($amount_saved, tep_get_tax_rate($product_info['products_tax_class_id'])) . ' (' . $percentage_saved . '%)</strong></div>';
    }
    $products_price .= '</div>
	   </div>
	  ';
    $special_active = true;
  } elseif (tep_check_cPath_array($cPath_array, 666)) {
    $products_price = '<div class="prod_info_price">
	  <div class="regular_price" id="regular-price">' . $currencies->display_price($product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id'])) . '</div>
	  <div id="regular-price"><strong>Excl VAT: ' . $currencies->display_price(($product_info['products_price'] / 1.2), tep_get_tax_rate($product_info['products_tax_class_id'])) . '</strong></div>
	  </div>';
  } else {
    $products_price = '<div class="prod_info_price"><div class="regular_price" id="regular-price">' . $currencies->display_price($product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id'])) . '</div></div>';
  }

}

require(DIR_WS_INCLUDES . 'template_top.php');

if ($page_not_found) {

  include(DIR_WS_MODULES . '404.php');
} else {

  $products_name = $product_info['products_name'];

  $products_description = $product_info['products_description'];

  $products_description = str_replace('$site', STORE_NAME, $products_description);
  $products_description = str_replace('$email', '<a href="mailto:' . STORE_OWNER_EMAIL_ADDRESS . '">' . STORE_OWNER_EMAIL_ADDRESS . '</a>', $products_description);
  $products_description = str_replace('$phone', '0345 604 0044', $products_description);

  if (tep_not_null($product_info['products_date_available']) && ($product_info['products_date_available'] != '0000-00-00 00:00:00') && ($product_info['products_date_available'] > (date("Y-m-d") . ' ' . date("G:i:s")))) {
    if ($product_info['products_quantity'] > '0') {
      $activate_pre_order = true;
    } else {
      $pre_order_no_stock = true;
    }
  }

  // Second Stage Config

  if (tep_not_null($product_info['products_tasting_notes']) || tep_not_null($product_info['products_awards']) || tep_check_cPath_array($cPath_array, 317)) {
    $add_side_desc = true;
  }

    $desc_count = strlen($products_description);

  if ($desc_count < 500 && tep_not_null($product_info['products_tasting_notes']) && tep_check_cPath_array($cPath_array, 317)) {
    $tasting_notes_left = true;
    $tasting_notes_right = false;
  } else {
    $tasting_notes_left = false;
    $tasting_notes_right = true;
  }

  if (tep_not_null($product_info['products_tasting_notes'])) {
    if (tep_check_cPath_array($cPath_array, 322) || tep_check_cPath_array($cPath_array, 1385)) { // For Pipes + Accessories
      $tasting_notes = '<h2 class="toggle-header">Key Info</h2>'; 
    } elseif (tep_check_cPath_array($cPath_array, 317) || tep_check_cPath_array($cPath_array, 697) || tep_check_cPath_array($cPath_array, 169)) { // Cigars + Alcohol + Pipe Tobacco
      $tasting_notes = '<h2 class="toggle-header">Tasting Notes</h2>';      
    } else {
      $tasting_notes = '<h2 class="toggle-header">More Info</h2>';
    }
    $tasting_notes .= '<div class="text_box toggle-content">';
    $tasting_notes .= stripslashes($product_info['products_tasting_notes']);
    $tasting_notes .= '</div>';
  }

  if (tep_not_null($product_info['products_awards'])) {
    $tasting_notes .= '<h2 class="toggle-header">Awards</h2>';
    $tasting_notes .= '<div class="text_box toggle-content">';
    $tasting_notes .= stripslashes($product_info['products_awards']);
    $tasting_notes .= '</div>';
  }

  if (tep_not_null($product_info['products_taste_test'])) {
    $taste_test = '<div class="product_info_text_box_container">';
    $taste_test .= '<h2 class="toggle-header">Taste Test</h2>';
    $taste_test .= '<div class="product_info_text_box toggle-content">' . stripslashes($product_info['products_taste_test']) . '</div>';
    $taste_test .= '</div>';
  }

  // Lighter restriction message
  if ($product_info['products_free_shipping'] == '3') {
   $lighter_restriction_message = '
          <div class="lighter-shipping-note">
            <ul>
              <li>UK Mainland delivery &pound;4.99 or free when you spend over &pound;100 in alcohol.</li>
              <li><a href="customer-service/shipping.htm#chapter8">For delivery outside the UK please click here to view alcohol shipping rates.</a></li>
            </ul>
          </div>';
  } elseif ($product_info['products_free_shipping'] == '5') {
   $lighter_restriction_message = '
           <div class="lighter-shipping-note">
             <ul>
               <li>All lighters are shipped empty of gas due to mail restrictions.</li>
               <li>Lighters must be shipped by courier and UK delivery is free when spending over &pound;100.</li>
               <li>UK Shipping &pound;4.95, Europe &pound;35.00, Worldwide &pound;55.00</li>
             </ul>
           </div>';
  }

?>
  <?php if ($exclusive_product == '1') { ?>
    <style>
      #exclusive-modal {
        padding: 5px 0px 0px 10px;
      }

      #exclusive-modal h2 {
        margin: 0;
        font-size: 20px;
      }

      #exclusive-modal i {
        float: left;
        padding-top: 3px;
        margin-right: 5px;
        font-size: 20px;
      }

      #exclusive-modal span {
        font-size: 14px;
      }

      .sub-button {
        background-color: #009966;
        color: #FFF;
      }
    </style>
    <div style='display:none'>
      <div id="exclusive-modal">
        <h2>This is a C.Gars cigar newsletter exclusive product</h2><br />
        <?php if (!tep_session_is_registered('customer_id')) { ?>
          Please log in to access this product.<br /><br />
          <a class="pure-button sub-button" href="login.php">Log in</a>
        <?php } else { ?>
          You must be a cigar newsletter subscriber to access this product.
        <?php } ?>
      </div>
    </div>
  <?php } ?>
  <?php if (tep_check_cPath_array($cPath_array, 1685) && $disabled) { ?>
    <div class="section_price_match">
      <div id="inner-wrapper">
        <div class="section_price_match_inner" style="padding-top: 10px;">
          <div class="text_box">
            <strong style="color: #CC0000;"><i class="material-icons" style="color: #FF9900;">&#xe8d0;</i> Pipes - Buy one get one half price!</strong> <span>Excludes Dunhill and Parker Pipes. Cheapest pipe is half price. </span>
          </div>
        </div>
      </div>
    </div>
  <?php } ?>

  <div class="prod_info_details">
    <div id="inner-wrapper">
      <div class="pure-g">
        <?php if ($cyber_monday && $rp_disable) { ?>
          <div class="pure-u-24-24">
            <a class="bfd-small" href="specials.php">
              <div class="pure-g">
                <div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-8-24 pure-u-xl-8-24">
                  <div <?php if (!$mobile_view) {
                          echo 'style="float: left; padding-left: 20px;"';
                        } ?>>January Sale</div>
                </div>
                <div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-8-24 pure-u-xl-8-24">Shop Now!</div>
                <div class="pure-u-24-24 pure-u-sm-8-24 pure-u-md-8-24 pure-u-lg-8-24 pure-u-xl-8-24"></div>
              </div>
            </a>
          </div>
        <?php } ?>

        <div class="pure-u-24-24">
          <div class="box-padding-both">
            <h1 class="product-title"><?php echo $products_name; ?></h1>
                <style>
              .hightlight-notice {
                display: none;
                position: absolute;
                background-color: #fff;
                color: #000;
                padding: 10px 15px;
                border-radius: 5px;
                font-size: 15px;
                z-index: 999;
              }
            </style>
            <div class="hightlight-notice" id="hightlight-notice"><strong>Best value guarantee</strong>You're getting great value on this product. We constantly review our prices for the best value.
              <ul>
                <li><i class="fa-solid fa-badge-check"></i>Dispatches the same day when you order before 3pm</li>
                <li><i class="fa-solid fa-badge-check"></i>Delivery 7 days a week (Yep, weekends too !)</li>
                <li><i class="fa-solid fa-badge-check"></i>Over 25,
                  000 Excellent reviews on Trustpilot</li>
              </ul>
            </div>
            <script>
              function showTooltip() {
                const selection = window.getSelection();
                const tooltip = document.getElementById('hightlight-notice');
                const productTitle = document.querySelector('.product-title');

                if (selection.rangeCount > 0) {
                  const range = selection.getRangeAt(0);
                  const selectedText = selection.toString();
                  const commonAncestor = range.commonAncestorContainer;

                  if (selectedText.length > 0 && productTitle.contains(commonAncestor)) {
                    const rect = range.getBoundingClientRect();
                    tooltip.style.display = 'block';

                    tooltip.style.left = `$ {
                      rect.left+window.scrollX
                    }

                    px`;
                    tooltip.style.top = `200px`;
                  } else {
                    tooltip.style.display = 'none';
                  }
                } else {
                  tooltip.style.display = 'none';
                }
              }

              document.addEventListener('selectionchange', showTooltip);
              document.addEventListener('mouseup', showTooltip);
              document.addEventListener('keyup', showTooltip);

              document.addEventListener('mousedown', function() {
                const tooltip = document.getElementById('hightlight-notice');
                tooltip.style.display = 'none';
              });
            </script>
          </div>
        </div>

        <div class="pure-u-24-24">
          <div class="reviews-box">
            <?php

            echo '<a href="' . tep_href_link(FILENAME_PRODUCT_REVIEWS, tep_get_all_get_params()) . '">' . tep_star_rating($product_info['review_average']) . '</a>';

            $average_count = substr($product_info['review_average'], 1); // remove first digit
            $reveiws_rating = substr($product_info['review_average'], 0, 1); // get the first digit only

            if ((int)$reveiws_rating == 1) {
              $review_word = 'Review';
            } else {
              $review_word = 'Reviews';
            }

            if ((int)$reveiws_rating < 1) {
              echo '&nbsp;&nbsp;&nbsp;<span style="color:#666;">' . (int)$average_count . '&nbsp;' . $review_word . '</span>';
            } else {
              echo '&nbsp;&nbsp;&nbsp;<a class="link-button pure-button" href="' . tep_href_link(FILENAME_PRODUCT_REVIEWS, 'products_id=' . $product_info['products_id']) . '">' . (int)$average_count . '&nbsp;' . $review_word . '</a>';
            }
            echo '&nbsp;&nbsp;|&nbsp;&nbsp;' . tep_draw_button('Write a Review', 'comment', tep_href_link(FILENAME_PRODUCT_REVIEWS_WRITE, tep_get_all_get_params()), 'primary', null, 'link-button pure-button') . '';

            ?>
          </div>
        </div>

<div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-12-24 pure-u-lg-8-24 pure-u-xl-10-24">
 <div class="prod-info-wrap-image">
 <?php require(DIR_WS_MODULES . 'product_info/' . 'prod_info_images.php'); ?>
 </div>
</div>

<div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-12-24 pure-u-lg-16-24 pure-u-xl-14-24">

<div class="pure-g">

<div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-17-24 pure-u-xl-12-24">
  
<div class="prod-info-wrap-buttons">
    <div class="pure-g">
      <div class="pure-u-24-24"><?php echo tep_draw_form('cart_quantity', tep_href_link(FILENAME_PRODUCT_INFO, tep_get_all_get_params(array('action')) . 'action=add_product')); ?></div>

  <?php require(DIR_WS_MODULES . 'prod_info_price.php'); ?>

  <?php 
  
  if ($activate_add_to_cart && $product_info['products_price'] > 0.01 && $product_info['products_quantity'] > '0' && (($product_info['products_status'] == '1') || ($product_info['products_status'] == '2' && $activate_pre_order))) {
    require(DIR_WS_MODULES . 'prod_info_qty.php');
    require(DIR_WS_MODULES . 'prod_info_attributes.php');
  } else {
    $button_params = array(params => 'id="availability"');
  }
    
  ?>
  <div class="add-to-cart-box">
    <?php require(DIR_WS_MODULES . 'product_info/' . 'prod_info_buttons.php'); ?>
  </div>

  </div>
</div>
</div>

<div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-7-24 pure-u-xl-12-24">
<div class="prod-info-wrap-fast-buy">
<?php require(DIR_WS_MODULES . 'product_info/' . 'prod_info_fast_buy.php'); ?>
<?php require(DIR_WS_MODULES . 'prod_info_engraving.php'); ?>
<?php require(DIR_WS_MODULES . 'product_info/' . 'prod_info_banners.php'); ?>
</div>
</div>
<!-- end prod_third -->
<div class="pure-u-24-24">
  
  <div class="prod-info-wrap-desc">
  <div class="pure-g">

    <div class="pure-u-24-24">
    
    <h2 class="toggle-header">Description
      <?php if ($product_info['products_model']) { ?>
        <span class="our-ref">Ref # <?php echo $product_info['products_model'] ?></span>
      <?php } ?>
    </h2>
    
    <div class="text_box toggle-content">
      <?php

      echo tep_show_distillery($products_id);

      if ($attribute_text) { 
      echo $attribute_text . '<br />';
      }

      echo stripslashes($products_description);

      if($product_info['products_read_more']){
       echo '<div class="read-more">Read More</div>';
       echo '<div class="read-more-description">' . $product_info['products_read_more'] . '</div>';
      }      

      echo $lighter_restriction_message;

      if ($prod_add_on['categories_id']) {
        echo '<br /><br /><strong>This product can only be purchased with a single, pack or box of handmade cigars of the same brand. <br />This excludes mini and club sized cigars.</strong>';
      }

      if ($product_info['products_bundle'] == "yes") {
        tep_display_bundle((int)$products_id, $product_info['products_price'], true);
      }

      if ($sold_in_bundle_only_string) {
        echo $sold_in_bundle_only_string;
      }

      if ($product_info['products_paypal'] == 1 && !$disable_paypal_banners) {
        echo '<strong style="color: #CC0000;">We accept PayPal payments to &#112;&#097;&#121;&#112;&#097;&#108;&#064;&#099;&#103;&#097;&#114;&#115;&#108;&#116;&#100;&#046;&#099;&#111;&#046;&#117;&#107;</strong>';
      }

      ?>
      
    </div>
    </div>

      <?php if ($tasting_notes) {  ?>
      <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-24-24 pure-u-xl-24-24">  
        <?php echo $tasting_notes; ?>
      </div>
      <?php } ?>

    <?php 
  if($taste_test){
   echo '<div class="pure-u-24-24">';
   echo $taste_test;
   echo '</div>';
  }    
  ?>

</div>
</div>

</div>
</div>

</div>


      </div>
    </div>
  </div>

  <?php 
    
    require(DIR_WS_MODULES . 'product_info/' . 'prod_info_spec.php');

    require(DIR_WS_MODULES . 'product_info/' . 'prod_info_bullet_points.php'); 

    echo '<div class="inner-wrapper"><div class="inner-wrapper-pad">';
    echo '<div class="pure-g">';
 
    require(DIR_WS_MODULES . 'product_info/' . 'prod_info_multi_box.php'); 
    
    echo '<div class="pure-u-24-24"><div class="prod-info-reviews-section">';
    
    if ((int)$average_count > 0) {

      echo '<h2>Reviews</h2>';
      echo '<div class="text_box">';
      include(DIR_WS_MODULES . 'reviews_module.php');
      echo '</div>';

      if (tep_session_is_registered('customer_id')) {

        echo '<h2>Write a Review</h2>';
        echo '<div class="text_box">';
        include(DIR_WS_MODULES . 'reviews_write_module.php');
        echo '</div>';
      }
    } else {

      echo '<h2>Reviews</h2>';
      echo '<div class="text_box">';
      echo 'There are currently no reviews.';
      echo '</div>';
    }

    echo '</div></div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>'; // inner-wrapper
  
  ?>
    <div class="section_grey">
      <div id="inner-wrapper">
        <div class="pure-g">

          <div class="pure-u-24-24 pure-u-sm-12-24">
            <h2>Our Price Promise</h2>
            <div class="text_box">
              <strong style="color: #FFF;">We won't be beaten on price!</strong><br /><br />
              If you can find any of our products cheaper from any online Cigar merchant, please send a link to <a style="color: #FFF;" href="mailto:&#099;&#117;&#115;&#116;&#111;&#109;&#101;&#114;&#099;&#097;&#114;&#101;&#064;&#099;&#103;&#097;&#114;&#115;&#108;&#116;&#100;&#046;&#099;&#111;&#046;&#117;&#107;">&#099;&#117;&#115;&#116;&#111;&#109;&#101;&#114;&#099;&#097;&#114;&#101;&#064;&#099;&#103;&#097;&#114;&#115;&#108;&#116;&#100;&#046;&#099;&#111;&#046;&#117;&#107;</a> and we will be more than happy to price match.
            </div>
          </div>

          <div class="pure-u-24-24 pure-u-sm-12-24">
            <h2>Ordering</h2>
            <div class="text_box">
              <?php include('html/product_info.htm'); ?>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div id="inner-wrapper">
      <div class="pure-g">
        <div class="pure-u-24-24">
          <?php include(DIR_WS_MODULES . FILENAME_MITCHELL_RECOMMENDS); ?>
        </div>
      </div>
    </div>
    <script type="text/javascript" src="//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-56c9fc4bde39b2fc"></script>
    <script type="text/javascript" src="https://apis.google.com/js/plusone.js">
      {
        lang: 'en-GB'
      }
    </script>
  <?php
}

require(DIR_WS_INCLUDES . 'template_bottom.php');
require(DIR_WS_INCLUDES . 'application_bottom.php');

  ?>