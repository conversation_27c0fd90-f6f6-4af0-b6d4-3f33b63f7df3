<?php
$features = ['tailwind', 'tagify'];
require('includes/application_top.php');

// Bulk tag update logic
if (isset($_GET['action']) && $_GET['action'] == 'update' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['product_new_tags'])) {
        foreach ($_POST['product_new_tags'] as $product_id => $tags) {
            tep_db_query("DELETE FROM products_tags WHERE product_id = '" . (int)$product_id . "'");
            if (tep_not_null($tags)) {
                $tagArr = [];
                // Try to decode as JSON array (Tagify format)
                $decoded = json_decode($tags, true);
                if (is_array($decoded) && isset($decoded[0]['value'])) {
                    // Tagify JSON array: extract only the value
                    foreach ($decoded as $tagObj) {
                        if (isset($tagObj['value']) && trim($tagObj['value']) !== '') {
                            $tagArr[] = trim($tagObj['value']);
                        }
                    }
                } else {
                    // Fallback: treat as comma-separated string
                    $tagArr = array_map('trim', explode(',', $tags));
                }
                // Save each tag as plain string (not JSON, not value key)
                foreach ($tagArr as $tag) {
                    if ($tag !== '') {
                        tep_db_query("INSERT INTO products_tags (product_id, tag) VALUES ('" . (int)$product_id . "', '" . tep_db_input($tag) . "')");
                    }
                }
            }
        }
        echo '<div class="messageStackSuccess">Tags updated successfully!</div>';
    }
}

// Parameters for pagination and filtering
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$row_by_page = isset($_GET['row_by_page']) ? (int)$_GET['row_by_page'] : 50;
$search_keywords = isset($_GET['search_keywords']) ? trim($_GET['search_keywords']) : '';
$current_category_id = isset($_GET['cPath']) ? (int)$_GET['cPath'] : 0;
$manufacturer = isset($_GET['manufacturer']) ? (int)$_GET['manufacturer'] : 0;
$supplier = isset($_GET['supplier']) ? (int)$_GET['supplier'] : 0;

// Build SQL WHERE clause based on filters
$where = ' WHERE 1 ';
if ($search_keywords !== '') {
    $where .= " AND (pd.products_name LIKE '%" . tep_db_input($search_keywords) . "%' OR p.products_model LIKE '%" . tep_db_input($search_keywords) . "%')";
}
if ($current_category_id > 0) {
    $where .= " AND p2c.categories_id = '" . (int)$current_category_id . "' ";
}
if ($manufacturer > 0) {
    $where .= " AND p.manufacturers_id = '" . (int)$manufacturer . "' ";
}
if ($supplier > 0) {
    $where .= " AND p.suppliers_id = '" . (int)$supplier . "' ";
}

// Main product query with pagination
$products_query_raw = "SELECT p.products_id, p.products_model, p.products_last_modified, pd.products_name FROM products p LEFT JOIN products_description pd ON p.products_id = pd.products_id AND pd.language_id = '1' LEFT JOIN products_to_categories p2c ON p.products_id = p2c.products_id $where GROUP BY p.products_id ORDER BY p.products_last_modified DESC";
$offset = ($page - 1) * $row_by_page;
$products_query_raw .= " LIMIT $offset, $row_by_page";
$products_query = tep_db_query($products_query_raw);

// Check if there is a next page for pagination
$next_page_query = tep_db_query(str_replace("LIMIT $offset, $row_by_page", "LIMIT " . ($offset + $row_by_page) . ", 1", $products_query_raw));
$has_next_page = tep_db_num_rows($next_page_query) > 0;
$num_pages = $page + ($has_next_page ? 1 : 0);

// Prepare dropdown arrays for filters
$row_bypage_array = array();
for ($i = 10; $i <= 200; $i += 10) {
    $row_bypage_array[] = array('id' => $i, 'text' => $i);
}
$manufacturers_array = array(array('id' => '0', 'text' => 'All Manufacturers'));
$manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from manufacturers order by manufacturers_name");
while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
    $manufacturers_array[] = array('id' => $manufacturers['manufacturers_id'], 'text' => $manufacturers['manufacturers_name']);
}
$suppliers_array_filter = array(array('id' => '0', 'text' => 'All Suppliers'));
$suppliers_query = tep_db_query("select suppliers_id, suppliers_name from suppliers order by suppliers_name");
while ($suppliers = tep_db_fetch_array($suppliers_query)) {
    $suppliers_array_filter[] = array('id' => $suppliers['suppliers_id'], 'text' => $suppliers['suppliers_name']);
}

require(DIR_WS_INCLUDES . 'template_top.php');
?>
<div class="max-w-7xl mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-8">Quick Tag Updates</h1>
    <!-- Filter Form -->
    <form method="get" class="flex flex-col md:flex-row md:items-end gap-4 mb-8 bg-white p-6 rounded-lg shadow border border-gray-200">
        <div class="flex-1 min-w-[180px]">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Rows per page</label>
            <?php echo tep_draw_pull_down_menu('row_by_page', $row_bypage_array, $row_by_page, 'class="w-full p-2 border border-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="this.form.submit();"'); ?>
        </div>
        <div class="flex-1 min-w-[180px]">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Search</label>
            <input type="text" name="search_keywords" value="<?php echo htmlspecialchars($search_keywords); ?>" class="w-full p-2 border border-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Model or Name">
        </div>
        <div class="flex-1 min-w-[180px]">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Category</label>
            <?php echo tep_draw_pull_down_menu('cPath', tep_get_category_tree(), $current_category_id, 'class="w-full p-2 border border-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="this.form.submit();"'); ?>
        </div>
        <div class="flex-1 min-w-[180px]">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Manufacturer</label>
            <?php echo tep_draw_pull_down_menu('manufacturer', $manufacturers_array, $manufacturer, 'class="w-full p-2 border border-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="this.form.submit();"'); ?>
        </div>
        <div class="flex-1 min-w-[180px]">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Supplier</label>
            <?php echo tep_draw_pull_down_menu('supplier', $suppliers_array_filter, $supplier, 'class="w-full p-2 border border-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="this.form.submit();"'); ?>
        </div>
        <div class="flex items-end">
            <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold rounded-md shadow">Apply</button>
        </div>
    </form>

    <!-- Product Tag Update Table -->
    <form name="update" method="POST" action="<?php echo basename($PHP_SELF) . '?action=update&page=' . $page . '&row_by_page=' . $row_by_page . '&manufacturer=' . $manufacturer . '&cPath=' . $current_category_id . '&supplier=' . $supplier . '&search_keywords=' . urlencode($search_keywords); ?>">
        <div class="overflow-x-auto bg-white rounded-lg shadow border border-gray-200">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-normal">Name</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tags</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-100">
                    <?php while ($prod = tep_db_fetch_array($products_query)) {
                        // Fetch tags for the current product
                        $tags_query = tep_db_query("SELECT tag FROM products_tags WHERE product_id = '" . (int)$prod['products_id'] . "'");
                        $tags = [];
                        while ($tag_row = tep_db_fetch_array($tags_query)) {
                            $tags[] = $tag_row['tag'];
                        }
                        $tags_str = implode(', ', $tags);
                    ?>
                        <tr>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800"><?php echo htmlspecialchars($prod['products_model']); ?></td>
                            <td class="px-4 py-2 whitespace-normal text-sm text-gray-800"><?php echo htmlspecialchars($prod['products_name']); ?></td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800">
                                <!-- Tagify input for tags -->
                                <input type="text" name="product_new_tags[<?php echo $prod['products_id']; ?>]" value="<?php echo htmlspecialchars($tags_str); ?>" class="tagify-input w-full min-w-[500px] max-w-2xl p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" style="min-width:400px; max-width:900px; white-space:nowrap;" placeholder="e.g. fathers day, halloween, christmas" autocomplete="off">
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>
        <div class="flex justify-between items-center mt-6">
            <div></div>
            <div class="modern-pagination flex gap-1">
                <?php
                // Modern Pagination (orders.php style)
                $limit = $row_by_page;
                $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                if ($page < 1) $page = 1;
                $adjacents = 3;
                $start = max(1, $page - $adjacents);
                $end = $page + ($has_next_page ? $adjacents : 0);
                $get_params = $_GET;
                unset($get_params['page']);
                function build_page_link_tag($page_num, $get_params) {
                    $params = $get_params;
                    $params['page'] = $page_num;
                    return basename($_SERVER['PHP_SELF']) . '?' . http_build_query($params);
                }
                // First
                if ($page > 1) {
                    echo '<a href="' . build_page_link_tag(1, $get_params) . '" class="px-3 py-1 rounded border bg-white text-gray-700 hover:bg-blue-50">First</a>';
                    echo '<a href="' . build_page_link_tag($page - 1, $get_params) . '" class="px-3 py-1 rounded border bg-white text-gray-700 hover:bg-blue-50">&laquo; Prev</a>';
                } else {
                    echo '<span class="px-3 py-1 rounded border bg-gray-100 text-gray-400">First</span>';
                    echo '<span class="px-3 py-1 rounded border bg-gray-100 text-gray-400">&laquo; Prev</span>';
                }
                // Middle
                for ($i = $start; $i <= $end; $i++) {
                    if ($i == $page) {
                        echo '<span class="px-3 py-1 rounded border bg-blue-700 text-white border-blue-700 font-bold shadow">' . $i . '</span>';
                    } else {
                        echo '<a href="' . build_page_link_tag($i, $get_params) . '" class="px-3 py-1 rounded border bg-white text-gray-700 hover:bg-blue-50">' . $i . '</a>';
                    }
                }
                // Last
                if ($has_next_page) {
                    echo '<a href="' . build_page_link_tag($page + 1, $get_params) . '" class="px-3 py-1 rounded border bg-white text-gray-700 hover:bg-blue-50">Next &raquo;</a>';
                    echo '<a href="' . build_page_link_tag($page + 1, $get_params) . '" class="px-3 py-1 rounded border bg-white text-gray-700 hover:bg-blue-50">Last</a>';
                } else {
                    echo '<span class="px-3 py-1 rounded border bg-gray-100 text-gray-400">Next &raquo;</span>';
                    echo '<span class="px-3 py-1 rounded border bg-gray-100 text-gray-400">Last</span>';
                }
                ?>
            </div>
            <div>
                <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold rounded-md shadow">Update Tags</button>
            </div>
        </div>
    </form>
</div>
<!-- Tagify JS (CDN) -->

<script>
    // Initialize Tagify for all tag inputs
    // This will enable tag chips and autocomplete suggestions from ajax_tag_search.php

    document.querySelectorAll('.tagify-input').forEach(function(input) {
        var tagify = new Tagify(input, {
            whitelist: [], // Will be dynamically loaded
            dropdown: {
                enabled: 1, // Show suggestions after 1 character
                maxItems: 10,
                classname: 'tags-look',
                fuzzySearch: true,
                position: 'all',
                highlightFirst: true
            },
            enforceWhitelist: false,
            delimiters: ",",
            editTags: 1,
            // Ensure form submits a plain comma-separated string, not JSON
            originalInputValueFormat: valuesArr => valuesArr.map(item => item.value).join(', ')
        });

        // Fetch suggestions from the server as the user types
        tagify.on('input', function(e) {
            var value = e.detail.value;
            fetch('ajax/ajax_tag_search.php?q=' + encodeURIComponent(value))
                .then(response => response.json())
                .then(function(suggestions) {
                    tagify.settings.whitelist = suggestions;
                    tagify.dropdown.show.call(tagify, value);
                });
        });
    });
</script>
<?php
require(DIR_WS_INCLUDES . 'template_bottom.php');
