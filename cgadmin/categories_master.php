<?php
/*
  $Id: categories.php,v 1.146 2003/07/11 14:40:27 hpdl Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$email_generic_heading = 'Sampler notification';
$email_generic_sub_heading = 'A sampler\'s status has changed';

// load email templates
include('../' . DIR_WS_LANGUAGES . 'english/email_generic_template.php');

if (isset($_GET['pID'])) {
  $pID = $_GET['pID'];
}

$action = (isset($_GET['action']) ? $_GET['action'] : '');

if (tep_not_null($action) && !$restrict_user_updates) {
  switch ($action) {
    //sort order
    case 'beginsort':
      $sorting = true;
      break;
      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_POST['cPath']));
    case 'setsortorder':
      for ($i = 0, $n = sizeof($_POST['products_id']); $i < $n; $i++) {
        tep_set_product_sort_order($_POST['products_id'][$i], $_POST['sortorder'][$i]);
      }
      $sorting = false;
      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_POST['cPath']));
      break;
    //end sort order

    //master site select
    case 'beginsiteselect':
      $siteselecting = true;
      break;
      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_POST['cPath']));
    case 'setsiteselect':
      for ($i = 0, $n = sizeof($_POST['products_id_cg']); $i < $n; $i++) {
        $cg_array = $_POST['siteselect_cg'];
        if (in_array($_POST['products_id_cg'][$i], (array)$cg_array, true)) {
          $checked = '1';
        } else {
          $checked = '0';
        }
        tep_set_product_site_select_cg($_POST['products_id_cg'][$i], $checked);
        tep_set_product_visible_status($_POST['products_id_cg'][$i]);
      }
      for ($i = 0, $n = sizeof($_POST['products_id_ho']); $i < $n; $i++) {
        $ho_array = $_POST['siteselect_ho'];
        if (in_array($_POST['products_id_ho'][$i], (array)$ho_array, true)) {
          $checked = '1';
        } else {
          $checked = '0';
        }
        tep_set_product_site_select_ho($_POST['products_id_ho'][$i], $checked);
        tep_set_product_visible_status($_POST['products_id_ho'][$i]);
      }
      for ($i = 0, $n = sizeof($_POST['products_id_hs']); $i < $n; $i++) {
        $hs_array = $_POST['siteselect_hs'];
        if (in_array($_POST['products_id_hs'][$i], (array)$hs_array, true)) {
          $checked = '1';
        } else {
          $checked = '0';
        }
        tep_set_product_site_select_hs($_POST['products_id_hs'][$i], $checked);
        tep_set_product_visible_status($_POST['products_id_hs'][$i]);
      }
      for ($i = 0, $n = sizeof($_POST['products_id_wm']); $i < $n; $i++) {
        $wm_array = $_POST['siteselect_wm'];
        if (in_array($_POST['products_id_wm'][$i], (array)$wm_array, true)) {
          $checked = '1';
        } else {
          $checked = '0';
        }
        tep_set_product_site_select_wm($_POST['products_id_wm'][$i], $checked);
        tep_set_product_visible_status($_POST['products_id_wm'][$i]);
      }
      for ($i = 0, $n = sizeof($_POST['products_id_dc']); $i < $n; $i++) {
        $af_array = $_POST['siteselect_dc'];
        if (in_array($_POST['products_id_dc'][$i], (array)$af_array, true)) {
          $checked = '1';
        } else {
          $checked = '0';
        }
        tep_set_product_site_select_dc($_POST['products_id_dc'][$i], $checked);
        tep_set_product_visible_status($_POST['products_id_dc'][$i]);
      }
      for ($i = 0, $n = sizeof($_POST['products_id_tt']); $i < $n; $i++) {
        $tt_array = $_POST['siteselect_tt'];
        if (in_array($_POST['products_id_tt'][$i], (array)$tt_array, true)) {
          $checked = '1';
        } else {
          $checked = '0';
        }
        tep_set_product_site_select_tt($_POST['products_id_tt'][$i], $checked);
        tep_set_product_visible_status($_POST['products_id_tt'][$i]);
      }

      if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('cat_links');
        tep_reset_cache_block('also_purchased');
        tep_reset_cache_block('mega_menu');
        tep_reset_cache_block('mega_menu_mobile');
        tep_reset_cache_block('mega_menu_davidoff');
        tep_reset_cache_block('filter_menu'); // 4 hour intervals
      }

      $siteselecting = false;
      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_POST['cPath']));
      break;
    //master site select

    //master site cat select
    case 'beginsitecatselect':
      $sitecatselecting = true;
      break;
      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_POST['cPath']));
    case 'setsitecatselect':

      foreach ($_POST['sitecatselect_old_cg'] as $id => $old_value) {
        // an unchecked box is now checked
        if (($_POST['sitecatselect_old_cg'][$id] == 9) && (isset($_POST['sitecatselect_new_cg'][$id]))) {
          tep_set_category_site_select_cg($id, '1');
          tep_set_category_visible_status($id);
        }
        // a checked box is now unchecked
        if (($_POST['sitecatselect_old_cg'][$id] == 1) && (!isset($_POST['sitecatselect_new_cg'][$id]))) {
          tep_set_category_site_select_cg($id, '0');
          tep_set_category_visible_status($id);
        }
      }

      foreach ($_POST['sitecatselect_old_ho'] as $id => $old_value) {
        // an unchecked box is now checked
        if (($_POST['sitecatselect_old_ho'][$id] == 9) && (isset($_POST['sitecatselect_new_ho'][$id]))) {
          tep_set_category_site_select_ho($id, '1');
          tep_set_category_visible_status($id);
        }
        // a checked box is now unchecked
        if (($_POST['sitecatselect_old_ho'][$id] == 1) && (!isset($_POST['sitecatselect_new_ho'][$id]))) {
          tep_set_category_site_select_ho($id, '0');
          tep_set_category_visible_status($id);
        }
      }

      foreach ($_POST['sitecatselect_old_hs'] as $id => $old_value) {
        // an unchecked box is now checked
        if (($_POST['sitecatselect_old_hs'][$id] == 9) && (isset($_POST['sitecatselect_new_hs'][$id]))) {
          tep_set_category_site_select_hs($id, '1');
          tep_set_category_visible_status($id);
        }
        // a checked box is now unchecked
        if (($_POST['sitecatselect_old_hs'][$id] == 1) && (!isset($_POST['sitecatselect_new_hs'][$id]))) {
          tep_set_category_site_select_hs($id, '0');
          tep_set_category_visible_status($id);
        }
      }

      foreach ($_POST['sitecatselect_old_wm'] as $id => $old_value) {
        // an unchecked box is now checked
        if (($_POST['sitecatselect_old_wm'][$id] == 9) && (isset($_POST['sitecatselect_new_wm'][$id]))) {
          tep_set_category_site_select_wm($id, '1');
          tep_set_category_visible_status($id);
        }
        // a checked box is now unchecked
        if (($_POST['sitecatselect_old_wm'][$id] == 1) && (!isset($_POST['sitecatselect_new_wm'][$id]))) {
          tep_set_category_site_select_wm($id, '0');
          tep_set_category_visible_status($id);
        }
      }

      foreach ($_POST['sitecatselect_old_dc'] as $id => $old_value) {
        // an unchecked box is now checked
        if (($_POST['sitecatselect_old_dc'][$id] == 9) && (isset($_POST['sitecatselect_new_dc'][$id]))) {
          tep_set_category_site_select_dc($id, '1');
          tep_set_category_visible_status($id);
        }
        // a checked box is now unchecked
        if (($_POST['sitecatselect_old_dc'][$id] == 1) && (!isset($_POST['sitecatselect_new_dc'][$id]))) {
          tep_set_category_site_select_dc($id, '0');
          tep_set_category_visible_status($id);
        }
      }

      foreach ($_POST['sitecatselect_old_tt'] as $id => $old_value) {
        // an unchecked box is now checked
        if (($_POST['sitecatselect_old_tt'][$id] == 9) && (isset($_POST['sitecatselect_new_tt'][$id]))) {
          tep_set_category_site_select_tt($id, '1');
          tep_set_category_visible_status($id);
        }
        // a checked box is now unchecked
        if (($_POST['sitecatselect_old_tt'][$id] == 1) && (!isset($_POST['sitecatselect_new_tt'][$id]))) {
          tep_set_category_site_select_tt($id, '0');
          tep_set_category_visible_status($id);
        }
      }

      if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('cat_links');
        tep_reset_cache_block('also_purchased');
        tep_reset_cache_block('mega_menu');
        tep_reset_cache_block('mega_menu_mobile');
        tep_reset_cache_block('mega_menu_davidoff');
        tep_reset_cache_block('filter_menu'); // 4 hour intervals
      }

      $sitecatselecting = false;

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_POST['cPath']));
      break;
    //master site cat select

    case 'setflag':
      if (($_GET['flag'] == '0') || ($_GET['flag'] == '1') || ($_GET['flag'] == '2') || ($_GET['flag'] == '3') || ($_GET['flag'] == '4') || ($_GET['flag'] == '5')  || ($_GET['flag'] == '6')  || ($_GET['flag'] == '7')) {
        if (isset($_GET['pID'])) {

          // 2 places on this page only - tep_set_products_status & tep_set_categories_status
          if (is_numeric($admin['id'])) {
            if ($allow_product_and_category_status_updates) {
              tep_set_product_status($_GET['pID'], $_GET['flag']);
              tep_set_product_visible_status($_GET['pID']);
              tep_log_this($admin['username'] . ' set product status to ' . $_GET['flag'], $_GET['pID']);
            }
          }
        }

        if (USE_CACHE == 'true') {
          tep_reset_cache_block('categories');
          tep_reset_cache_block('cat_links');
          tep_reset_cache_block('also_purchased');
          tep_reset_cache_block('mega_menu');
          tep_reset_cache_block('mega_menu_mobile');
          tep_reset_cache_block('mega_menu_davidoff');
          tep_reset_cache_block('filter_menu'); // 4 hour intervals
        }
      }

      if (isset($_GET['flag']) && isset($_GET['pID']) && $_GET['flag'] == '1') {

        $notification_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_NOTIFICATIONS . " n where products_id = '" . $_GET['pID'] . "'");
        $notification = tep_db_fetch_array($notification_query);

        if ($notification['total'] > 0) {
          tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID'] . '&notify=' . $notification['total']));
        } else {
          tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID']));
        }
      } else {
        tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID']));
      }

      break;
    // ####################### Added Categories Enable / Disable ###############
    case 'setflag_cat':
      if (($_GET['flag'] == '0') || ($_GET['flag'] == '1') || ($_GET['flag'] == '2') || ($_GET['flag'] == '3') || ($_GET['flag'] == '4') || ($_GET['flag'] == '5') || ($_GET['flag'] == '6') || ($_GET['flag'] == '7')) {
        if (isset($_GET['cID'])) {

          // 2 places on this page only - tep_set_product_status & tep_set_categories_status
          if (is_numeric($admin['id'])) {
            if ($allow_product_and_category_status_updates) {
              tep_set_categories_status($_GET['cID'], $_GET['flag']);
              tep_set_category_visible_status($_GET['cID']);
              tep_log_this($admin['username'] . ' set category status to ' . $_GET['flag'] . '. Category id is: ' . $_GET['cID'], 1);
            }
          }
        }

        if (USE_CACHE == 'true') {
          tep_reset_cache_block('categories');
          tep_reset_cache_block('search_box');
          tep_reset_cache_block('cat_links');
          tep_reset_cache_block('also_purchased');
          tep_reset_cache_block('mega_menu');
          tep_reset_cache_block('mega_menu_mobile');
          tep_reset_cache_block('mega_menu_davidoff');
          tep_reset_cache_block('filter_menu'); // 4 hour intervals
        }
      }

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $_GET['cPath'] . '&cID=' . $_GET['cID']));
      break;
    // ####################### End Categories Enable / Disable ###############
    case 'insert_category':
    case 'update_category':
      if (isset($_POST['categories_id'])) $categories_id = tep_db_prepare_input($_POST['categories_id']);
      $sort_order = tep_db_prepare_input($_POST['sort_order']);

      // ####################### Added Categories Enable / Disable ###############
      $categories_status = tep_db_prepare_input($_POST['categories_status']);
      // Mega Menu Columns
      $mm_col = tep_db_prepare_input($_POST['mm_col']);
      $sub_cats_show_image = tep_db_prepare_input($_POST['sub_cats_show_image']);

      $sql_data_array = array('sort_order' => $sort_order, 'categories_status' => $categories_status, 'mm_col' => $mm_col, 'sub_cats_show_image' => $sub_cats_show_image);




      if ($action == 'insert_category') {
        $insert_sql_data = array(
          'parent_id' => $current_category_id,
          'date_added' => 'now()'
        );

        $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

        tep_db_perform(TABLE_CATEGORIES, $sql_data_array);

        $categories_id = tep_db_insert_id();
      } elseif ($action == 'update_category') {
        $update_sql_data = array('last_modified' => 'now()');

        $sql_data_array = array_merge($sql_data_array, $update_sql_data);

        tep_db_perform(TABLE_CATEGORIES, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "'");

        tep_log_this($admin['username'] . ' set category status to ' . $categories_status . '. Category id is: ' . (int)$categories_id, 1);
      }


      // START FOR CATEGORIES_TO_STORES

      if (tep_not_null($_POST['store_cg'])) {
        $cat_store_cg = $_POST['store_cg'];
      } else {
        $cat_store_cg = '0';
      }
      if (tep_not_null($_POST['store_ho'])) {
        $cat_store_ho = $_POST['store_ho'];
      } else {
        $cat_store_ho = '0';
      }
      if (tep_not_null($_POST['store_hs'])) {
        $cat_store_hs = $_POST['store_hs'];
      } else {
        $cat_store_hs = '0';
      }
      if (tep_not_null($_POST['store_wm'])) {
        $cat_store_wm = $_POST['store_wm'];
      } else {
        $cat_store_wm = '0';
      }
      if (tep_not_null($_POST['store_dc'])) {
        $cat_store_dc = $_POST['store_dc'];
      } else {
        $cat_store_dc = '0';
      }
      if (tep_not_null($_POST['store_tt'])) {
        $cat_store_tt = $_POST['store_tt'];
      } else {
        $cat_store_tt = '0';
      }

      $sql_data_array = array(
        'store_cg' => tep_db_prepare_input($cat_store_cg),
        'store_ho' => tep_db_prepare_input($cat_store_ho),
        'store_hs' => tep_db_prepare_input($cat_store_hs),
        'store_wm' => tep_db_prepare_input($cat_store_wm),
        'store_dc' => tep_db_prepare_input($cat_store_dc),
        'store_tt' => tep_db_prepare_input($cat_store_tt)
      );

      if ($action == 'insert_category') {
        $insert_sql_data = array('categories_id' => (int)$categories_id);

        $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

        tep_db_perform(TABLE_CATEGORIES_TO_STORES, $sql_data_array);
      } elseif ($action == 'update_category') {
        tep_db_perform(TABLE_CATEGORIES_TO_STORES, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "'");
      }

      // FINISH FOR CATEGORIES_TO_STORES

      // START FOR HO EXTRA

      // CHECK FOR EXISTING DATA

      $check_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$categories_id . "' and store_id = '2'");

      if (tep_db_num_rows($check_query)) {
        $check_exists = 1;
      } else {
        $check_exists = 0;
      }

      if (tep_not_null($_POST['ho_categories_name']) ||  tep_not_null($_POST['ho_categories_description']) ||  tep_not_null($_POST['ho_categories_htc_title_tag']) ||  tep_not_null($_POST['ho_categories_htc_desc_tag']) ||  tep_not_null($_POST['ho_categories_htc_keywords_tag']) || tep_not_null($ho_categories_image) || tep_not_null($_POST['ho_sort_order']) || tep_not_null($_POST['ho_parent_id']) || $check_exists == 1) {

        $ho_categories_name = tep_db_prepare_input($_POST['ho_categories_name']);
        $ho_categories_description = tep_db_prepare_input($_POST['ho_categories_description']);
        $ho_categories_htc_title_tag = tep_db_prepare_input($_POST['ho_categories_htc_title_tag']);
        $ho_categories_htc_desc_tag = tep_db_prepare_input($_POST['ho_categories_htc_desc_tag']);
        $ho_categories_htc_keywords_tag = tep_db_prepare_input($_POST['ho_categories_htc_keywords_tag']);

        // insert null if its empty because inserting a blank '' gives a value of 0
        if (tep_not_null($_POST['ho_parent_id'])) {
          $ho_parent_id = tep_db_prepare_input($_POST['ho_parent_id']);
        } else {
          $ho_parent_id = 'null';
        }
        if (tep_not_null($_POST['ho_sort_order'])) {
          $ho_sort_order = tep_db_prepare_input($_POST['ho_sort_order']);
        } else {
          $ho_sort_order = 'null';
        }

        $sql_data_array = array(
          'categories_id' => $categories_id,
          'store_id' => '2',
          'parent_id' => $ho_parent_id,
          'sort_order' => $ho_sort_order,
          'categories_name' => $ho_categories_name,
          'categories_htc_title_tag' => $ho_categories_htc_title_tag,
          'categories_htc_desc_tag' => $ho_categories_htc_desc_tag,
          'categories_htc_keywords_tag' => $ho_categories_htc_keywords_tag,
          'categories_description' => $ho_categories_description
        );

        if ($action == 'insert_product' || $check_exists == 0) {

          if ($ho_categories_image = new upload('ho_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($ho_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $ho_categories_image->filename));
            }
          }

          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array);
        } elseif ($action == 'update_category') {

          if ($ho_categories_image = new upload('ho_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($ho_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $ho_categories_image->filename));
            }
          }
          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "' and store_id = '2'");
        }
      }

      // FINISH FOR HO EXTRA

      // START FOR HS EXTRA

      // CHECK FOR EXISTING DATA

      $check_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$categories_id . "' and store_id = '3'");

      if (tep_db_num_rows($check_query)) {
        $check_exists = 1;
      } else {
        $check_exists = 0;
      }

      if (tep_not_null($_POST['hs_categories_name']) ||  tep_not_null($_POST['hs_categories_description']) ||  tep_not_null($_POST['hs_categories_htc_title_tag']) ||  tep_not_null($_POST['hs_categories_htc_desc_tag']) ||  tep_not_null($_POST['hs_categories_htc_keywords_tag']) || tep_not_null($hs_categories_image) || tep_not_null($_POST['hs_sort_order']) || tep_not_null($_POST['hs_parent_id']) || $check_exists == 1) {

        $hs_categories_name = tep_db_prepare_input($_POST['hs_categories_name']);
        $hs_categories_description = tep_db_prepare_input($_POST['hs_categories_description']);
        $hs_categories_htc_title_tag = tep_db_prepare_input($_POST['hs_categories_htc_title_tag']);
        $hs_categories_htc_desc_tag = tep_db_prepare_input($_POST['hs_categories_htc_desc_tag']);
        $hs_categories_htc_keywords_tag = tep_db_prepare_input($_POST['hs_categories_htc_keywords_tag']);

        if (tep_not_null($_POST['hs_parent_id'])) {
          $hs_parent_id = tep_db_prepare_input($_POST['hs_parent_id']);
        } else {
          $hs_parent_id = 'null';
        }
        if (tep_not_null($_POST['hs_sort_order'])) {
          $hs_sort_order = tep_db_prepare_input($_POST['hs_sort_order']);
        } else {
          $hs_sort_order = 'null';
        }

        $sql_data_array = array(
          'categories_id' => $categories_id,
          'store_id' => '3',
          'parent_id' => $hs_parent_id,
          'sort_order' => $hs_sort_order,
          'categories_name' => $hs_categories_name,
          'categories_htc_title_tag' => $hs_categories_htc_title_tag,
          'categories_htc_desc_tag' => $hs_categories_htc_desc_tag,
          'categories_htc_keywords_tag' => $hs_categories_htc_keywords_tag,
          'categories_description' => $hs_categories_description
        );

        if ($action == 'insert_product' || $check_exists == 0) {

          if ($hs_categories_image = new upload('hs_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($hs_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $hs_categories_image->filename));
            }
          }

          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array);
        } elseif ($action == 'update_category') {

          if ($hs_categories_image = new upload('hs_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($hs_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $hs_categories_image->filename));
            }
          }
          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "' and store_id = '3'");
        }
      }

      // FINISH FOR HS EXTRA

      // START FOR CU EXTRA

      // CHECK FOR EXISTING DATA

      $check_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$categories_id . "' and store_id = '6'");

      if (tep_db_num_rows($check_query)) {
        $check_exists = 1;
      } else {
        $check_exists = 0;
      }

      if (tep_not_null($_POST['wm_categories_name']) ||  tep_not_null($_POST['wm_categories_description']) ||  tep_not_null($_POST['wm_categories_htc_title_tag']) ||  tep_not_null($_POST['wm_categories_htc_desc_tag']) ||  tep_not_null($_POST['wm_categories_htc_keywords_tag']) || tep_not_null($wm_categories_image) || tep_not_null($_POST['wm_sort_order']) || tep_not_null($_POST['wm_parent_id']) || $check_exists == 1) {

        $wm_categories_name = tep_db_prepare_input($_POST['wm_categories_name']);
        $wm_categories_description = tep_db_prepare_input($_POST['wm_categories_description']);
        $wm_categories_htc_title_tag = tep_db_prepare_input($_POST['wm_categories_htc_title_tag']);
        $wm_categories_htc_desc_tag = tep_db_prepare_input($_POST['wm_categories_htc_desc_tag']);
        $wm_categories_htc_keywords_tag = tep_db_prepare_input($_POST['wm_categories_htc_keywords_tag']);

        if (tep_not_null($_POST['wm_parent_id'])) {
          $wm_parent_id = tep_db_prepare_input($_POST['wm_parent_id']);
        } else {
          $wm_parent_id = 'null';
        }
        if (tep_not_null($_POST['wm_sort_order'])) {
          $wm_sort_order = tep_db_prepare_input($_POST['wm_sort_order']);
        } else {
          $wm_sort_order = 'null';
        }

        $sql_data_array = array(
          'categories_id' => $categories_id,
          'store_id' => '6',
          'parent_id' => $wm_parent_id,
          'sort_order' => $wm_sort_order,
          'categories_name' => $wm_categories_name,
          'categories_htc_title_tag' => $wm_categories_htc_title_tag,
          'categories_htc_desc_tag' => $wm_categories_htc_desc_tag,
          'categories_htc_keywords_tag' => $wm_categories_htc_keywords_tag,
          'categories_description' => $wm_categories_description
        );

        if ($action == 'insert_product' || $check_exists == 0) {

          if ($wm_categories_image = new upload('wm_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($wm_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $wm_categories_image->filename));
            }
          }

          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array);
        } elseif ($action == 'update_category') {

          if ($wm_categories_image = new upload('wm_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($wm_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $wm_categories_image->filename));
            }
          }
          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "' and store_id = '6'");
        }
      }

      // FINISH FOR CU EXTRA

      // START FOR DC EXTRA

      // CHECK FOR EXISTING DATA

      $check_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$categories_id . "' and store_id = '7'");

      if (tep_db_num_rows($check_query)) {
        $check_exists = 1;
      } else {
        $check_exists = 0;
      }

      if (tep_not_null($_POST['af_categories_name']) ||  tep_not_null($_POST['af_categories_description']) ||  tep_not_null($_POST['af_categories_htc_title_tag']) ||  tep_not_null($_POST['af_categories_htc_desc_tag']) ||  tep_not_null($_POST['af_categories_htc_keywords_tag']) || tep_not_null($af_categories_image) || tep_not_null($_POST['af_sort_order']) || tep_not_null($_POST['af_parent_id']) || $check_exists == 1) {

        $af_categories_name = tep_db_prepare_input($_POST['af_categories_name']);
        $af_categories_description = tep_db_prepare_input($_POST['af_categories_description']);
        $af_categories_htc_title_tag = tep_db_prepare_input($_POST['af_categories_htc_title_tag']);
        $af_categories_htc_desc_tag = tep_db_prepare_input($_POST['af_categories_htc_desc_tag']);
        $af_categories_htc_keywords_tag = tep_db_prepare_input($_POST['af_categories_htc_keywords_tag']);

        if (tep_not_null($_POST['af_parent_id'])) {
          $af_parent_id = tep_db_prepare_input($_POST['af_parent_id']);
        } else {
          $af_parent_id = 'null';
        }
        if (tep_not_null($_POST['af_sort_order'])) {
          $af_sort_order = tep_db_prepare_input($_POST['af_sort_order']);
        } else {
          $af_sort_order = 'null';
        }

        $sql_data_array = array(
          'categories_id' => $categories_id,
          'store_id' => '7',
          'parent_id' => $af_parent_id,
          'sort_order' => $af_sort_order,
          'categories_name' => $af_categories_name,
          'categories_htc_title_tag' => $af_categories_htc_title_tag,
          'categories_htc_desc_tag' => $af_categories_htc_desc_tag,
          'categories_htc_keywords_tag' => $af_categories_htc_keywords_tag,
          'categories_description' => $af_categories_description
        );

        if ($action == 'insert_product' || $check_exists == 0) {

          if ($af_categories_image = new upload('af_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($af_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $af_categories_image->filename));
            }
          }

          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array);
        } elseif ($action == 'update_category') {

          if ($af_categories_image = new upload('af_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($af_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $af_categories_image->filename));
            }
          }
          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "' and store_id = '7'");
        }
      }

      // FINISH FOR DC EXTRA

      // START FOR TT EXTRA

      // CHECK FOR EXISTING DATA

      $check_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$categories_id . "' and store_id = '8'");

      if (tep_db_num_rows($check_query)) {
        $check_exists = 1;
      } else {
        $check_exists = 0;
      }

      if (tep_not_null($_POST['tt_categories_name']) ||  tep_not_null($_POST['tt_categories_description']) ||  tep_not_null($_POST['tt_categories_htc_title_tag']) ||  tep_not_null($_POST['tt_categories_htc_desc_tag']) ||  tep_not_null($_POST['tt_categories_htc_keywords_tag']) || tep_not_null($tt_categories_image) || tep_not_null($_POST['tt_sort_order']) || tep_not_null($_POST['tt_parent_id']) || $check_exists == 1) {

        $tt_categories_name = tep_db_prepare_input($_POST['tt_categories_name']);
        $tt_categories_description = tep_db_prepare_input($_POST['tt_categories_description']);
        $tt_categories_htc_title_tag = tep_db_prepare_input($_POST['tt_categories_htc_title_tag']);
        $tt_categories_htc_desc_tag = tep_db_prepare_input($_POST['tt_categories_htc_desc_tag']);
        $tt_categories_htc_keywords_tag = tep_db_prepare_input($_POST['tt_categories_htc_keywords_tag']);

        if (tep_not_null($_POST['tt_parent_id'])) {
          $tt_parent_id = tep_db_prepare_input($_POST['tt_parent_id']);
        } else {
          $tt_parent_id = 'null';
        }
        if (tep_not_null($_POST['tt_sort_order'])) {
          $tt_sort_order = tep_db_prepare_input($_POST['tt_sort_order']);
        } else {
          $tt_sort_order = 'null';
        }

        $sql_data_array = array(
          'categories_id' => $categories_id,
          'store_id' => '8',
          'parent_id' => $tt_parent_id,
          'sort_order' => $tt_sort_order,
          'categories_name' => $tt_categories_name,
          'categories_htc_title_tag' => $tt_categories_htc_title_tag,
          'categories_htc_desc_tag' => $tt_categories_htc_desc_tag,
          'categories_htc_keywords_tag' => $tt_categories_htc_keywords_tag,
          'categories_description' => $tt_categories_description
        );

        if ($action == 'insert_product' || $check_exists == 0) {

          if ($tt_categories_image = new upload('tt_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($tt_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $tt_categories_image->filename));
            }
          }

          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array);
        } elseif ($action == 'update_category') {

          if ($tt_categories_image = new upload('tt_categories_image', DIR_FS_CATALOG_IMAGES)) {
            if (tep_not_null($tt_categories_image->filename)) {
              $sql_data_array = array_merge($sql_data_array, array('categories_image' => $tt_categories_image->filename));
            }
          }
          tep_db_perform(TABLE_CATEGORIES_STORES_EXTRA, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "' and store_id = '8'");
        }
      }

      // FINISH FOR TT EXTRA

      $languages = tep_get_languages();
      for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
        $categories_name_array = $_POST['categories_name'];
        $categories_name_highlight_array = $_POST['categories_name_highlight'];
        $categories_name_highlight_black_array = $_POST['categories_name_highlight_black'];
        $categories_name_gap_array = $_POST['categories_name_gap'];
        $categories_desc_switch_array = $_POST['categories_desc_switch'];
        $categories_description_array = $_POST['categories_description'];
        $categories_read_more_array = $_POST['categories_read_more'];

        //HTC BOC
        $categories_htc_title_array = $_POST['categories_htc_title_tag'];
        $categories_htc_desc_array = $_POST['categories_htc_desc_tag'];
        $categories_htc_keywords_array = $_POST['categories_htc_keywords_tag'];
        //HTC EOC

        $language_id = $languages[$i]['id'];

        //HTC BOC
        $sql_data_array = array(
          'categories_name' => tep_db_prepare_input($categories_name_array[$language_id]),
          'categories_description' => tep_db_prepare_input($categories_description_array[$language_id]),
          'categories_read_more' => tep_db_prepare_input($categories_read_more_array[$language_id]),
          'categories_htc_title_tag' => (tep_not_null($categories_htc_title_array[$language_id]) ? tep_db_prepare_input($categories_htc_title_array[$language_id]) :  tep_db_prepare_input($categories_name_array[$language_id])),
          'categories_htc_desc_tag' => tep_db_prepare_input($categories_htc_desc_array[$language_id]),
          'categories_htc_keywords_tag' => tep_db_prepare_input($categories_htc_keywords_array[$language_id]),
          'categories_name_highlight' => tep_db_prepare_input($categories_name_highlight_array[$language_id]),
          'categories_name_highlight_black' => tep_db_prepare_input($categories_name_highlight_black_array[$language_id]),
          'categories_name_gap' => tep_db_prepare_input($categories_name_gap_array[$language_id]),
          'categories_desc_switch' => tep_db_prepare_input($categories_desc_switch_array[$language_id])
        );
        //HTC EOC

        if ($action == 'insert_category') {
          $insert_sql_data = array(
            'categories_id' => $categories_id,
            'language_id' => $languages[$i]['id']
          );

          $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

          tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array);
        } elseif ($action == 'update_category') {
          tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array, 'update', "categories_id = '" . (int)$categories_id . "' and language_id = '" . (int)$languages[$i]['id'] . "'");
        }
      }

      if ($categories_image = new upload('categories_image', DIR_FS_CATALOG_IMAGES)) {
        if (!is_null($categories_image->filename)) {
          tep_db_query("update " . TABLE_CATEGORIES . " set categories_image = '" . tep_db_input($categories_image->filename) . "' where categories_id = '" . (int)$categories_id . "'");
        }
      }

      if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('search_box');
        tep_reset_cache_block('cat_links');
        tep_reset_cache_block('also_purchased');
        tep_reset_cache_block('mega_menu');
        tep_reset_cache_block('mega_menu_mobile');
        tep_reset_cache_block('mega_menu_davidoff');
        tep_reset_cache_block('filter_menu'); // 4 hour intervals
      }

      tep_set_category_visible_status((int)$categories_id);

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $categories_id));
      break;
    case 'delete_category_confirm':
      if (isset($_POST['categories_id'])) {
        $categories_id = tep_db_prepare_input($_POST['categories_id']);

        // BEGIN Prevent categories_id = 0
        if ($categories_id == 0) {
          tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $categories_id));
          break;
        }
        // END Prevent categories_id = 0

        $categories = tep_get_category_tree($categories_id, '', '0', '', true);
        $products = array();
        $products_delete = array();

        for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
          $product_ids_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . (int)$categories[$i]['id'] . "'");

          while ($product_ids = tep_db_fetch_array($product_ids_query)) {
            $products[$product_ids['products_id']]['categories'][] = $categories[$i]['id'];
          }
        }

        reset($products);
        while (list($key, $value) = each($products)) {
          $category_ids = '';

          for ($i = 0, $n = sizeof($value['categories']); $i < $n; $i++) {
            $category_ids .= "'" . (int)$value['categories'][$i] . "', ";
          }
          $category_ids = substr($category_ids, 0, -2);

          $check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int)$key . "' and categories_id not in (" . $category_ids . ")");
          $check = tep_db_fetch_array($check_query);
          if ($check['total'] < '1') {
            $products_delete[$key] = $key;
          }
        }

        // removing categories can be a lengthy process
        tep_set_time_limit(0);
        for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
          tep_remove_category($categories[$i]['id']);
        }

        reset($products_delete);
        while (list($key) = each($products_delete)) {
          tep_remove_product($key);
        }
      }

      if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('cat_links');
        tep_reset_cache_block('also_purchased');
        tep_reset_cache_block('mega_menu');
        tep_reset_cache_block('mega_menu_mobile');
        tep_reset_cache_block('mega_menu_davidoff');
        tep_reset_cache_block('filter_menu'); // 4 hour intervals
      }

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath));
      break;

    case 'delete_single_product_confirm':
      if (isset($_POST['products_id']) && isset($_POST['product_categories']) && is_array($_POST['product_categories'])) {
        $product_id = tep_db_prepare_input($_POST['products_id']);
        $product_categories = $_POST['product_categories'];

        for ($i = 0, $n = sizeof($product_categories); $i < $n; $i++) {
          // backup TABLE_PRODUCTS_TO_CATEGORIES data for reports first..
          tep_p2c_history_add_prod_cat((int)$product_id, (int)$product_categories[$i]);
          tep_db_query("delete from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int)$product_id . "' and categories_id = '" . (int)$product_categories[$i] . "'");
        }

        $product_categories_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int)$product_id . "'");
        $product_categories = tep_db_fetch_array($product_categories_query);

        if ($product_categories['total'] == '0') {
          tep_remove_product($product_id);
          tep_log_this($admin['username'] . ' deleted this product ', (int)$product_id);
        }
        /* Optional Related Products (ORP) */
        tep_db_query("delete from " . TABLE_PRODUCTS_RELATED_PRODUCTS . " where pop_products_id_master = '" . (int)$product_id . "'");
        tep_db_query("delete from " . TABLE_PRODUCTS_RELATED_PRODUCTS . " where pop_products_id_slave = '" . (int)$product_id . "'");
        //ORP: end
      }

      if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('cat_links');
        tep_reset_cache_block('also_purchased');
        tep_reset_cache_block('filter_menu'); // 4 hour intervals
      }

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath));
      break;

    case 'delete_product_confirm':
      if (isset($_POST['products_id']) && isset($_POST['product_categories'])) {

        $products_id = tep_db_prepare_input($_POST['products_id']);
        $product_categories = tep_db_prepare_input($_POST['product_categories']);
        $delete_from_all_cats = tep_db_prepare_input($_POST['delete_from_all_cats']);

        foreach ($products_id as $product) {
          // delete the product from all categories or just the current category
          if ($delete_from_all_cats == '1') { // delete all
            tep_p2c_history_add_product($product); // backup TABLE_PRODUCTS_TO_CATEGORIES data for reports first..
            tep_db_query("delete from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . tep_db_input($product) . "'");
          } else {
            tep_p2c_history_add_prod_cat($product, (int)$product_categories[$i]); // backup TABLE_PRODUCTS_TO_CATEGORIES data for reports first..
            tep_db_query("delete from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . tep_db_input($product) . "' and categories_id = '" . (int)$product_categories . "'");
          }

          // find out if the product is in any other category
          $product_categories_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . tep_db_input($product) . "'");
          $product_categories_check = tep_db_fetch_array($product_categories_query);

          // if its not tied to any categories anymore then delete it
          if ($product_categories_check['total'] == '0') {
            tep_remove_product($product);
            tep_log_this($admin['username'] . ' deleted this product ', $product);
          }
        }
      }

      if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('cat_links');
        tep_reset_cache_block('also_purchased');
        tep_reset_cache_block('filter_menu'); // 4 hour intervals
      }

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath));
      break;
    case 'move_category_confirm':
      if (isset($_POST['categories_id']) && ($_POST['categories_id'] != $_POST['move_to_category_id'])) {
        $categories_id = tep_db_prepare_input($_POST['categories_id']);
        $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);

        $path = explode('_', tep_get_generated_category_path_ids($new_parent_id));

        if (in_array($categories_id, $path)) {
          $messageStack->add_session(ERROR_CANNOT_MOVE_CATEGORY_TO_PARENT, 'error');

          tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $categories_id));
        } else {
          tep_db_query("update " . TABLE_CATEGORIES . " set parent_id = '" . (int)$new_parent_id . "', last_modified = now() where categories_id = '" . (int)$categories_id . "'");

          if (USE_CACHE == 'true') {
            tep_reset_cache_block('categories');
            tep_reset_cache_block('search_box');
            tep_reset_cache_block('cat_links');
            tep_reset_cache_block('also_purchased');
            tep_reset_cache_block('mega_menu');
            tep_reset_cache_block('mega_menu_mobile');
            tep_reset_cache_block('mega_menu_davidoff');
            tep_reset_cache_block('filter_menu'); // 4 hour intervals
          }

          tep_set_category_visible_status((int)$categories_id);

          tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $new_parent_id . '&cID=' . $categories_id));
        }
      }

      break;
    case 'move_product_confirm':
      $products_id = tep_db_prepare_input($_POST['products_id']);
      $old_parent_id = tep_db_prepare_input($_POST['move_from_category_id']);
      $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);

      foreach ($products_id as $product) {
        $duplicate_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . tep_db_input($product) . "' and categories_id = '" . tep_db_input($new_parent_id) . "'");
        $duplicate_check = tep_db_fetch_array($duplicate_check_query);
        if ($duplicate_check['total'] < 1) tep_db_query("update " . TABLE_PRODUCTS_TO_CATEGORIES . " set categories_id = '" . tep_db_input($new_parent_id) . "' where products_id = '" . tep_db_input($product) . "' and categories_id = '" . $current_category_id . "'");

        tep_set_product_visible_status(tep_db_input($product));
      }

      if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('cat_links');
        tep_reset_cache_block('also_purchased');
        tep_reset_cache_block('filter_menu'); // 4 hour intervals
      }

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $old_parent_id));
      break;

    case 'copy_to_wm':

      if (isset($_GET['pID'])) $products_id = tep_db_prepare_input($_GET['pID']);

      require_once(DIR_WS_MODULES . 'copy_to_wm.php');

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products_id));
      break;

    case 'save_product_notes':

      $products_id = tep_db_prepare_input($_POST['products_id']);
      $products_admin_notes = tep_db_prepare_input($_POST['products_admin_notes']);

      tep_db_query("update " . TABLE_PRODUCTS_DESCRIPTION . " set products_admin_notes = '" . tep_db_input($products_admin_notes) . "', products_admin_notes_modified = now() where products_id = '" . (int)$products_id . "'");

      $messageStack->add_session("Product Notes updated.", 'success');

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products_id));
      break;

    case 'save_category_notes':

      $categories_id = tep_db_prepare_input($_POST['categories_id']);
      $category_admin_notes = tep_db_prepare_input($_POST['category_admin_notes']);

      tep_db_query("update " . TABLE_CATEGORIES_DESCRIPTION . " set category_admin_notes = '" . tep_db_input($category_admin_notes) . "', category_admin_notes_modified = now() where categories_id = '" . (int)$categories_id . "'");

      $messageStack->add_session("Category Notes updated.", 'success');

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $categories_id));
      break;

    case 'send_product_tweet':

      $products_id = tep_db_prepare_input($_POST['products_id']);
      $products_admin_tweet = tep_db_prepare_input($_POST['products_admin_tweet']) . ' ';

      require_once(DIR_WS_MODULES . 'twitter/twitter.class.php');

      // ENTER HERE YOUR CREDENTIALS (see readme.txt)
      //$twitter = new Twitter($consumerKey, $consumerSecret, $accessToken, $accessTokenSecret);

      // Richard
      // $twitter = new Twitter('*************************', 'owDF2zwLfai2KSC6ES4pxolHI41yfxQTlFo3FCALqtGjb4YPGs', '1002158763049213952-6E7TVvYOoDIy8jFy8yhaomVboASMov', 'TlE4mlusPKzOxWgKw9kr8ET2WRYrKepPc71gLV425tOiB');

      $twitter = new Twitter('*************************', 'msngcK4ps7U3N15dFF9yG1HmNEso3VTRlDnrhpygMCvCv2bsKm', '27025045-gHDINUczFnE1MZETSq4cjmYyZpelgYfADWRHGFZFY', 'cr8rcEDfhOperXK0nAYZ7QzbZa3XavC9S2O6SjQSMzMbA');

      require_once('includes/modules/twitter/URLResolver.php');

      $resolver = new URLResolver();

      $catalog_raw_url = tep_catalog_href_link('product_info.php', 'products_id=' . $products_id);

      if (tep_not_null($products_admin_tweet)) {
        $products_admin_tweet .= $resolver->resolveURL($catalog_raw_url)->getURL();
      } else {
        $products_admin_tweet = $resolver->resolveURL($catalog_raw_url)->getURL();
      }

      try {
        $tweet = $twitter->send($products_admin_tweet); // you can add $imagePath or array of image paths as second argument
        $messageStack->add_session("Product Tweeted.", 'success');
      } catch (TwitterException $e) {
        $messageStack->add_session("Error: " . $e->getMessage(), 'success');
      }

      tep_db_query("update " . TABLE_PRODUCTS . " set products_tweet_date = now() where products_id = '" . (int)$products_id . "'");


      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products_id));
      break;

    case 'insert_product':
    case 'update_product':
      if (isset($_POST['edit_x']) || isset($_POST['edit_y'])) {
        $action = 'new_product';
      } else {
        if (isset($_GET['pID'])) $products_id = tep_db_prepare_input($_GET['pID']);
        $products_date_available = tep_db_prepare_input($_POST['products_date_available']);


        $products_date_available = (date('Y-m-d') < $products_date_available) ? $products_date_available : 'null';


        // products_profit_margin is only calculated for the sorting feature on quick updates
        if (tep_not_null($_POST['products_price']) && $_POST['products_price'] != 0) {

          $sale_price_ex_vat = tep_db_prepare_input($_POST['products_price']) / 1.2;
          $trade_price_ex_vat = tep_db_prepare_input($_POST['products_trade_price']);
          $trade_price_discount = tep_db_prepare_input($_POST['products_trade_price_discount']);

          if ($trade_price_discount != 0) {
            $trade_price_discount_amount = $trade_price_ex_vat * ($trade_price_discount / 100);
            $trade_price_ex_vat_with_discount = $trade_price_ex_vat - $trade_price_discount_amount;
            $profit_in_sterling = $sale_price_ex_vat - $trade_price_ex_vat_with_discount;
          } else {
            $trade_price_ex_vat_with_discount = $trade_price_ex_vat;
            $profit_in_sterling = $sale_price_ex_vat - $trade_price_ex_vat;
          }

          if ($sale_price_ex_vat) {
            $profit_margin = ($profit_in_sterling / $sale_price_ex_vat) * 100;
          } else {
            $profit_margin = 0;
          }
        } else {
          $profit_margin = 0;
        }

        if (!tep_not_null($trade_price_ex_vat)) {
          $trade_price_ex_vat = 'null';
        }

        if (tep_not_null($_POST['products_event_date'])) {
          $products_event_date = tep_db_prepare_input($_POST['products_event_date']);
        } else {
          $products_event_date = 'null';
        }

        $sql_data_array = array(
          'products_quantity' => tep_db_prepare_input($_POST['products_quantity']),
          'products_model' => tep_db_prepare_input($_POST['products_model']),
          'products_mpn' => tep_db_prepare_input($_POST['products_mpn']),
          'products_price' => tep_db_prepare_input($_POST['products_price']),
          'products_trade_price' => $trade_price_ex_vat,
          'products_trade_price_discount' => tep_db_prepare_input($_POST['products_trade_price_discount']),
          'products_profit_margin' => number_format((float)$profit_margin, 2),
          'products_date_available' => $products_date_available,
          'products_weight' => tep_db_prepare_input($_POST['products_weight']),
          'products_cigar_ring_gauge' => tep_db_prepare_input($_POST['products_cigar_ring_gauge']),
          'products_cigar_length' => tep_db_prepare_input($_POST['products_cigar_length']),
          'products_cigar_smoke_time' => tep_db_prepare_input($_POST['products_cigar_smoke_time']),
          'products_max_order' => tep_db_prepare_input($_POST['products_max_order']),
          'products_status' => tep_db_prepare_input($_POST['products_status']),
          'products_tax_class_id' => tep_db_prepare_input($_POST['products_tax_class_id']),
          'products_geo_zones' => tep_db_prepare_input($_POST['products_geo_zones']),
          'manufacturers_id' => tep_db_prepare_input($_POST['manufacturers_id']),
          'suppliers_id' => tep_db_prepare_input($_POST['suppliers_id']),
          'products_bundle' => ($_POST['products_bundle'] == 'yes' ? 'yes' : 'no'),
          'sold_in_bundle_only' => ($_POST['sold_in_bundle_only'] == 'yes' ? 'yes' : 'no'),
          'products_free_shipping' => tep_db_prepare_input($_POST['products_free_shipping']),
          'products_order_type' => tep_db_prepare_input($_POST['products_order_type']),
          'products_cigar' => tep_db_prepare_input($_POST['products_cigar']),
          'products_earn_points' => tep_db_prepare_input($_POST['products_earn_points']),
          'products_vat_deductable' => tep_db_prepare_input($_POST['products_vat_deductable']),
          'products_paypal' => tep_db_prepare_input($_POST['products_paypal']),
          'hide_navigation' => tep_db_prepare_input($_POST['hide_navigation']),
          'hide_new_in' => tep_db_prepare_input($_POST['hide_new_in']),
          'products_event_date' => $products_event_date
        );

        if ($action == 'insert_product') {
          $insert_sql_data = array('products_date_added' => 'now()', 'products_created_date' => 'now()');

          $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

          tep_db_perform(TABLE_PRODUCTS, $sql_data_array);
          $products_id = tep_db_insert_id();

          tep_db_query("insert into " . TABLE_PRODUCTS_TO_CATEGORIES . " (products_id, categories_id) values ('" . (int)$products_id . "', '" . (int)$current_category_id . "')");
        } elseif ($action == 'update_product') {

          $update_sql_data = array('products_last_modified' => 'now()');

          $sql_data_array = array_merge($sql_data_array, $update_sql_data);

          tep_db_perform(TABLE_PRODUCTS, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "'");

          // BOF Bundled Products
          if ($_POST['products_bundle'] == "yes") {
            $to_avoid = tep_bundle_avoid($products_id);
            $subprods = array();
            $subprodqty = array();
            tep_db_query("DELETE FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE bundle_id = '" . (int)$products_id . "'");
            for ($i = 0, $n = 100; $i < $n; $i++) {
              if (isset($_POST['subproduct_' . $i . '_qty']) && ((int)$_POST['subproduct_' . $i . '_qty'] > 0) && !in_array($_POST['subproduct_' . $i . '_id'], $to_avoid)) {
                if (in_array($_POST['subproduct_' . $i . '_id'], $subprods)) {
                  $subprodqty[$_POST['subproduct_' . $i . '_id']] += (int)$_POST['subproduct_' . $i . '_qty'];
                  tep_db_query('update ' . TABLE_PRODUCTS_BUNDLES . ' set subproduct_qty = ' . (int)$subprodqty[$_POST['subproduct_' . $i . '_id']] . ' where bundle_id = ' . (int)$products_id . ' and subproduct_id = ' . (int)$_POST['subproduct_' . $i . '_id']);
                } else {
                  $subprods[] = $_POST['subproduct_' . $i . '_id'];
                  $subprodqty[$_POST['subproduct_' . $i . '_id']] = (int)$_POST['subproduct_' . $i . '_qty'];
                  tep_db_query("INSERT INTO " . TABLE_PRODUCTS_BUNDLES . " (bundle_id, subproduct_id, subproduct_qty, subproduct_type) VALUES ('" . (int)$products_id . "', '" . (int)$_POST['subproduct_' . $i . '_id'] . "', '" . (int)$_POST['subproduct_' . $i . '_qty'] . "', '" . (int)$_POST['subproduct_' . $i . '_type'] . "')");
                }
              }
            }
            if (empty($subprods)) { // not a bundle if no subproducts set
              tep_db_query('update ' . TABLE_PRODUCTS . ' set products_bundle = "no" where products_id = ' . (int)$products_id);
            }
          } elseif ($_POST['products_bundle'] == "no" && $_POST['old_products_bundle'] == "yes") {
            tep_db_query("DELETE FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE bundle_id = '" . (int)$products_id . "'");
          }
          // EOF Bundled Products

          // has the trade price or trade price discount changed?
          //   	$trade_price_ex_vat = tep_db_prepare_input();
          //   	$trade_price_discount = tep_db_prepare_input();

          // if one of these has changed...
          if (($_POST['products_trade_price'] != $_POST['products_trade_price_old']) || ($_POST['products_trade_price_discount'] != $_POST['products_trade_price_discount_old'])) {
            // check if this product is part of a bundle and update that bundle's cost price
            tep_check_bundle_cost_price((int)$products_id);
          }

          // override status if stock is set to zero
          if ($_POST['products_quantity'] == '0' && ($_POST['products_status'] != '2' or $_POST['products_status'] != '4')) {
            tep_set_product_status($products_id, 2); // set the status to out of stock
          }
        } // end if update product

        // BOF: More Pics 6
        //end delete image function
        if ($_POST['delete_image_14'] == 'yes') {
          //unlink(DIR_FS_CATALOG_IMAGES . $_POST['products_subimage7']);
          $sql_data_array['products_subimage7'] = tep_db_prepare_input($_POST['none']);
        }
        //end delete image function
        // EOF: More Pics 6

        $pi_sort_order = 0;
        $piArray = array(0);

        foreach ($_FILES as $key => $value) {

          if (!$main_image_set && $key != 'wm_products_image') {
            $main_image_set = true;

            $t = new upload($key);
            $t->set_destination(DIR_FS_CATALOG_IMAGES);
            if ($t->parse()) {
              $t->rename_with_product_id($products_id);
              if ($t->save()) {
                $new_product_image = tep_db_prepare_input($t->filename);
              }
            } elseif (preg_match('/^products_image_large_([0-9]+)$/', $key, $matches)) {
              $new_product_image = tep_db_prepare_input($_POST['old_products_image_large_' . $matches[1]]);
            } else {
              $new_product_image = $_POST['old_products_image'];
            }

            tep_db_query("update " . TABLE_PRODUCTS . " set products_image = '" . $new_product_image . "' where products_id = " . (int)$products_id);
          } elseif (preg_match('/^products_image_large_([0-9]+)$/', $key, $matches)) {
            // Update existing large product images
            $pi_sort_order++;

            $image_sql_data_array = array(
              'sort_order' => $pi_sort_order,
              'type' => tep_db_prepare_input($_POST['products_image_type_' . $matches[1]])
            );

            $t = new upload($key);
            $t->set_destination(DIR_FS_CATALOG_IMAGES);
            if ($t->parse()) {
              $t->rename_with_product_id($products_id);
              if ($t->save()) {
                $image_sql_data_array['image'] = tep_db_prepare_input($t->filename);
              }
            }

            tep_db_perform(TABLE_PRODUCTS_IMAGES, $image_sql_data_array, 'update', "products_id = '" . (int)$products_id . "' and id = '" . (int)$matches[1] . "'");

            $piArray[] = (int)$matches[1];
          } elseif (preg_match('/^products_image_large_new_([0-9]+)$/', $key, $matches) || $key == 'products_image') {
            // Insert new large product images
            $image_sql_data_array = array('products_id' => (int)$products_id);

            if ($key == 'products_image') {
              $type_value = tep_db_prepare_input($_POST['products_image_type'] ?? '');
            } else {
              $type_value = tep_db_prepare_input($_POST['products_image_type_new_' . $matches[1]]);
            }
            $image_sql_data_array['type'] = $type_value;

            $t = new upload($key);
            $t->set_destination(DIR_FS_CATALOG_IMAGES);
            if ($t->parse()) {
              $t->rename_with_product_id($products_id);

              if ($t->save()) {
                $pi_sort_order++;

                $image_sql_data_array['image'] = tep_db_prepare_input($t->filename);
                $image_sql_data_array['sort_order'] = $pi_sort_order;

                tep_db_perform(TABLE_PRODUCTS_IMAGES, $image_sql_data_array);

                $piArray[] = tep_db_insert_id();
              }
            } elseif ($key == 'products_image') {
              $pi_sort_order++;

              $image_sql_data_array['image'] = tep_db_prepare_input($_POST['old_products_image']);
              $image_sql_data_array['sort_order'] = $pi_sort_order;

              tep_db_perform(TABLE_PRODUCTS_IMAGES, $image_sql_data_array);

              $piArray[] = tep_db_insert_id();
            }
          }
        }

        if (!$main_image_set) {
          if (tep_not_null($_POST['old_products_image'])) {
            tep_image_check_delete($_POST['old_products_image']);
          }
          tep_db_query("update " . TABLE_PRODUCTS . " set products_image = '' where products_id = " . (int)$products_id);
        }

        $product_images_query = tep_db_query("select image from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int)$products_id . "' and id not in (" . implode(',', $piArray) . ")");
        if (tep_db_num_rows($product_images_query)) {
          while ($product_images = tep_db_fetch_array($product_images_query)) {
            tep_image_check_delete($product_images['image']);
          }
          tep_db_query("delete from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int)$products_id . "' and id not in (" . implode(',', $piArray) . ")");
        }

        // for update & insert product..
        if ($action == 'insert_product') {
          $log_text = $admin['username'] . ' added product.';
        } else {
          $log_text = $admin['username'] . ' modified product.';
        }

        if ($_POST['products_paypal'] != $_POST['old_products_paypal']) {
          if ($_POST['products_paypal'] == '1') {
            $log_text .= ' PP set to: On.';
          } else {
            $log_text .= ' PP set to: Off.';
          }
        }

        // check if the products_quantity has changed
        if ($_POST['products_quantity'] != $_POST['old_products_quantity']) {

          if ($_POST['products_quantity'] < $_POST['old_products_quantity']) {
            $quantity_message = '<div class="messageStackQuantityDecrease">Total quantity <strong>decreased</strong> to: <span>' . $_POST['products_quantity'] . '</span></div>';
          } else {
            $quantity_message = '<div class="messageStackQuantityIncrease">Total quantity <strong>increased</strong> to: <span>' . $_POST['products_quantity'] . '</span></div>';
          }

          if ($_POST['stock_liverpool'] > 0) {
            $quantity_message .= 'Liverpool: ' . $_POST['stock_liverpool'] . '<br />';
          }
          if ($_POST['stock_liverpool_stashed'] > 0) {
            $quantity_message .= 'Liverpool Stashed: ' . $_POST['stock_liverpool_stashed'] . '<br />';
          }
          if ($_POST['stock_norfolk_stashed'] > 0) {
            $quantity_message .= 'Norfolk Stashed: ' . $_POST['stock_norfolk_stashed'] . '<br />';
          }
          if ($_POST['stock_chester'] > 0) {
            $quantity_message .= 'Chester: ' . $_POST['stock_chester'] . '<br />';
          }
          if ($_POST['stock_chester_stashed'] > 0) {
            $quantity_message .= 'Chester Stashed: ' . $_POST['stock_chester_stashed'] . '<br />';
          }
          if ($_POST['stock_leeds'] > 0) {
            $quantity_message .= 'Leeds: ' . $_POST['stock_leeds'] . '<br />';
          }
          if ($_POST['stock_leeds_stashed'] > 0) {
            $quantity_message .= 'Leeds Stashed: ' . $_POST['stock_leeds_stashed'] . '<br />';
          }
          if ($_POST['stock_mashtun'] > 0) {
            $quantity_message .= 'Mash Tun: ' . $_POST['stock_mashtun'] . '<br />';
          }
          if ($_POST['stock_mashtun_stashed'] > 0) {
            $quantity_message .= 'MT Stashed: ' . $_POST['stock_mashtun_stashed'] . '<br />';
          }
          if ($_POST['stock_lime_street'] > 0) {
            $quantity_message .= 'Lime Street: ' . $_POST['stock_lime_street'] . '<br />';
          }
          if ($_POST['stock_lime_street_stashed'] > 0) {
            $quantity_message .= 'Lime Street Stashed: ' . $_POST['stock_lime_street_stashed'] . '<br />';
          }
          if ($_POST['stock_edinburgh'] > 0) {
            $quantity_message .= 'Edinburgh: ' . $_POST['stock_edinburgh'] . '<br />';
          }
          if ($_POST['stock_edinburgh_stashed'] > 0) {
            $quantity_message .= 'Edinburgh Stashed: ' . $_POST['stock_edinburgh_stashed'] . '<br />';
          }
          if ($_POST['stock_london'] > 0) {
            $quantity_message .= 'London: ' . $_POST['stock_london'] . '<br />';
          }
          if ($_POST['stock_london_cg'] > 0) {
            $quantity_message .= 'St. James: ' . $_POST['stock_london_cg'] . '<br />';
          }
          if ($_POST['stock_mayfair'] > 0) {
            $quantity_message .= 'Mayfair: ' . $_POST['stock_mayfair'] . '<br />';
          }
          if ($_POST['stock_norfolk'] > 0) {
            $quantity_message .= 'Norfolk: ' . $_POST['stock_norfolk'] . '<br />';
          }
          if ($_POST['stock_knutsford'] > 0) {
            $quantity_message .= 'Knutsford: ' . $_POST['stock_knutsford'] . '<br />';
          }

          $products_quantity_has_changed = true;
          $log_text .= ' Qty set to: ' . $_POST['products_quantity'];

          $messageStack->add_session($quantity_message, 'quantity');
        }

        tep_log_this($log_text, (int)$products_id);

        $log_price_new = tep_db_prepare_input($_POST['products_price']);
        $log_price_old = tep_db_prepare_input($_POST['products_price_old']);
        if ($log_price_new != $log_price_old) {
          tep_log_price($log_price_new, $log_price_old, (int)$products_id);
        }

        if ($action == 'insert_product') {
          // add product to any Coupons that have product restrictions
          $restriction_query = tep_db_query("select coupons_id from " . TABLE_DISCOUNT_COUPONS_TO_PRODUCTS . " group by coupons_id");

          while ($restriction = tep_db_fetch_array($restriction_query)) {
            tep_db_query("insert into " . TABLE_DISCOUNT_COUPONS_TO_PRODUCTS . " (coupons_id, products_id) values ('" . $restriction['coupons_id'] . "', '" . (int)$products_id . "')");
          }
        }

        /** AJAX Attribute Manager  **/
        require_once('attributeManager/includes/attributeManagerUpdateAtomic.inc.php');
        /** AJAX Attribute Manager  end **/

        // TOBACCO ATTRIBUTES START

        if (tep_not_null($_POST['product_type'])) {

          $selected_product_type = $_POST['product_type'];

          // check for existing tobacco slider option values
          $products_options_query = tep_db_query("select pa.options_id, po.products_options_type from " . TABLE_PRODUCTS_ATTRIBUTES . " pa left join " . TABLE_PRODUCTS_OPTIONS . " po on pa.options_id = po.products_options_id where pa.products_id = '" . (int)$products_id . "'");

          $current_product_type = 1; // normal

          while ($products_options = tep_db_fetch_array($products_options_query)) {
            if ($products_options['products_options_type'] == '5') { // slider
              $current_product_type = $products_options['options_id']; // slider option type
            }
          }

          // $messageStack->add_session('Current: ' . $current_product_type . ' Selected: ' . $selected_product_type, 'error');

          // DELETE ANY EXISTING UNWANTED TOBACCO SLIDER ATTRIBUTES
          if (($selected_product_type != $current_product_type) && ($current_product_type > 1)) {
            // tep_db_query("delete from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int)$products_id . "' and options_id = '" . $current_product_type . "'"); // delete only slider attributes
            tep_db_query("delete from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int)$products_id . "'"); // delete all attributes for that product
            tep_db_query("delete from " . TABLE_PRODUCTS_TOBACCO . " where products_id = '" . (int)$products_id . "'");
          }

          // A TOBACCO SLIDER ATTRIBUTE IS SELECTED
          if ($selected_product_type > 1) {

            // GET ALL THE OPTION VALUES FOR THE SELECTED PRODUCT TYPE
            $product_option_values_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_sortorder, m.products_options_values_profit_margin, m.products_options_values_default from " . TABLE_PRODUCTS_OPTIONS_VALUES_TO_PRODUCTS_OPTIONS . " povtpo, " . TABLE_PRODUCTS_OPTIONS_VALUES . " pov, " . TABLE_PRODUCTS_OPTIONS_VALUES_MARGINS . " m where povtpo.products_options_id = '" . $selected_product_type . "' and povtpo.products_options_values_id = pov.products_options_values_id and povtpo.products_options_values_id = m.products_options_values_id order by pov.products_options_values_sortorder asc");

            $base_price = tep_db_prepare_input($_POST['products_price']);
            $base_trade_price = tep_db_prepare_input($_POST['products_trade_price']);
            $one_gram_trade = tep_db_prepare_input($_POST['products_price_per_gram']);

            while ($product_option_values = tep_db_fetch_array($product_option_values_query)) {

              if ((!$base_trade_grams) || ($product_option_values['products_options_values_default'] == '1')) {
                $base_trade_grams = $product_option_values['products_options_values_sortorder'];
                $base_trade_margin = $product_option_values['products_options_values_profit_margin'];
              }

              if (!is_numeric($one_gram_trade)) {
                $one_gram_trade = 0;
              }

              // 2) Get desired prices
              $desired_trade_price_ex_vat = ($one_gram_trade * $product_option_values['products_options_values_sortorder']);

              if ($product_option_values['products_options_values_sortorder'] == '10') {
                $desired_price_inc_vat = '2.99'; // force sample price
              } else {
                $desired_price_inc_vat = (((($product_option_values['products_options_values_profit_margin'] / 100) * $desired_trade_price_ex_vat) + $desired_trade_price_ex_vat) * 1.2);
              }

              if (number_format((float)$desired_trade_price_ex_vat, 2, '.', '') >= number_format((float)$base_trade_price, 2, '.', '')) {
                $final_trade_price_ex_vat = ($desired_trade_price_ex_vat - $base_trade_price);
                $final_trade_price_ex_vat_symbol = '+';
              } else {
                $final_trade_price_ex_vat = ($base_trade_price - $desired_trade_price_ex_vat);
                $final_trade_price_ex_vat_symbol = '-';
              }

              if (number_format((float)$desired_price_inc_vat, 2, '.', '') >= number_format((float)$base_price, 2, '.', '')) {
                $final_price_inc_vat = ($desired_price_inc_vat - $base_price);
                $final_price_inc_vat_symbol = '+';
              } else {
                $final_price_inc_vat = ($base_price - $desired_price_inc_vat);
                $final_price_inc_vat_symbol = '-';
              }

              // ADD OR UPDATE?
              $attributes_query = tep_db_query("select products_attributes_id from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int)$products_id . "' and options_values_id = '" . $product_option_values['products_options_values_id'] . "'");

              if (tep_db_num_rows($attributes_query)) { // UPDATE

                //tep_db_query("update " . TABLE_PRODUCTS_ATTRIBUTES . " set options_values_price = '" . tep_db_input($final_price_inc_vat) . "', options_values_price_cost = '" . tep_db_input($final_trade_price_ex_vat) . "', price_prefix = '+', price_cost_prefix = '+' where products_id = '" . (int)$products_id . "' and options_values_id = '" . $product_option_values['products_options_values_id'] . "' ");

                $update_not_add_to_tobacco_table = true;
              } else { // ADD NEW

                tep_db_query("insert into " . TABLE_PRODUCTS_ATTRIBUTES . " (products_id, options_id, options_values_id, options_values_price, price_prefix, options_values_price_cost, price_cost_prefix) values ('" . (int)$products_id . "', '" . tep_db_input($selected_product_type) . "', '" . tep_db_input($product_option_values['products_options_values_id']) . "', '" . tep_db_input($final_price_inc_vat) . "', '" . $final_price_inc_vat_symbol . "', '" . tep_db_input($final_trade_price_ex_vat) . "', '" . $final_trade_price_ex_vat_symbol . "')");
              }
            }

            if ($update_not_add_to_tobacco_table) {
              //tep_db_query("update " . TABLE_PRODUCTS_TOBACCO . " set products_options_id = '" . tep_db_input($selected_product_type) . "', base_grams = '" . tep_db_input($base_trade_grams) . "', base_margin = '" . tep_db_input($base_trade_margin) . "', price_per_gram = '" . tep_db_input($one_gram_trade) . "' where products_id = '" . (int)$products_id . "'");
            } else {
              tep_db_query("insert into " . TABLE_PRODUCTS_TOBACCO . " (products_id, products_options_id, base_grams, base_margin, price_per_gram) values ('" . (int)$products_id . "', '" . tep_db_input($selected_product_type) . "', '" . tep_db_input($base_trade_grams) . "', '" . tep_db_input($base_trade_margin) . "', '" . tep_db_input($one_gram_trade) . "')");
            }
          }
        }

        // TOBACCO ATTRIBUTES END

        // WE START FOR PROD DESCRIPTION

        $languages = tep_get_languages();
        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
          $language_id = $languages[$i]['id'];

          //HTC BOC
          $sql_data_array = array(
            'products_name' => tep_db_prepare_input($_POST['products_name'][$language_id]),
            'products_description' => tep_db_prepare_input($_POST['products_description'][$language_id]),
            'products_read_more' => tep_db_prepare_input($_POST['products_read_more'][$language_id]),
            'products_tasting_notes' => tep_db_prepare_input($_POST['products_tasting_notes'][$language_id]),
            'products_taste_test' => tep_db_prepare_input($_POST['products_taste_test'][$language_id]),
            'products_awards' => tep_db_prepare_input($_POST['products_awards'][$language_id]),
            'products_url' => tep_db_prepare_input($_POST['products_url'][$language_id]),
            'products_head_title_tag' => ((tep_not_null($_POST['products_head_title_tag'][$language_id])) ? tep_db_prepare_input($_POST['products_head_title_tag'][$language_id]) : tep_db_prepare_input($_POST['products_name'][$language_id])),
            'products_head_desc_tag' => tep_db_prepare_input($_POST['products_head_desc_tag'][$language_id]),
            'products_head_keywords_tag' => tep_db_prepare_input($_POST['products_head_keywords_tag'][$language_id])
          );
          //HTC EOC

          if ($action == 'insert_product') {
            $insert_sql_data = array(
              'products_id' => $products_id,
              'language_id' => $language_id
            );

            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

            tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array);
          } elseif ($action == 'update_product') {
            tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "' and language_id = '" . (int)$language_id . "'");
          }
        }

        // START FOR HO EXTRA
        // START FOR ADDITIONAL INFO

        $check_query = tep_db_query("select ai_products_id from " . TABLE_ADDITIONAL_INFO_CIGAR . " where ai_products_id = '" . (int)$products_id . "'");
        if (tep_db_num_rows($check_query)) {
          $row_exists = 1;
        } else {
          $row_exists = 0;
        }

        $sql_data_array = array();
        $value_exists = false;

        $option_name_query = tep_db_query("select ai_column, ai_options_type from " . TABLE_ADDITIONAL_INFO_OPTIONS . " where ai_options_group = '1'"); // 1 = TABLE_ADDITIONAL_INFO_CIGAR
        while ($option_name = tep_db_fetch_array($option_name_query)) {

          if (tep_not_null($_POST[$option_name["ai_column"]]) && ($_POST[$option_name["ai_column"]] != '0')) {
            $value_submitted = tep_db_prepare_input($_POST[$option_name["ai_column"]]);
            // check input is appropriate
            $value_submitted = tep_check_additional_info_input($value_submitted, $option_name['ai_options_type']);
            $value_exists = true;
          } else {
            $value_submitted = 'null';
          }

          $sql_data_array[$option_name['ai_column']] = $value_submitted;
        }

        if ($row_exists == 0 && $value_exists) {
          $insert_sql_data = array('ai_products_id' => $products_id);
          $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
          tep_db_perform(TABLE_ADDITIONAL_INFO_CIGAR, $sql_data_array);
        } elseif ($row_exists == 1) {
          tep_db_perform(TABLE_ADDITIONAL_INFO_CIGAR, $sql_data_array, 'update', "ai_products_id = '" . (int)$products_id . "'");
        }



        $check_query = tep_db_query("select ai_products_id from " . TABLE_ADDITIONAL_INFO_WHISKY . " where ai_products_id = '" . (int)$products_id . "'");
        if (tep_db_num_rows($check_query)) {
          $row_exists = 1;
        } else {
          $row_exists = 0;
        }

        $sql_data_array = array();
        $value_exists = false;

        $option_name_query = tep_db_query("select ai_column, ai_options_type from " . TABLE_ADDITIONAL_INFO_OPTIONS . " where ai_options_group = '2'"); // 2 = TABLE_ADDITIONAL_INFO_WHISKY
        while ($option_name = tep_db_fetch_array($option_name_query)) {

          if (tep_not_null($_POST[$option_name["ai_column"]]) && ($_POST[$option_name["ai_column"]] != '0')) {
            $value_submitted = tep_db_prepare_input($_POST[$option_name["ai_column"]]);
            // check input is appropriate
            $value_submitted = tep_check_additional_info_input($value_submitted, $option_name['ai_options_type']);
            $value_exists = true;
          } else {
            $value_submitted = 'null';
          }

          $sql_data_array[$option_name['ai_column']] = $value_submitted;
        }

        if ($row_exists == 0 && $value_exists) {
          $insert_sql_data = array('ai_products_id' => $products_id);
          $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
          tep_db_perform(TABLE_ADDITIONAL_INFO_WHISKY, $sql_data_array);
        } elseif ($row_exists == 1) {
          tep_db_perform(TABLE_ADDITIONAL_INFO_WHISKY, $sql_data_array, 'update', "ai_products_id = '" . (int)$products_id . "'");
        }

        // END FOR ADDITIONAL INFO
        // CHECK FOR EXISTING DATA

        $check_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$products_id . "' and store_id = '2'");

        if (tep_db_num_rows($check_query)) {
          $check_exists = 1;
        } else {
          $check_exists = 0;
        }

        if (tep_not_null($_POST['ho_products_name']) ||  tep_not_null($_POST['ho_products_description']) ||  tep_not_null($_POST['ho_products_head_title_tag']) ||  tep_not_null($_POST['ho_products_head_desc_tag']) ||  tep_not_null($_POST['ho_products_head_keywords_tag']) ||  tep_not_null($_POST['ho_products_price']) || $check_exists == 1) {

          $ho_products_name = tep_db_prepare_input($_POST['ho_products_name']);

          if (tep_not_null($_POST['ho_products_description'])) {
            $ho_products_description = tep_db_prepare_input($_POST['ho_products_description']);
          } else {
            $ho_products_description = 'null';
          }

          if (tep_not_null($_POST['ho_products_head_title_tag'])) {
            $ho_products_head_title_tag = tep_db_prepare_input($_POST['ho_products_head_title_tag']);
          } else {
            $ho_products_head_title_tag = 'null';
          }

          if (tep_not_null($_POST['ho_products_head_desc_tag'])) {
            $ho_products_head_desc_tag = tep_db_prepare_input($_POST['ho_products_head_desc_tag']);
          } else {
            $ho_products_head_desc_tag = 'null';
          }

          if (tep_not_null($_POST['ho_products_head_keywords_tag'])) {
            $ho_products_head_keywords_tag = tep_db_prepare_input($_POST['ho_products_head_keywords_tag']);
          } else {
            $ho_products_head_keywords_tag = 'null';
          }

          if (tep_not_null($_POST['ho_products_price'])) {
            $ho_products_price = tep_db_prepare_input($_POST['ho_products_price']);
          } else {
            $ho_products_price = 'null';
          }

          $ho_products_tax_class_id = tep_db_prepare_input($_POST['ho_products_tax_class_id']);



          $sql_data_array = array(
            'store_id' => '2',
            'products_name' => $ho_products_name,
            'products_head_title_tag' => $ho_products_head_title_tag,
            'products_head_desc_tag' => $ho_products_head_desc_tag,
            'products_head_keywords_tag' => $ho_products_head_keywords_tag,
            'products_description' => $ho_products_description,
            'products_price' => $ho_products_price,
            'products_tax_class_id' => $ho_products_tax_class_id
          );

          if ($action == 'insert_product' || $check_exists == 0) {
            $insert_sql_data = array('products_id' => $products_id);

            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array);
          } elseif ($action == 'update_product') {
            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "' and store_id = '2'");
          }
        }

        // FINISH FOR HO EXTRA

        // START FOR HS EXTRA

        // CHECK FOR EXISTING DATA

        $check_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$products_id . "' and store_id = '3'");

        if (tep_db_num_rows($check_query)) {
          $check_exists = 1;
        } else {
          $check_exists = 0;
        }

        if (tep_not_null($_POST['hs_products_name']) ||  tep_not_null($_POST['hs_products_description']) ||  tep_not_null($_POST['hs_products_head_title_tag']) ||  tep_not_null($_POST['hs_products_head_desc_tag']) ||  tep_not_null($_POST['hs_products_head_keywords_tag']) ||  tep_not_null($_POST['hs_products_price']) || $check_exists == 1) {

          $hs_products_name = tep_db_prepare_input($_POST['hs_products_name']);

          if (tep_not_null($_POST['hs_products_description'])) {
            $hs_products_description = tep_db_prepare_input($_POST['hs_products_description']);
          } else {
            $hs_products_description = 'null';
          }

          if (tep_not_null($_POST['hs_products_head_title_tag'])) {
            $hs_products_head_title_tag = tep_db_prepare_input($_POST['hs_products_head_title_tag']);
          } else {
            $hs_products_head_title_tag = 'null';
          }

          if (tep_not_null($_POST['hs_products_head_desc_tag'])) {
            $hs_products_head_desc_tag = tep_db_prepare_input($_POST['hs_products_head_desc_tag']);
          } else {
            $hs_products_head_desc_tag = 'null';
          }

          if (tep_not_null($_POST['hs_products_head_keywords_tag'])) {
            $hs_products_head_keywords_tag = tep_db_prepare_input($_POST['hs_products_head_keywords_tag']);
          } else {
            $hs_products_head_keywords_tag = 'null';
          }

          if (tep_not_null($_POST['hs_products_price'])) {
            $hs_products_price = tep_db_prepare_input($_POST['hs_products_price']);
          } else {
            $hs_products_price = 'null';
          }

          $hs_products_tax_class_id = tep_db_prepare_input($_POST['hs_products_tax_class_id']);

          $sql_data_array = array(
            'store_id' => '3',
            'products_name' => $hs_products_name,
            'products_head_title_tag' => $hs_products_head_title_tag,
            'products_head_desc_tag' => $hs_products_head_desc_tag,
            'products_head_keywords_tag' => $hs_products_head_keywords_tag,
            'products_description' => $hs_products_description,
            'products_price' => $hs_products_price,
            'products_tax_class_id' => $hs_products_tax_class_id
          );

          if ($action == 'insert_product' || $check_exists == 0) {
            $insert_sql_data = array('products_id' => $products_id);

            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array);
          } elseif ($action == 'update_product') {
            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "' and store_id = '3'");
          }
        }

        // FINISH FOR HS EXTRA

        // START FOR CU EXTRA

        // CHECK FOR EXISTING DATA

        $check_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$products_id . "' and store_id = '6'");

        if (tep_db_num_rows($check_query)) {
          $check_exists = 1;
        } else {
          $check_exists = 0;
        }

        if (tep_not_null($_POST['wm_products_name']) ||  tep_not_null($_POST['wm_products_description']) ||  tep_not_null($_POST['wm_products_head_title_tag']) ||  tep_not_null($_POST['wm_products_head_desc_tag']) ||  tep_not_null($_POST['wm_products_head_keywords_tag']) ||  tep_not_null($_POST['wm_products_price']) ||  tep_not_null($_FILES['wm_products_image']['name']) || $check_exists == 1) {

          $wm_products_name = tep_db_prepare_input($_POST['wm_products_name']);

          if (tep_not_null($_POST['wm_products_description'])) {
            $wm_products_description = tep_db_prepare_input($_POST['wm_products_description']);
          } else {
            $wm_products_description = 'null';
          }

          if (tep_not_null($_POST['wm_products_head_title_tag'])) {
            $wm_products_head_title_tag = tep_db_prepare_input($_POST['wm_products_head_title_tag']);
          } else {
            $wm_products_head_title_tag = 'null';
          }

          if (tep_not_null($_POST['wm_products_head_desc_tag'])) {
            $wm_products_head_desc_tag = tep_db_prepare_input($_POST['wm_products_head_desc_tag']);
          } else {
            $wm_products_head_desc_tag = 'null';
          }

          if (tep_not_null($_POST['wm_products_head_keywords_tag'])) {
            $wm_products_head_keywords_tag = tep_db_prepare_input($_POST['wm_products_head_keywords_tag']);
          } else {
            $wm_products_head_keywords_tag = 'null';
          }

          if (tep_not_null($_POST['wm_products_price'])) {
            $wm_products_price = tep_db_prepare_input($_POST['wm_products_price']);
          } else {
            $wm_products_price = 'null';
          }

          $wm_products_tax_class_id = tep_db_prepare_input($_POST['wm_products_tax_class_id']);

          $sql_data_array = array(
            'store_id' => '6',
            'products_name' => $wm_products_name,
            'products_head_title_tag' => $wm_products_head_title_tag,
            'products_head_desc_tag' => $wm_products_head_desc_tag,
            'products_head_keywords_tag' => $wm_products_head_keywords_tag,
            'products_description' => $wm_products_description,
            'products_price' => $wm_products_price,
            'products_tax_class_id' => $wm_products_tax_class_id
          );

          if (isset($_FILES['wm_products_image']['name']) && !empty($_FILES['wm_products_image']['name'])) {
            $wm_products_image = new upload('wm_products_image');
            $wm_products_image->set_destination(DIR_FS_CATALOG_IMAGES . 'wm/');
            if ($wm_products_image->parse() && $wm_products_image->save()) {
              $sql_data_array = array_merge($sql_data_array, array('products_image' => $wm_products_image->filename));
            }
          } elseif ($_POST['wm_delete_image_1'] == 'yes') { // add delete image function
            $sql_data_array = array_merge($sql_data_array, array('products_image' => 'null'));
          }

          if ($action == 'insert_product' || $check_exists == 0) {

            $insert_sql_data = array('products_id' => $products_id);
            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array);
          } elseif ($action == 'update_product') {

            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "' and store_id = '6'");
          }
        }




        // FINISH FOR CU EXTRA

        // START FOR DC EXTRA

        // CHECK FOR EXISTING DATA

        $check_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$products_id . "' and store_id = '7'");

        if (tep_db_num_rows($check_query)) {
          $check_exists = 1;
        } else {
          $check_exists = 0;
        }

        if (tep_not_null($_POST['af_products_name']) ||  tep_not_null($_POST['af_products_description']) ||  tep_not_null($_POST['af_products_head_title_tag']) ||  tep_not_null($_POST['af_products_head_desc_tag']) ||  tep_not_null($_POST['af_products_head_keywords_tag']) ||  tep_not_null($_POST['af_products_price']) || $check_exists == 1) {

          $af_products_name = tep_db_prepare_input($_POST['af_products_name']);

          if (tep_not_null($_POST['af_products_description'])) {
            $af_products_description = tep_db_prepare_input($_POST['af_products_description']);
          } else {
            $af_products_description = 'null';
          }

          if (tep_not_null($_POST['af_products_head_title_tag'])) {
            $af_products_head_title_tag = tep_db_prepare_input($_POST['af_products_head_title_tag']);
          } else {
            $af_products_head_title_tag = 'null';
          }

          if (tep_not_null($_POST['af_products_head_desc_tag'])) {
            $af_products_head_desc_tag = tep_db_prepare_input($_POST['af_products_head_desc_tag']);
          } else {
            $af_products_head_desc_tag = 'null';
          }

          if (tep_not_null($_POST['af_products_head_keywords_tag'])) {
            $af_products_head_keywords_tag = tep_db_prepare_input($_POST['af_products_head_keywords_tag']);
          } else {
            $af_products_head_keywords_tag = 'null';
          }

          if (tep_not_null($_POST['af_products_price'])) {
            $af_products_price = tep_db_prepare_input($_POST['af_products_price']);
          } else {
            $af_products_price = 'null';
          }

          $af_products_tax_class_id = tep_db_prepare_input($_POST['af_products_tax_class_id']);

          $sql_data_array = array(
            'store_id' => '7',
            'products_name' => $af_products_name,
            'products_head_title_tag' => $af_products_head_title_tag,
            'products_head_desc_tag' => $af_products_head_desc_tag,
            'products_head_keywords_tag' => $af_products_head_keywords_tag,
            'products_description' => $af_products_description,
            'products_price' => $af_products_price,
            'products_tax_class_id' => $af_products_tax_class_id
          );

          if ($action == 'insert_product' || $check_exists == 0) {
            $insert_sql_data = array('products_id' => $products_id);

            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array);
          } elseif ($action == 'update_product') {
            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "' and store_id = '7'");
          }
        }

        // FINISH FOR DC EXTRA

        // START FOR TT EXTRA

        // CHECK FOR EXISTING DATA

        $check_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$products_id . "' and store_id = '8'");

        if (tep_db_num_rows($check_query)) {
          $check_exists = 1;
        } else {
          $check_exists = 0;
        }

        if (tep_not_null($_POST['tt_products_name']) ||  tep_not_null($_POST['tt_products_description']) ||  tep_not_null($_POST['tt_products_head_title_tag']) ||  tep_not_null($_POST['tt_products_head_desc_tag']) ||  tep_not_null($_POST['tt_products_head_keywords_tag']) ||  tep_not_null($_POST['tt_products_price']) || $check_exists == 1) {

          $tt_products_name = tep_db_prepare_input($_POST['tt_products_name']);

          if (tep_not_null($_POST['tt_products_description'])) {
            $tt_products_description = tep_db_prepare_input($_POST['tt_products_description']);
          } else {
            $tt_products_description = 'null';
          }

          if (tep_not_null($_POST['tt_products_head_title_tag'])) {
            $tt_products_head_title_tag = tep_db_prepare_input($_POST['tt_products_head_title_tag']);
          } else {
            $tt_products_head_title_tag = 'null';
          }

          if (tep_not_null($_POST['tt_products_head_desc_tag'])) {
            $tt_products_head_desc_tag = tep_db_prepare_input($_POST['tt_products_head_desc_tag']);
          } else {
            $tt_products_head_desc_tag = 'null';
          }

          if (tep_not_null($_POST['tt_products_head_keywords_tag'])) {
            $tt_products_head_keywords_tag = tep_db_prepare_input($_POST['tt_products_head_keywords_tag']);
          } else {
            $tt_products_head_keywords_tag = 'null';
          }

          if (tep_not_null($_POST['tt_products_price'])) {
            $tt_products_price = tep_db_prepare_input($_POST['tt_products_price']);
          } else {
            $tt_products_price = 'null';
          }

          $tt_products_tax_class_id = tep_db_prepare_input($_POST['tt_products_tax_class_id']);

          $sql_data_array = array(
            'store_id' => '8',
            'products_name' => $tt_products_name,
            'products_head_title_tag' => $tt_products_head_title_tag,
            'products_head_desc_tag' => $tt_products_head_desc_tag,
            'products_head_keywords_tag' => $tt_products_head_keywords_tag,
            'products_description' => $tt_products_description,
            'products_price' => $tt_products_price,
            'products_tax_class_id' => $tt_products_tax_class_id
          );

          if ($action == 'insert_product' || $check_exists == 0) {
            $insert_sql_data = array('products_id' => $products_id);

            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array);
          } elseif ($action == 'update_product') {
            tep_db_perform(TABLE_PRODUCTS_STORES_EXTRA, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "' and store_id = '8'");
          }
        }

        // FINISH FOR TT EXTRA

        // START FOR PRODUCTS_TO_STORES

        if (tep_not_null($_POST['store_cg'])) {
          $prod_store_cg = $_POST['store_cg'];
        } else {
          $prod_store_cg = '0';
        }
        if (tep_not_null($_POST['store_ho'])) {
          $prod_store_ho = $_POST['store_ho'];
        } else {
          $prod_store_ho = '0';
        }
        if (tep_not_null($_POST['store_hs'])) {
          $prod_store_hs = $_POST['store_hs'];
        } else {
          $prod_store_hs = '0';
        }
        if (tep_not_null($_POST['store_wm'])) {
          $prod_store_wm = $_POST['store_wm'];
        } else {
          $prod_store_wm = '0';
        }
        if (tep_not_null($_POST['store_dc'])) {
          $prod_store_dc = $_POST['store_dc'];
        } else {
          $prod_store_dc = '0';
        }
        if (tep_not_null($_POST['store_tt'])) {
          $prod_store_tt = $_POST['store_tt'];
        } else {
          $prod_store_tt = '0';
        }

        $sql_data_array = array(
          'store_cg' => tep_db_prepare_input($prod_store_cg),
          'store_ho' => tep_db_prepare_input($prod_store_ho),
          'store_hs' => tep_db_prepare_input($prod_store_hs),
          'store_wm' => tep_db_prepare_input($prod_store_wm),
          'store_dc' => tep_db_prepare_input($prod_store_dc),
          'store_tt' => tep_db_prepare_input($prod_store_tt)
        );

        if ($action == 'insert_product') {
          $insert_sql_data = array('products_id' => $products_id);

          $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

          tep_db_perform(TABLE_PRODUCTS_TO_STORES, $sql_data_array);
        } elseif ($action == 'update_product') {
          tep_db_perform(TABLE_PRODUCTS_TO_STORES, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "'");
        }

        // FINISH FOR PRODUCTS_TO_STORES

        // START FOR PRODUCTS_TO_STOCK_LOCATION

        $prod_stock_liverpool = $_POST['stock_liverpool'];
        $prod_stock_liverpool_stashed = $_POST['stock_liverpool_stashed'];
        $prod_stock_norfolk_stashed = $_POST['stock_norfolk_stashed'];
        $prod_stock_chester = $_POST['stock_chester'];
        $prod_stock_chester_stashed = $_POST['stock_chester_stashed'];
        $prod_stock_leeds = $_POST['stock_leeds'];
        $prod_stock_leeds_stashed = $_POST['stock_leeds_stashed'];
        $prod_stock_mashtun = $_POST['stock_mashtun'];
        $prod_stock_mashtun_stashed = $_POST['stock_mashtun_stashed'];
        $prod_stock_lime_street = $_POST['stock_lime_street'];
        $prod_stock_lime_street_stashed = $_POST['stock_lime_street_stashed'];
        $prod_stock_edinburgh = $_POST['stock_edinburgh'];
        $prod_stock_edinburgh_stashed = $_POST['stock_edinburgh_stashed'];
        $prod_stock_london = $_POST['stock_london'];
        $prod_stock_london_cg = $_POST['stock_london_cg'];
        $prod_stock_mayfair = $_POST['stock_mayfair'];
        $prod_stock_norfolk = $_POST['stock_norfolk'];
        $prod_stock_knutsford = $_POST['stock_knutsford'];

        if (tep_not_null($_POST['stock_supplier'])) {
          $prod_stock_supplier = $_POST['stock_supplier'];
        } else {
          $prod_stock_supplier = '0';
        }
        if (tep_not_null($_POST['stock_transfer'])) {
          $prod_stock_transfer = $_POST['stock_transfer'];
        } else {
          $prod_stock_transfer = '0';
        }

        $sql_data_array = array(
          'stock_liverpool' => tep_db_prepare_input($prod_stock_liverpool),
          'stock_liverpool_stashed' => tep_db_prepare_input($prod_stock_liverpool_stashed),
          'stock_norfolk_stashed' => tep_db_prepare_input($prod_stock_norfolk_stashed),
          'stock_chester' => tep_db_prepare_input($prod_stock_chester),
          'stock_chester_stashed' => tep_db_prepare_input($prod_stock_chester_stashed),
          'stock_leeds' => tep_db_prepare_input($prod_stock_leeds),
          'stock_leeds_stashed' => tep_db_prepare_input($prod_stock_leeds_stashed),
          'stock_mashtun' => tep_db_prepare_input($prod_stock_mashtun),
          'stock_mashtun_stashed' => tep_db_prepare_input($prod_stock_mashtun_stashed),
          'stock_lime_street' => tep_db_prepare_input($prod_stock_lime_street),
          'stock_edinburgh' => tep_db_prepare_input($prod_stock_edinburgh),
          'stock_lime_street_stashed' => tep_db_prepare_input($prod_stock_lime_street_stashed),
          'stock_edinburgh_stashed' => tep_db_prepare_input($prod_stock_edinburgh_stashed),
          'stock_london' => tep_db_prepare_input($prod_stock_london),
          'stock_london_cg' => tep_db_prepare_input($prod_stock_london_cg),
          'stock_mayfair' => tep_db_prepare_input($prod_stock_mayfair),
          'stock_norfolk' => tep_db_prepare_input($prod_stock_norfolk),
          'stock_knutsford' => tep_db_prepare_input($prod_stock_knutsford),
          'stock_supplier' => tep_db_prepare_input($prod_stock_supplier),
          'stock_transfer' => tep_db_prepare_input($prod_stock_transfer)
        );

        // TABLE_PRODUCTS_TO_STOCK_LOCATION
        if ($action == 'insert_product') {
          $general_insert_sql_data = array('products_id' => $products_id);
          $general_sql_data_array = array_merge($sql_data_array, $general_insert_sql_data);
          tep_db_perform(TABLE_PRODUCTS_TO_STOCK_LOCATION, $general_sql_data_array);
        } elseif ($action == 'update_product') {
          tep_db_perform(TABLE_PRODUCTS_TO_STOCK_LOCATION, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "'");
        }


        // TABLE_PRODUCTS_TO_STOCK_LOCATION_HISTORY
        // get the last entry
        $ptslh_query = tep_db_query("select * from " . TABLE_PRODUCTS_TO_STOCK_LOCATION_HISTORY . " where products_id = '" . (int)$products_id . "' order by products_to_stock_location_history_id desc limit 1");
        $ptslh = tep_db_fetch_array($ptslh_query);

        // if there is an entry
        if ($ptslh['products_to_stock_location_history_id']) {
          // is the last entry different?
          if ($ptslh['stock_liverpool'] != $prod_stock_liverpool || $ptslh['stock_liverpool_stashed'] != $prod_stock_liverpool_stashed || $ptslh['stock_norfolk_stashed'] != $prod_stock_norfolk_stashed || $ptslh['stock_chester'] != $prod_stock_chester || $ptslh['stock_chester_stashed'] != $prod_stock_chester_stashed || $ptslh['stock_leeds'] != $prod_stock_leeds || $ptslh['stock_leeds_stashed'] != $prod_stock_leeds_stashed || $ptslh['stock_mashtun'] != $prod_stock_mashtun || $ptslh['stock_mashtun_stashed'] != $prod_stock_mashtun_stashed || $ptslh['stock_lime_street'] != $prod_stock_lime_street || $ptslh['stock_edinburgh'] != $prod_stock_edinburgh || $ptslh['stock_lime_street_stashed'] != $prod_stock_lime_street_stashed || $ptslh['stock_edinburgh_stashed'] != $prod_stock_edinburgh_stashed || $ptslh['stock_london'] != $prod_stock_london || $ptslh['stock_london_cg'] != $prod_stock_london_cg || $ptslh['stock_mayfair'] != $prod_stock_mayfair || $ptslh['stock_norfolk'] != $prod_stock_norfolk || $ptslh['stock_knutsford'] != $prod_stock_knutsford || $ptslh['stock_supplier'] != $prod_stock_supplier || $ptslh['stock_transfer'] != $prod_stock_transfer) {
            $insert_stock_history = true;
          } else {
            $insert_stock_history = false;
          }
        } else { // theres no previous entry

          if (tep_not_null($prod_stock_liverpool) || tep_not_null($prod_stock_liverpool_stashed) || tep_not_null($prod_stock_norfolk_stashed) || tep_not_null($prod_stock_chester) || tep_not_null($prod_stock_chester_stashed) || tep_not_null($prod_stock_leeds) || tep_not_null($prod_stock_leeds_stashed) || tep_not_null($prod_stock_mashtun) || tep_not_null($prod_stock_mashtun_stashed) || tep_not_null($prod_stock_lime_street) || tep_not_null($prod_stock_edinburgh) || tep_not_null($prod_stock_lime_street_stashed) || tep_not_null($prod_stock_edinburgh_stashed) || tep_not_null($prod_stock_london) || tep_not_null($prod_stock_london_cg) || tep_not_null($prod_stock_mayfair) || tep_not_null($prod_stock_norfolk) || tep_not_null($prod_stock_knutsford) || $prod_stock_supplier != '0' || $prod_stock_transfer != '0') { // check for blank inserts
            $insert_stock_history = true;
          } else {
            $insert_stock_history = false;
          }

          if ($prod_stock_liverpool == '0' && $prod_stock_liverpool_stashed == '0' && $prod_stock_norfolk_stashed == '0' && $prod_stock_chester == '0' && $prod_stock_chester_stashed == '0' && $prod_stock_leeds == '0' && $prod_stock_leeds_stashed == '0' && $prod_stock_mashtun == '0' && $prod_stock_mashtun_stashed == '0' && $prod_stock_lime_street == '0' && $prod_stock_edinburgh == '0' && $prod_stock_lime_street_stashed == '0' && $prod_stock_edinburgh_stashed == '0' && $prod_stock_london == '0' && $prod_stock_london_cg == '0' && $prod_stock_mayfair == '0' && $prod_stock_norfolk == '0' && $prod_stock_knutsford == '0' && $prod_stock_supplier == '0' && $prod_stock_transfer == '0') { // check for 0 updates
            $insert_stock_history = false;
          }
        }

        if ($insert_stock_history) {
          $history_insert_sql_data = array('products_id' => $products_id, 'date_modified' => 'now()');
          $history_sql_data_array = array_merge($sql_data_array, $history_insert_sql_data);
          tep_db_perform(TABLE_PRODUCTS_TO_STOCK_LOCATION_HISTORY, $history_sql_data_array);
        }

        // FINISH FOR PRODUCTS_TO_STOCK_LOCATION

        // START FOR PRODUCTS_ADD_ON_CATEGORIES

        $prod_add_on_cat_id = $_POST['prod_add_on_cat_id'];

        $check_cat_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_ADD_ON_CATEGORIES . " where products_id = '" . (int)$products_id . "'");

        if (tep_db_num_rows($check_cat_query)) {
          $check_cat_exists = 1;
        } else {
          $check_cat_exists = 0;
        }

        if (($prod_add_on_cat_id != '0' && $prod_add_on_cat_id != '') || $check_cat_exists) {

          $sql_data_array = array('categories_id' => tep_db_prepare_input($prod_add_on_cat_id));

          if ($action == 'insert_product' || $check_cat_exists == 0) {
            $insert_sql_data = array('products_id' => $products_id);
            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
            tep_db_perform(TABLE_PRODUCTS_ADD_ON_CATEGORIES, $sql_data_array);
          } elseif ($action == 'update_product') {
            tep_db_perform(TABLE_PRODUCTS_ADD_ON_CATEGORIES, $sql_data_array, 'update', "products_id = '" . (int)$products_id . "'");
          }
        }

        // FINISH FOR PRODUCTS_ADD_ON_CATEGORIES

        // START: Extra Fields Contribution
        $extra_fields_query = tep_db_query("SELECT * FROM " . TABLE_PRODUCTS_TO_PRODUCTS_EXTRA_FIELDS . " WHERE products_id = " . (int)$products_id);
        while ($products_extra_fields = tep_db_fetch_array($extra_fields_query)) {
          $extra_product_entry[$products_extra_fields['products_extra_fields_id']] = $products_extra_fields['products_extra_fields_value'];
        }

        if ($_POST['extra_field']) { // Check to see if there are any need to update extra fields.
          foreach ($_POST['extra_field'] as $key => $val) {
            if (isset($extra_product_entry[$key])) { // an entry exists
              if ($val == '') tep_db_query("DELETE FROM " . TABLE_PRODUCTS_TO_PRODUCTS_EXTRA_FIELDS . " where products_id = " . (int)$products_id . " AND  products_extra_fields_id = " . (int)$key);
              else tep_db_query("UPDATE " . TABLE_PRODUCTS_TO_PRODUCTS_EXTRA_FIELDS . " SET products_extra_fields_value = '" . tep_db_input($val) . "' WHERE products_id = " . (int)$products_id . " AND  products_extra_fields_id = " . (int)$key);
            } else { // an entry does not exist
              if ($val != '') tep_db_query("INSERT INTO " . TABLE_PRODUCTS_TO_PRODUCTS_EXTRA_FIELDS . " (products_id, products_extra_fields_id, products_extra_fields_value) VALUES ('" . (int)$products_id . "', '" . (int)$key . "', '" . tep_db_input($val) . "')");
            }
          }
        }
        // END: Extra Fields Contribution

        if (USE_CACHE == 'true') {
          tep_reset_cache_block('categories');
          tep_reset_cache_block('cat_links');
          tep_reset_cache_block('also_purchased');
          tep_reset_cache_block('filter_menu'); // 4 hour intervals
        }

        tep_set_product_visible_status((int)$products_id);

        // TAG EKLEME - ürün ekleme/güncelleme işleminin hemen sonuna ekle
        if (isset($products_id)) {
          tep_db_query("DELETE FROM products_tags WHERE product_id = '" . (int)$products_id . "'");
          if (tep_not_null($_POST['tags'])) {
            $tags = array_map('trim', explode(',', $_POST['tags']));
            foreach ($tags as $tag) {
              if ($tag !== '') {
                $sql = "INSERT INTO products_tags (product_id, tag) VALUES ('" . (int)$products_id . "', '" . tep_db_input($tag) . "')";
                tep_db_query($sql);
              }
            }
          }
        }

        if (isset($_POST['return']) && $_POST['return'] == '1') {
          tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products_id . '&action=new_product'));
        } else {
          tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products_id));
        }
      }
      break;
    case 'copy_to_confirm':


      if ((tep_not_null($_POST['products_id'])) && (tep_not_null($_POST['categories_id']))) {

        $products_id = tep_db_prepare_input($_POST['products_id']);
        $categories_id = tep_db_prepare_input($_POST['categories_id']);

        if ($_POST['copy_as'] == 'link') {
          if ($_POST['categories_id'] != $current_category_id) {
            foreach ($products_id as $product) {

              $check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . tep_db_input($product) . "' and categories_id = '" . tep_db_input($categories_id) . "'");
              $check = tep_db_fetch_array($check_query);

              if ($check['total'] < '1') {
                tep_db_query("insert into " . TABLE_PRODUCTS_TO_CATEGORIES . " (products_id, categories_id) values ('" . tep_db_input($product) . "', '" . tep_db_input($categories_id) . "')");
              }

              tep_set_product_visible_status(tep_db_input($product));
            }
          } else {
            $messageStack->add_session(ERROR_CANNOT_LINK_TO_SAME_CATEGORY, 'error');
          }
        } elseif ($_POST['copy_as'] == 'duplicate') {

          foreach ($products_id as $product_id) {

            $product_query = tep_db_query("select * from " . TABLE_PRODUCTS . " where products_id = '" . tep_db_input($product_id) . "'");

            $product = tep_db_fetch_array($product_query);

            tep_db_query("insert into " . TABLE_PRODUCTS . " (products_quantity, products_model, products_mpn, products_image, products_subimage7, products_price, products_trade_price, products_trade_price_discount, products_date_added, products_date_available, products_weight, products_cigar_ring_gauge, products_cigar_length, products_cigar_smoke_time, products_max_order, products_status, products_tax_class_id, manufacturers_id, suppliers_id, products_geo_zones, products_free_shipping, products_order_type, products_cigar, products_earn_points, products_vat_deductable, products_paypal, hide_navigation, hide_new_in, products_event_date, products_bundle, sold_in_bundle_only, products_created_date) values ('" . tep_db_input($product['products_quantity']) . "', '" . tep_db_input($product['products_model']) . "','" . tep_db_input($product['products_mpn']) . "', '" . tep_db_input($product['products_image']) . "', '" . tep_db_input($product['products_subimage7']) . "', '" . tep_db_input($product['products_price']) . "', '" . tep_db_input($product['products_trade_price']) . "', '" . tep_db_input($product['products_trade_price_discount']) . "',  now(), '" . tep_db_input($product['products_date_available']) . "', '" . tep_db_input($product['products_weight']) . "', '" . tep_db_input($product['products_cigar_ring_gauge']) . "', '" . tep_db_input($product['products_cigar_length']) . "', '" . tep_db_input($product['products_cigar_smoke_time']) . "', '" . tep_db_input($product['products_max_order']) . "', '0', '" . (int)$product['products_tax_class_id'] . "', '" . (int)$product['manufacturers_id'] . "', '" . (int)$product['suppliers_id'] . "', '" . (int)$product['products_geo_zones'] . "', '" . (int)$product['products_free_shipping'] . "', '" . (int)$product['products_order_type'] . "', '" . (int)$product['products_cigar'] . "', '" . (int)$product['products_earn_points'] . "', '" . (int)$product['products_vat_deductable'] . "', '" . (int)$product['products_paypal'] . "', '" . (int)$product['hide_navigation'] . "', '" . (int)$product['hide_new_in'] . "', '" . $product['products_event_date'] . "', '" . (int)$product['products_bundle'] . "', '" . (int)$product['sold_in_bundle_only'] . "', now())");

            $dup_products_id = tep_db_insert_id();

            // bundled products begin
            if ($product['products_bundle'] == 'yes') {
              $bundle_query = tep_db_query('select subproduct_id, subproduct_qty from ' . TABLE_PRODUCTS_BUNDLES . ' where bundle_id = ' . (int)$product_id);
              while ($subprod = tep_db_fetch_array($bundle_query)) {
                tep_db_query('insert into ' . TABLE_PRODUCTS_BUNDLES . " (bundle_id, subproduct_id, subproduct_qty) VALUES ('" . (int)$dup_products_id . "', '" . (int)$subprod['subproduct_id'] . "', '" . (int)$subprod['subproduct_qty'] . "')");
              }
            }
            // bundled products end

            // START FOR ADDITIONAL IMAGES

            $additional_images_query = tep_db_query("select pi.* from " . TABLE_PRODUCTS_IMAGES . " pi where pi.products_id = '" . (int)$product_id . "'");
            while ($additional_images = tep_db_fetch_array($additional_images_query)) {
              tep_db_query("insert into " . TABLE_PRODUCTS_IMAGES . " (products_id, image, sort_order, type) values ('" . (int)$dup_products_id . "', '" . tep_db_input($additional_images['image']) . "', '" . tep_db_input($additional_images['sort_order']) . "', '" . tep_db_input($additional_images['type']) . "')");
            }

            // FINISH FOR ADDITIONAL IMAGES


            // START FOR ADDITIONAL INFO

            $additional_info_array = tep_product_additional_info_array((int)$product_id, 1, false);
            if (!empty($additional_info_array)) {
              $new_product_id_sql_data = array('ai_products_id' => $dup_products_id);
              $additional_info_sql_data_array = array_merge($additional_info_array, $new_product_id_sql_data);
              tep_db_perform(TABLE_ADDITIONAL_INFO_CIGAR, $additional_info_sql_data_array);
            }

            $additional_info_array = tep_product_additional_info_array((int)$product_id, 2, false);
            if (!empty($additional_info_array)) {
              $new_product_id_sql_data = array('ai_products_id' => $dup_products_id);
              $additional_info_sql_data_array = array_merge($additional_info_array, $new_product_id_sql_data);
              tep_db_perform(TABLE_ADDITIONAL_INFO_WHISKY, $additional_info_sql_data_array);
            }

            // FINISH FOR ADDITIONAL INFO

            // START FOR ATTRIBUTES

            $products_attributes_query = tep_db_query("select pa.* from " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . (int)$product_id . "'");

            while ($products_attributes = tep_db_fetch_array($products_attributes_query)) {

              tep_db_query("insert into " . TABLE_PRODUCTS_ATTRIBUTES . " (products_id, options_id, options_values_id, options_values_price, price_prefix, options_values_price_cost, price_cost_prefix, sort_order, products_options_sort_order) values ('" . (int)$dup_products_id . "', '" . tep_db_input($products_attributes['options_id']) . "', '" . tep_db_input($products_attributes['options_values_id']) . "', '" . tep_db_input($products_attributes['options_values_price']) . "', '" . tep_db_input($products_attributes['price_prefix']) . "', '" . tep_db_input($products_attributes['options_values_price_cost']) . "', '" . tep_db_input($products_attributes['price_cost_prefix']) . "', '" . tep_db_input($products_attributes['sort_order']) . "', '" . tep_db_input($products_attributes['products_options_sort_order']) . "')");
            }

            // FINISH FOR ATTRIBUTES

            // START FOR TOBACCO

            $tobacco_query = tep_db_query("select t.* from " . TABLE_PRODUCTS_TOBACCO . " t where t.products_id = '" . (int)$product_id . "'");
            $tobacco = tep_db_fetch_array($tobacco_query);

            if ($tobacco['products_id']) {

              tep_db_query("insert into " . TABLE_PRODUCTS_TOBACCO . " (products_id, products_options_id, base_grams, base_margin, price_per_gram) values ('" . (int)$dup_products_id . "', '" . tep_db_input($tobacco['products_options_id']) . "', '" . tep_db_input($tobacco['base_grams']) . "', '" . tep_db_input($tobacco['base_margin']) . "', '" . tep_db_input($tobacco['price_per_gram']) . "')");
            }

            // FINISH FOR TOBACCO

            // START FOR PRODUCTS_TO_STORES

            $product_to_stores_query = tep_db_query("select store_cg, store_hs, store_ho, store_wm, store_dc, store_tt from " . TABLE_PRODUCTS_TO_STORES . " where products_id = '" . tep_db_input($product_id) . "'");

            $product_to_stores = tep_db_fetch_array($product_to_stores_query);

            tep_db_query("insert into " . TABLE_PRODUCTS_TO_STORES . " (products_id, store_cg, store_hs, store_ho, store_wm, store_dc, store_tt) values ('" . (int)$dup_products_id . "', '" . tep_db_input($product_to_stores['store_cg']) . "', '" . tep_db_input($product_to_stores['store_hs']) . "', '" . tep_db_input($product_to_stores['store_ho']) . "', '" . tep_db_input($product_to_stores['store_wm']) . "', '" . tep_db_input($product_to_stores['store_dc']) . "', '" . tep_db_input($product_to_stores['store_tt']) . "')");

            // FINISH FOR PRODUCTS_TO_STORES

            // START FOR PRODUCTS_TO_STOCK_LOCATION

            $product_to_stock_location_query = tep_db_query("select stock_liverpool, stock_liverpool_stashed, stock_norfolk_stashed, stock_chester, stock_chester_stashed, stock_leeds, stock_leeds_stashed, stock_mashtun, stock_mashtun_stashed, stock_lime_street, stock_edinburgh, stock_lime_street_stashed, stock_edinburgh_stashed, stock_london, stock_london_cg, stock_mayfair, stock_norfolk, stock_knutsford, stock_supplier, stock_transfer from " . TABLE_PRODUCTS_TO_STOCK_LOCATION . " where products_id = '" . tep_db_input($product_id) . "'");

            $product_to_stock_location = tep_db_fetch_array($product_to_stock_location_query);

            tep_db_query("insert into " . TABLE_PRODUCTS_TO_STOCK_LOCATION . " (products_id, stock_liverpool, stock_liverpool_stashed, stock_norfolk_stashed, stock_chester, stock_chester_stashed, stock_leeds, stock_leeds_stashed, stock_mashtun, stock_mashtun_stashed, stock_lime_street, stock_edinburgh, stock_lime_street_stashed, stock_edinburgh_stashed, stock_london, stock_london_cg, stock_mayfair, stock_norfolk, stock_knutsford, stock_supplier, stock_transfer) values ('" . (int)$dup_products_id . "', '" . tep_db_input($product_to_stock_location['stock_liverpool']) . "', '" . tep_db_input($product_to_stock_location['stock_liverpool_stashed']) . "', '" . tep_db_input($product_to_stock_location['stock_norfolk_stashed']) . "', '" . tep_db_input($product_to_stock_location['stock_chester']) . "', '" . tep_db_input($product_to_stock_location['stock_chester_stashed']) . "', '" . tep_db_input($product_to_stock_location['stock_leeds']) . "', '" . tep_db_input($product_to_stock_location['stock_leeds_stashed']) . "', '" . tep_db_input($product_to_stock_location['stock_mashtun']) . "', '" . tep_db_input($product_to_stock_location['stock_mashtun_stashed']) . "', '" . tep_db_input($product_to_stock_location['stock_lime_street']) . "', '" . tep_db_input($product_to_stock_location['stock_edinburgh']) . "', '" . tep_db_input($product_to_stock_location['stock_lime_street_stashed']) . "', '" . tep_db_input($product_to_stock_location['stock_edinburgh_stashed']) . "', '" . tep_db_input($product_to_stock_location['stock_london']) . "', '" . tep_db_input($product_to_stock_location['stock_london_cg']) . "', '" . tep_db_input($product_to_stock_location['stock_mayfair']) . "', '" . tep_db_input($product_to_stock_location['stock_norfolk']) . "', '" . tep_db_input($product_to_stock_location['stock_knutsford']) . "', '" . tep_db_input($product_to_stock_location['stock_supplier']) . "', '" . tep_db_input($product_to_stock_location['stock_transfer']) . "')");

            // FINISH FOR PRODUCTS_TO_STOCK_LOCATION

            // START FOR PRODUCTS_ADD_ON_CATEGORIES

            $product_add_on_categories_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_ADD_ON_CATEGORIES . " where products_id = '" . tep_db_input($product_id) . "'");

            $product_add_on_categories = tep_db_fetch_array($product_add_on_categories_query);

            tep_db_query("insert into " . TABLE_PRODUCTS_ADD_ON_CATEGORIES . " (products_id, categories_id) values ('" . (int)$dup_products_id . "', '" . tep_db_input($product_add_on_categories['categories_id']) . "')");

            // FINISH FOR PRODUCTS_ADD_ON_CATEGORIES

            // START FOR PRODUCTS STORES EXTRA

            $product_stores_extra_query = tep_db_query("select store_id, products_name, products_description, products_price, products_image, products_head_title_tag, products_head_desc_tag, products_head_keywords_tag from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . tep_db_input($product_id) . "'");

            if (tep_db_num_rows($product_stores_extra_query)) {

              while ($product_stores_extra = tep_db_fetch_array($product_stores_extra_query)) {

                tep_db_query("insert into " . TABLE_PRODUCTS_STORES_EXTRA . " (products_id, store_id, products_name, products_description, products_price, products_image, products_head_title_tag, products_head_desc_tag, products_head_keywords_tag) values ('" . (int)$dup_products_id . "', '" . tep_db_input($product_stores_extra['store_id']) . "', '" . tep_db_input($product_stores_extra['products_name']) . "', '" . tep_db_input($product_stores_extra['products_description']) . "', '" . tep_db_input($product_stores_extra['products_price']) . "', '" . tep_db_input($product_stores_extra['products_image']) . "', '" . tep_db_input($product_stores_extra['products_head_title_tag']) . "', '" . tep_db_input($product_stores_extra['products_head_desc_tag']) . "', '" . tep_db_input($product_stores_extra['products_head_keywords_tag']) . "')");
              }
            }
            // FINISH FOR PRODUCTS STORES EXTRA

            //HTC BOC
            $description_query = tep_db_query("select language_id, products_name, products_description, products_read_more, products_tasting_notes, products_taste_test, products_awards, products_head_title_tag, products_head_desc_tag, products_head_keywords_tag, products_url from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . tep_db_input($product_id) . "'");

            while ($description = tep_db_fetch_array($description_query)) {

              tep_db_query("INSERT INTO " . TABLE_PRODUCTS_DESCRIPTION . " (products_id, language_id, products_name, products_description, products_read_more, products_tasting_notes, products_taste_test, products_awards, products_head_title_tag, products_head_desc_tag, products_head_keywords_tag, products_url, products_viewed) VALUES ('" . (int)$dup_products_id . "', '" . (int)$description['language_id'] . "', '" . tep_db_input($description['products_name']) . "', '" . tep_db_input($description['products_description']) . "', '" . tep_db_input($description['products_read_more']) . "', '" . tep_db_input($description['products_tasting_notes']) . "', '" . tep_db_input($description['products_taste_test']) . "', '" . tep_db_input($description['products_awards']) . "', '" . tep_db_input($description['products_head_title_tag']) . "', '" . tep_db_input($description['products_head_desc_tag']) . "', '" . tep_db_input($description['products_head_keywords_tag']) . "', '" . tep_db_input($description['products_url']) . "', '0')");
            }
            //HTC EOC

            tep_db_query("insert into " . TABLE_PRODUCTS_TO_CATEGORIES . " (products_id, categories_id) values ('" . $dup_products_id . "', '" . tep_db_input($categories_id) . "')");

            // add product to any Coupons that have product restrictions
            $restriction_query = tep_db_query("select coupons_id from " . TABLE_DISCOUNT_COUPONS_TO_PRODUCTS . " group by coupons_id");

            while ($restriction = tep_db_fetch_array($restriction_query)) {
              tep_db_query("insert into " . TABLE_DISCOUNT_COUPONS_TO_PRODUCTS . " (coupons_id, products_id) values ('" . $restriction['coupons_id'] . "', '" . $dup_products_id . "')");
            }

            tep_set_product_visible_status($dup_products_id);
          }
        }

        if (USE_CACHE == 'true') {
          tep_reset_cache_block('categories');
          tep_reset_cache_block('cat_links');
          tep_reset_cache_block('also_purchased');
          tep_reset_cache_block('filter_menu'); // 4 hour intervals
        }
      }

      tep_redirect(tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $categories_id));


      break;
  }
}

// check if the catalog image directory exists
if (is_dir(DIR_FS_CATALOG_IMAGES)) {
  if (!is_writeable(DIR_FS_CATALOG_IMAGES)) $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_NOT_WRITEABLE, 'error');
} else {
  $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_DOES_NOT_EXIST, 'error');
}

$cat_master = 1; // load in cat master styles and java

require(DIR_WS_INCLUDES . 'template_top.php');

if ($action == 'new_product') {

  $parameters = array(
    'products_name' => '',
    'products_description' => '',
    'products_read_more' => '',
    'products_tasting_notes' => '',
    'products_taste_test' => '',
    'products_awards' => '',
    'products_url' => '',
    'store_cg' => '',
    'store_ho' => '',
    'store_hs' => '',
    'store_wm' => '',
    'store_dc' => '',
    'store_tt' => '',
    'stock_liverpool' => '',
    'stock_liverpool_stashed' => '',
    'stock_norfolk_stashed' => '',
    'stock_chester' => '',
    'stock_chester_stashed' => '',
    'stock_leeds' => '',
    'stock_leeds_stashed' => '',
    'stock_mashtun' => '',
    'stock_mashtun_stashed' => '',
    'stock_lime_street' => '',
    'stock_edinburgh' => '',
    'stock_lime_street_stashed' => '',
    'stock_edinburgh_stashed' => '',
    'stock_london' => '',
    'stock_london_cg' => '',
    'stock_mayfair' => '',
    'stock_norfolk' => '',
    'stock_knutsford' => '',
    'stock_supplier' => '',
    'stock_transfer' => '',
    'products_id' => '',
    'products_quantity' => '9999',
    'products_model' => '',
    'products_mpn' => '',
    'products_image' => '',
    'products_subimage7' => '',
    'products_larger_images' => array(),
    'products_price' => '',
    'products_trade_price' => '',
    'products_trade_price_discount' => '',
    'products_weight' => '',
    'products_cigar_ring_gauge' => '',
    'products_cigar_length' => '',
    'products_cigar_smoke_time' => '',
    'products_max_order' => '',
    'products_date_added' => '',
    'products_last_modified' => '',
    'products_date_available' => '',
    'products_status' => '',
    'products_tax_class_id' => '',
    'manufacturers_id' => '',
    'suppliers_id' => '',
    'prod_add_on_cat_id' => '',
    'products_geo_zones' => '',
    'products_free_shipping' => '',
    'products_order_type' => '',
    'products_cigar' => '',
    'products_earn_points' => '1',
    'products_paypal' => '',
    'hide_navigation' => '',
    'hide_new_in' => '',
    'products_event_date' => '',
    'products_bundle' => '',
    'sold_in_bundle_only' => 'no',
    'products_vat_deductable' => '',
    'product_tags' => ''
  );

  $pInfo = new objectInfo($parameters);

  //HTC BOC // BOF: More Pics 6  Added
  if (isset($_GET['pID']) && (!$_POST)) {

    // START: Extra Fields Contribution
    $products_extra_fields_query = tep_db_query("SELECT * FROM " . TABLE_PRODUCTS_TO_PRODUCTS_EXTRA_FIELDS . " WHERE products_id=" . (int)$_GET['pID']);
    while ($products_extra_fields = tep_db_fetch_array($products_extra_fields_query)) {
      $extra_field[$products_extra_fields['products_extra_fields_id']] = $products_extra_fields['products_extra_fields_value'];
    }
    $extra_field_array = array('extra_field' => $extra_field);
    $pInfo->__construct($extra_field_array);
    // END: Extra Fields Contribution


    if ($admin['id'] == '2') {
      $product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_read_more, pd.products_tasting_notes, pd.products_taste_test, pd.products_awards, pd.products_head_title_tag, pd.products_head_desc_tag, pd.products_head_keywords_tag, pd.products_url, psl.stock_liverpool, psl.stock_liverpool_stashed, psl.stock_norfolk_stashed, psl.stock_chester, psl.stock_chester_stashed, psl.stock_leeds, psl.stock_leeds_stashed, psl.stock_mashtun, psl.stock_mashtun_stashed, psl.stock_lime_street, psl.stock_edinburgh, psl.stock_lime_street_stashed, psl.stock_edinburgh_stashed, psl.stock_london, psl.stock_london_cg, psl.stock_mayfair, psl.stock_norfolk, psl.stock_knutsford, psl.stock_supplier, psl.stock_transfer, ps.store_cg, ps.store_ho, ps.store_hs, ps.store_wm, ps.store_dc, ps.store_tt, p.products_id, p.products_quantity, p.products_model, p.products_mpn, p.products_image, p.products_subimage7, p.products_price, p.products_trade_price, p.products_trade_price_discount, p.products_weight, p.products_cigar_ring_gauge, p.products_cigar_length, p.products_cigar_smoke_time, p.products_max_order, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_tax_class_id, p.manufacturers_id, p.suppliers_id, p.products_geo_zones, p.products_free_shipping, p.products_order_type, p.products_cigar, p.products_earn_points, p.products_vat_deductable, p.products_paypal, p.hide_navigation, p.hide_new_in, p.products_event_date, paoc.categories_id as prod_add_on_cat_id, p.products_bundle, p.sold_in_bundle_only from " . TABLE_PRODUCTS . " p left join " . TABLE_PRODUCTS_ADD_ON_CATEGORIES . " paoc on p.products_id = paoc.products_id left join " . TABLE_PRODUCTS_TO_STOCK_LOCATION . " psl on p.products_id = psl.products_id, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_STORES . " ps where p.products_id = '" . (int)$_GET['pID'] . "' and p.products_id = pd.products_id and p.products_id = ps.products_id and pd.language_id = '" . (int)$languages_id . "'");
    } else {
      $product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_read_more, pd.products_tasting_notes, pd.products_taste_test, pd.products_awards, pd.products_head_title_tag, pd.products_head_desc_tag, pd.products_head_keywords_tag, pd.products_url, psl.stock_liverpool, psl.stock_liverpool_stashed, psl.stock_norfolk_stashed, psl.stock_chester, psl.stock_chester_stashed, psl.stock_leeds, psl.stock_leeds_stashed, psl.stock_mashtun, psl.stock_mashtun_stashed, psl.stock_lime_street, psl.stock_edinburgh, psl.stock_lime_street_stashed, psl.stock_edinburgh_stashed, psl.stock_london, psl.stock_london_cg, psl.stock_mayfair, psl.stock_norfolk, psl.stock_knutsford, psl.stock_supplier, psl.stock_transfer, ps.store_cg, ps.store_ho, ps.store_hs, ps.store_wm, ps.store_dc, ps.store_tt, p.products_id, p.products_quantity, p.products_model, p.products_mpn, p.products_image, p.products_subimage7, p.products_price, p.products_trade_price, p.products_trade_price_discount, p.products_weight, p.products_cigar_ring_gauge, p.products_cigar_length, p.products_cigar_smoke_time, p.products_max_order, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_tax_class_id, p.manufacturers_id, p.suppliers_id, p.products_geo_zones, p.products_free_shipping, p.products_order_type, p.products_cigar, p.products_earn_points, p.products_vat_deductable, p.products_paypal, p.hide_navigation, p.hide_new_in, p.products_event_date, paoc.categories_id as prod_add_on_cat_id, p.products_bundle, p.sold_in_bundle_only from " . TABLE_PRODUCTS . " p left join " . TABLE_PRODUCTS_ADD_ON_CATEGORIES . " paoc on p.products_id = paoc.products_id, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_STORES . " ps, " . TABLE_PRODUCTS_TO_STOCK_LOCATION . " psl where p.products_id = '" . (int)$_GET['pID'] . "' and p.products_id = pd.products_id and p.products_id = ps.products_id and p.products_id = psl.products_id and pd.language_id = '" . (int)$languages_id . "'");
    }
    $product = tep_db_fetch_array($product_query);
    //HTC EOC  // EOF: More Pics 6

    $pInfo->__construct($product);

    $product_images_query = tep_db_query("select id, image, htmlcontent, sort_order, type from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int)$product['products_id'] . "' order by sort_order");
    while ($product_images = tep_db_fetch_array($product_images_query)) {
      $pInfo->products_larger_images[] = array(
        'id' => $product_images['id'],
        'image' => $product_images['image'],
        'htmlcontent' => $product_images['htmlcontent'],
        'sort_order' => $product_images['sort_order'],
        'type' => $product_images['type']
      );
    }

    // 1) Mevcut etiketleri yükle (formun başında, $pInfo oluşturulduktan hemen sonra)
    if (isset($_GET['pID']) && (!$_POST)) {
      $tags_query = tep_db_query("SELECT tag FROM products_tags WHERE product_id = '" . (int)$_GET['pID'] . "'");
      $tags = array();
      while ($tag_row = tep_db_fetch_array($tags_query)) {
        $tags[] = $tag_row['tag'];
      }
      $pInfo->product_tags = implode(', ', $tags);
    } elseif (tep_not_null($_POST['tags'])) {
      $pInfo->product_tags = $_POST['tags'];
    }
  } elseif (tep_not_null($_POST)) {

    $pInfo->__construct($_POST);

    $products_name = $_POST['products_name'];
    $products_description = $_POST['products_description'];
    $products_read_more = $_POST['products_read_more'];
    $products_tasting_notes = $_POST['products_tasting_notes'];
    $products_taste_test = $_POST['products_taste_test'];
    $products_awards = $_POST['products_awards'];
    $products_url = $_POST['products_url'];
  }

  // BOF Bundled Products
  $bundle_array = array();
  if (isset($pInfo->products_bundle) && $pInfo->products_bundle == "yes") {
    $bundle_array = tep_get_bundle_data_array((int)$_GET['pID']);
  }
  $bundle_count = count($bundle_array);
  // EOF Bundled Products

  $manufacturers_array = array(array('id' => '', 'text' => TEXT_NONE));
  $manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from " . TABLE_MANUFACTURERS . " order by manufacturers_name");
  while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
    $manufacturers_array[] = array(
      'id' => $manufacturers['manufacturers_id'],
      'text' => $manufacturers['manufacturers_name']
    );
  }

  $suppliers_array = array(array('id' => '', 'text' => TEXT_NONE));
  $suppliers_query = tep_db_query("select suppliers_id, suppliers_name from " . TABLE_SUPPLIERS . " order by suppliers_name");
  while ($suppliers = tep_db_fetch_array($suppliers_query)) {
    $suppliers_array[] = array(
      'id' => $suppliers['suppliers_id'],
      'text' => $suppliers['suppliers_name']
    );
  }

  //BEGIN country_based_shipping
  $geo_zones_array = array(array('id' => '0', 'text' => TEXT_ALL_GEO_ZONES));
  $geo_zones_query = tep_db_query("select geo_zone_id, geo_zone_name from " . TABLE_GEO_ZONES . " where geo_zone_id not in (6,14,15,17,18) order by geo_zone_name");
  while ($geo_zones = tep_db_fetch_array($geo_zones_query)) {
    $geo_zones_array[] = array(
      'id' => $geo_zones['geo_zone_id'],
      'text' => $geo_zones['geo_zone_name']
    );
  }

  //END country_based_shipping
  $free_shipping_array = array(array('id' => '0', 'text' => 'Standard'), array('id' => '1', 'text' => 'Free UK Shipping'), array('id' => '2', 'text' => 'Free Worldwide Shipping'), array('id' => '18', 'text' => 'Free Worldwide Shipping (excluding UK/EU)'), array('id' => '3', 'text' => 'Bottle Shipping'), array('id' => '16', 'text' => 'Wine & Champagne'), array('id' => '19', 'text' => '5cl & Aftershave Bottles'), array('id' => '4', 'text' => 'TNT Shipping (Butane Gas)'), array('id' => '5', 'text' => 'Lighter Shipping'), array('id' => '6', 'text' => 'Gift Voucher / Competition'), array('id' => '7', 'text' => 'Whisky Gift'), array('id' => '8', 'text' => 'Oversize Humidor'), array('id' => '10', 'text' => 'Humidor Shipping'), array('id' => '11', 'text' => 'Pipe Tobacco Shipping'), array('id' => '12', 'text' => 'Hand Rolling Tobacco Shipping'), array('id' => '13', 'text' => 'Cigarette Shipping (Standard 2-4 Days)'), array('id' => '14', 'text' => 'Delayed Shipping (Standard 2-4 Days)'), array('id' => '15', 'text' => 'Swisscave Humidor'), array('id' => '17', 'text' => 'Davidoff Accessories'));

  $order_type_array = array(array('id' => '0', 'text' => 'London (Default)'), array('id' => '1', 'text' => 'Norfolk'), array('id' => '2', 'text' => 'Alcohol'), array('id' => '9', 'text' => 'Liverpool'), array('id' => '3', 'text' => 'Neutral'), array('id' => '10', 'text' => 'Chester'), array('id' => '6', 'text' => 'Chester : If other go to London'), array('id' => '4', 'text' => 'Chester : If other go to Norfolk'),  array('id' => '7', 'text' => 'Liverpool : If other go to London'), array('id' => '5', 'text' => 'Liverpool : If other go to Norfolk'), array('id' => '8', 'text' => 'Norfolk : If other go to London'));

  $tobacco_array = array(array('id' => '0', 'text' => 'Tobacco'), array('id' => '1', 'text' => 'Non Tobacco'), array('id' => '2', 'text' => 'Other Cuban'));
  $earn_points_array = array(array('id' => '0', 'text' => TEXT_NO), array('id' => '1', 'text' => TEXT_YES));
  $vat_deductable_array = array(array('id' => '0', 'text' => 'No'), array('id' => '2', 'text' => 'Yes'));
  $paypal_dropdown_array = array(array('id' => '2', 'text' => 'No'), array('id' => '1', 'text' => 'PayPal Allowed'));
  $hide_navigation_array = array(array('id' => '0', 'text' => TEXT_NO), array('id' => '1', 'text' => TEXT_YES));
  $hide_new_in_array = array(array('id' => '0', 'text' => 'Active'), array('id' => '2', 'text' => 'Active + on Home Page'), array('id' => '1', 'text' => 'Hidden'));

  $image_type_array = array(array('id' => '0', 'text' => 'Product Image'), array('id' => '1', 'text' => 'Promotional Image'));

  $tax_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
  $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
  while ($tax_class = tep_db_fetch_array($tax_class_query)) {
    $tax_class_array[] = array(
      'id' => $tax_class['tax_class_id'],
      'text' => $tax_class['tax_class_title']
    );
  }

  $languages = tep_get_languages();

  if (!isset($pInfo->products_status)) $pInfo->products_status = '1';

  //switch ($pInfo->products_status) {
  //  case '0': $in_status = false; $out_status = true; break;
  //  case '1':
  //  default: $in_status = true; $out_status = false;
  //}

  $product_type_array = array(array('id' => '1', 'text' => 'Normal'));
  $product_type_query = tep_db_query("select products_options_id, products_options_name, products_options_length from " . TABLE_PRODUCTS_OPTIONS . " where products_options_type = '5' order by products_options_name");

  while ($product_type = tep_db_fetch_array($product_type_query)) {
    $product_type_array[] = array(
      'id' => $product_type['products_options_id'],
      'text' => $product_type['products_options_name']
    );

    $jquery_string .= " if ($(this).val() == '" . $product_type['products_options_id'] . "') { ";
    $jquery_string .= " $('.bulk-text').text('" . $product_type['products_options_length'] . " grams'); ";
    $jquery_string .= " } ";

    // Get the margin % for the base amount.
    $product_option_values_query = tep_db_query("select pov.products_options_values_sortorder, m.products_options_values_profit_margin from " . TABLE_PRODUCTS_OPTIONS_VALUES_TO_PRODUCTS_OPTIONS . " povtpo, " . TABLE_PRODUCTS_OPTIONS_VALUES . " pov, " . TABLE_PRODUCTS_OPTIONS_VALUES_MARGINS . " m where povtpo.products_options_id = '" . $product_type['products_options_id'] . "' and povtpo.products_options_values_id = pov.products_options_values_id and povtpo.products_options_values_id = m.products_options_values_id order by m.products_options_values_default desc, pov.products_options_values_sortorder asc limit 1");

    $product_option_values = tep_db_fetch_array($product_option_values_query);

    $jquery_function_string .= "if ($('select[name=product_type]').val() == '" . $product_type['products_options_id'] . "') { ";
    $jquery_function_string .= "var base_amount_grams = '" . $product_option_values['products_options_values_sortorder'] . "';";
    $jquery_function_string .= "var base_amount_margin = '" . $product_option_values['products_options_values_profit_margin'] . "';";
    $jquery_function_string .= "var bulk_amount_grams = '" . $product_type['products_options_length'] . "';";
    $jquery_function_string .= " } ";
  }


  if (isset($_GET['pID'])) {  // check what type of product it is

    $products_options_query = tep_db_query("select pa.options_id, po.products_options_type from " . TABLE_PRODUCTS_ATTRIBUTES . " pa left join " . TABLE_PRODUCTS_OPTIONS . " po on pa.options_id = po.products_options_id where pa.products_id = '" . (int)$_GET['pID'] . "'");

    $current_product_type = 1; // normal

    while ($products_options = tep_db_fetch_array($products_options_query)) {
      if ($products_options['products_options_type'] == '5') { // slider
        $current_product_type = $products_options['options_id']; // slider option type
      }
    }
  } else {
    //default
    $bulk_cost_price_text = '';
    $current_product_type = 1;
  }

  if ($current_product_type != 1) {
    $jquery_string .= " if ($(this).val() == '" . $current_product_type . "') { ";
    $jquery_string .= " $('.bulk-cost-price').hide(); ";
    $jquery_string .= " $('.tobacco-edit-button').show(); ";
    $jquery_string .= " } ";
  }

  $form_action = (isset($_GET['pID'])) ? 'update_product' : 'insert_product';

  echo tep_draw_form('new_product', FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '') . '&action=' . $form_action, 'post', 'enctype="multipart/form-data" id="prodForm"'); ?>
  <div id="dhtmltooltip"></div>

  <script type="text/javascript">
    var tax_rates = new Array();
    <?php
    for ($i = 0, $n = sizeof($tax_class_array); $i < $n; $i++) {
      if ($tax_class_array[$i]['id'] > 0) {
        echo 'tax_rates["' . $tax_class_array[$i]['id'] . '"] = ' . tep_get_tax_rate_value($tax_class_array[$i]['id']) . ';' . "\n";
      }
    }
    ?>

    /***********************************************
     * Cool DHTML tooltip script- � Dynamic Drive DHTML code library (www.dynamicdrive.com)
     * This notice MUST stay intact for legal use
     * Visit Dynamic Drive at http://www.dynamicdrive.com/ for full source code
     ***********************************************/

    var offsetxpoint = -60 //Customize x offset of tooltip
    var offsetypoint = 20 //Customize y offset of tooltip
    var ie = document.all
    var ns6 = document.getElementById && !document.all
    var enabletip = false
    if (ie || ns6)
      var tipobj = document.all ? document.all["dhtmltooltip"] : document.getElementById ? document.getElementById("dhtmltooltip") : ""

    function ietruebody() {
      return (document.compatMode && document.compatMode != "BackCompat") ? document.documentElement : document.body
    }

    function ddrivetip(thetext, thecolor, thewidth) {
      if (ns6 || ie) {
        if (typeof thewidth != "undefined") tipobj.style.width = thewidth + "px"
        if (typeof thecolor != "undefined" && thecolor != "") tipobj.style.backgroundColor = thecolor
        tipobj.innerHTML = thetext
        enabletip = true
        return false
      }
    }

    function positiontip(e) {
      if (enabletip) {
        var curX = (ns6) ? e.pageX : event.clientX + ietruebody().scrollLeft;
        var curY = (ns6) ? e.pageY : event.clientY + ietruebody().scrollTop;
        //Find out how close the mouse is to the corner of the window
        var rightedge = ie && !window.opera ? ietruebody().clientWidth - event.clientX - offsetxpoint : window.innerWidth - e.clientX - offsetxpoint - 20
        var bottomedge = ie && !window.opera ? ietruebody().clientHeight - event.clientY - offsetypoint : window.innerHeight - e.clientY - offsetypoint - 20

        var leftedge = (offsetxpoint < 0) ? offsetxpoint * (-1) : -1000

        //if the horizontal distance isn't enough to accomodate the width of the context menu
        if (rightedge < tipobj.offsetWidth)
          //move the horizontal position of the menu to the left by it's width
          tipobj.style.left = ie ? ietruebody().scrollLeft + event.clientX - tipobj.offsetWidth + "px" : window.pageXOffset + e.clientX - tipobj.offsetWidth + "px"
        else if (curX < leftedge)
          tipobj.style.left = "5px"
        else
          //position the horizontal position of the menu where the mouse is positioned
          tipobj.style.left = curX + offsetxpoint + "px"

        //same concept with the vertical position
        if (bottomedge < tipobj.offsetHeight)
          tipobj.style.top = ie ? ietruebody().scrollTop + event.clientY - tipobj.offsetHeight - offsetypoint + "px" : window.pageYOffset + e.clientY - tipobj.offsetHeight - offsetypoint + "px"
        else
          tipobj.style.top = curY + offsetypoint + "px"
        tipobj.style.visibility = "visible"
      }
    }

    function hideddrivetip() {
      if (ns6 || ie) {
        enabletip = false
        tipobj.style.visibility = "hidden"
        tipobj.style.left = "-1000px"
        tipobj.style.backgroundColor = ''
        tipobj.style.width = ''
      }
    }

    document.onmousemove = positiontip
  </script>
  <script>
    $(document).ready(function() {

      var ctrlDown = false,
        ctrlKey = 17,
        cmdKey = 91,
        vKey = 86,
        cKey = 67;

      $(document).keydown(function(e) {
        if (e.keyCode == ctrlKey || e.keyCode == cmdKey) ctrlDown = true;
      }).keyup(function(e) {
        if (e.keyCode == ctrlKey || e.keyCode == cmdKey) ctrlDown = false;
      });

      $('.num-only').keydown(function(event) {

        if (event.shiftKey == true) {
          event.preventDefault();
        }

        if (ctrlDown && (e.keyCode == vKey || e.keyCode == cKey)) {
          // copy paste is ok
        } else {
          if ((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105) || event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 37 || event.keyCode == 39 || event.keyCode == 46) {
            // numbers ok
          } else {
            event.preventDefault();
          }
        }
      });

      $('.decimal-only').keydown(function(event) {

        if (event.shiftKey == true) {
          event.preventDefault();
        }

        if (ctrlDown && (e.keyCode == vKey || e.keyCode == cKey)) {
          // copy paste is ok
        } else {
          if ((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105) || event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 37 || event.keyCode == 39 || event.keyCode == 46 || event.keyCode == 190) {
            // numbers and decimal ok
          } else {
            event.preventDefault();
          }
        }

        if ($(this).val().indexOf('.') !== -1 && event.keyCode == 190)
          event.preventDefault();

      });

    });
  </script>
  <table border="0" width="100%" cellspacing="0" cellpadding="2">
    <tr>
      <td>
        <table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php echo sprintf(TEXT_NEW_PRODUCT, tep_output_generated_category_path($current_category_id)); ?></td>
            <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
    </tr>
    <tr>
      <td>
        <table border="0" cellspacing="0" cellpadding="2" width="100%">
          <tr>
            <td colspan="2">
              <table>
                <tr>
                  <td>CG</td>
                  <td>TT</td>
                </tr>
                <?php
                if ($pInfo->store_cg == 1) {
                  $prod_checked_cg = 'CHECKED';
                } else {
                  $prod_checked_cg = '';
                }
                if ($pInfo->store_ho == 1) {
                  $prod_checked_ho = 'CHECKED';
                } else {
                  $prod_checked_ho = '';
                }
                if ($pInfo->store_hs == 1) {
                  $prod_checked_hs = 'CHECKED';
                } else {
                  $prod_checked_hs = '';
                }
                if ($pInfo->store_wm == 1) {
                  $prod_checked_wm = 'CHECKED';
                } else {
                  $prod_checked_wm = '';
                }
                if ($pInfo->store_dc == 1) {
                  $prod_checked_dc = 'CHECKED';
                } else {
                  $prod_checked_dc = '';
                }
                if ($pInfo->store_tt == 1) {
                  $prod_checked_tt = 'CHECKED';
                } else {
                  $prod_checked_tt = '';
                }

                if ($pInfo->store_cg == '') {
                  $prod_checked_cg = 'CHECKED';
                } // set CG as default for new products
                ?>
                <tr>
                  <td><?php echo tep_draw_ms_checkbox_field('store_cg', '1', $prod_checked_cg); ?></td>
                  <td><?php echo tep_draw_ms_checkbox_field('store_tt', '1', $prod_checked_tt); ?></td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <?php echo tep_draw_hidden_field('products_status', $pInfo->products_status); ?>
          <script language="javascript">
            <!--
            var dateAvailable = new ctlSpiffyCalendarBox("dateAvailable", "new_product", "products_date_available", "btnDate1", "<?php echo $pInfo->products_date_available; ?>", scBTNMODE_CUSTOMBLUE);
            //
            -->
          </script>
          <tr>
            <td colspan="2"><?php echo TEXT_PRODUCTS_DATE_AVAILABLE; ?><br /><small>(YYYY-MM-DD)</small> <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;'; ?><script language="javascript">
                dateAvailable.writeControl();
                dateAvailable.dateFormat = "yyyy-MM-dd";
              </script> Please make sure stock is not zero when creating pre-orders.</td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td colspan="2">
              <div id="wrapper">
                <ul class="tabs">
                  <li><a href="#" class="defaulttab" rel="tabs1">CG</a></li>
                  <li><a href="#" rel="tabs8">TT</a></li>
                </ul>

                <div class="tab-content" id="tabs1">
                  <table border="0" cellspacing="0" cellpadding="2" width="100%">
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td width="130">Product Name:</td>
                        <td>
                          <?php echo tep_draw_input_field('products_name[' . $languages[$i]['id'] . ']', (isset($products_name[$languages[$i]['id']]) ? $products_name[$languages[$i]['id']] : tep_get_products_name($pInfo->products_id, $languages[$i]['id'])), 'style="width: 500px;"');
                          $products_name = tep_get_products_name($pInfo->products_id, $languages[$i]['id']); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                    <tr>
                      <td colspan="2"><strong style="float: left; margin: 10px 0; width: 100%;">Meta Tags (optional):</strong></td>
                    </tr>
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>Page Title:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('products_head_title_tag[' . $languages[$i]['id'] . ']', 'soft', '70', '1', (isset($products_head_title_tag[$languages[$i]['id']]) ? stripslashes($products_head_title_tag[$languages[$i]['id']]) : tep_get_products_head_title_tag($pInfo->products_id, $languages[$i]['id']))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>Meta Description:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('products_head_desc_tag[' . $languages[$i]['id'] . ']', 'soft', '70', '1', (isset($products_head_desc_tag[$languages[$i]['id']]) ? stripslashes($products_head_desc_tag[$languages[$i]['id']]) : tep_get_products_head_desc_tag($pInfo->products_id, $languages[$i]['id']))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>Meta Keywords:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('products_head_keywords_tag[' . $languages[$i]['id'] . ']', 'soft', '70', '1', (isset($products_head_keywords_tag[$languages[$i]['id']]) ? stripslashes($products_head_keywords_tag[$languages[$i]['id']]) : tep_get_products_head_keywords_tag($pInfo->products_id, $languages[$i]['id']))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                  </table>
                </div>
                <div class="tab-content" id="tabs2">

                  <table border="0" cellspacing="0" cellpadding="2" width="100%">
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td width="130">HO Product Name:</td>
                        <td>
                          <?php echo tep_draw_input_field('ho_products_name', (isset($ho_products_name) ? $ho_products_name : tep_get_extra_products_name($pInfo->products_id, '2')), 'style="width: 500px;"'); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                    <tr>
                      <td colspan="2"><strong style="float: left; margin: 10px 0; width: 100%;">Meta Tags (optional):</strong></td>
                    </tr>
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>HO Page Title:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('ho_products_head_title_tag', 'soft', '70', '1', (isset($ho_products_head_title_tag) ? $ho_products_head_title_tag : tep_get_extra_products_head_title_tag($pInfo->products_id, '2'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>HO Meta Description:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('ho_products_head_desc_tag', 'soft', '70', '1', (isset($ho_products_head_desc_tag) ? $ho_products_head_desc_tag : tep_get_extra_products_head_desc_tag($pInfo->products_id, '2'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>HO Meta Keywords:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('ho_products_head_keywords_tag', 'soft', '70', '1', (isset($ho_products_head_keywords_tag) ? $ho_products_head_keywords_tag : tep_get_extra_products_head_keywords_tag($pInfo->products_id, '2'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                  </table>
                </div>

                <div class="tab-content" id="tabs6">

                  <table border="0" cellspacing="0" cellpadding="2" width="100%">
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td width="130">WM Product Name:</td>
                        <td>
                          <?php echo tep_draw_input_field('wm_products_name', (isset($wm_products_name) ? $wm_products_name : tep_get_extra_products_name($pInfo->products_id, '6')), 'style="width: 500px;"'); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                    <tr>
                      <td colspan="2"><strong style="float: left; margin: 10px 0; width: 100%;">Meta Tags (optional):</strong></td>
                    </tr>
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>WM Page Title:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('wm_products_head_title_tag', 'soft', '70', '1', (isset($wm_products_head_title_tag) ? $wm_products_head_title_tag : tep_get_extra_products_head_title_tag($pInfo->products_id, '6'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>WM Meta Description:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('wm_products_head_desc_tag', 'soft', '70', '1', (isset($wm_products_head_desc_tag) ? $wm_products_head_desc_tag : tep_get_extra_products_head_desc_tag($pInfo->products_id, '6'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>WM Meta Key Words:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('wm_products_head_keywords_tag', 'soft', '70', '1', (isset($wm_products_head_keywords_tag) ? $wm_products_head_keywords_tag : tep_get_extra_products_head_keywords_tag($pInfo->products_id, '6'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                  </table>


                </div>
                <div class="tab-content" id="tabs7">

                  <table border="0" cellspacing="0" cellpadding="2" width="100%">
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td width="130">DC Product Name:</td>
                        <td>
                          <?php echo tep_draw_input_field('af_products_name', (isset($af_products_name) ? $af_products_name : tep_get_extra_products_name($pInfo->products_id, '7')), 'style="width: 500px;"'); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                    <tr>
                      <td colspan="2"><strong style="float: left; margin: 10px 0; width: 100%;">Meta Tags (optional):</strong></td>
                    </tr>
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>DC Page Title:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('af_products_head_title_tag', 'soft', '70', '1', (isset($af_products_head_title_tag) ? $af_products_head_title_tag : tep_get_extra_products_head_title_tag($pInfo->products_id, '7'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>DC Meta Description:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('af_products_head_desc_tag', 'soft', '70', '1', (isset($af_products_head_desc_tag) ? $af_products_head_desc_tag : tep_get_extra_products_head_desc_tag($pInfo->products_id, '7'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>DC Meta Key Words:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('af_products_head_keywords_tag', 'soft', '70', '1', (isset($af_products_head_keywords_tag) ? $af_products_head_keywords_tag : tep_get_extra_products_head_keywords_tag($pInfo->products_id, '7'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                  </table>


                </div>
                <div class="tab-content" id="tabs8">

                  <table border="0" cellspacing="0" cellpadding="2" width="100%">
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td width="130">TT Product Name:</td>
                        <td>
                          <?php echo tep_draw_input_field('tt_products_name', (isset($tt_products_name) ? $tt_products_name : tep_get_extra_products_name($pInfo->products_id, '8')), 'style="width: 500px;"'); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                    <tr>
                      <td colspan="2"><strong style="float: left; margin: 10px 0; width: 100%;">Meta Tags (optional):</strong></td>
                    </tr>
                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>TT Page Title:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('tt_products_head_title_tag', 'soft', '70', '1', (isset($tt_products_head_title_tag) ? $tt_products_head_title_tag : tep_get_extra_products_head_title_tag($pInfo->products_id, '8'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>TT Meta Description:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('tt_products_head_desc_tag', 'soft', '70', '1', (isset($tt_products_head_desc_tag) ? $tt_products_head_desc_tag : tep_get_extra_products_head_desc_tag($pInfo->products_id, '8'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>

                    <?php
                    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    ?>
                      <tr>
                        <td>TT Meta Key Words:</td>
                        <td>
                          <?php echo tep_draw_textarea_field('tt_products_head_keywords_tag', 'soft', '70', '1', (isset($tt_products_head_keywords_tag) ? $tt_products_head_keywords_tag : tep_get_extra_products_head_keywords_tag($pInfo->products_id, '8'))); ?>
                        </td>
                      </tr>
                    <?php
                    }
                    ?>
                  </table>
                </div>
              </div>

            </td>
          </tr>
          <tr>
            <td colspan="2">
              <script>
                function ghost_boxes(product_type) {

                  // alert(product_type);

                  $('[name="products_price"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="products_price_gross"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");

                  $('[name="ho_products_price"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="ho_products_price_gross"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="hs_products_price"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="hs_products_price_gross"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="wm_products_price"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="wm_products_price_gross"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="af_products_price"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="af_products_price_gross"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="tt_products_price"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="tt_products_price_gross"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");

                  $('[name="products_trade_price"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");
                  $('[name="products_trade_price_discount"]').prop("readonly", true).css("background-color", "#EEE").css("color", "#666");

                  $('.bulk-cost-price').show();
                  $('.group-update-box').hide();
                  if (product_type != '9999') { // loose tobacco (removed)
                    $('#attributeManager').hide();
                  } else {
                    $('#attributeManager').show();
                  }
                  $('.tobacco-edit-button').hide();
                }

                function un_ghost_boxes() {
                  $('[name="products_price"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="products_price_gross"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");

                  $('[name="ho_products_price"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="ho_products_price_gross"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="hs_products_price"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="hs_products_price_gross"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="wm_products_price"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="wm_products_price_gross"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="af_products_price"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="af_products_price_gross"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="tt_products_price"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="tt_products_price_gross"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");

                  $('[name="products_trade_price"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");
                  $('[name="products_trade_price_discount"]').prop("readonly", false).css("background-color", "#FFF").css("color", "#222");

                  $('.bulk-cost-price').hide();
                  $('.group-update-box').show();
                  $('#attributeManager').show();
                  $('.base-amount-grams').text('');
                  $('.tobacco-edit-button').hide();
                }

                function calc_tobacco_boxes() {

                  <?php echo $jquery_function_string; ?>

                  var entered_amount = $('input[name="bulk_cost_price"]').val();

                  var price_per_gram = (entered_amount / bulk_amount_grams);

                  var new_trade_price = parseFloat((price_per_gram * base_amount_grams));

                  var new_products_price = parseFloat((((base_amount_margin / 100) * new_trade_price) + new_trade_price) * 1.2);

                  $('input[name="products_price"]').val(new_products_price.toFixed(4));
                  $('input[name="products_price_gross"]').val(new_products_price.toFixed(4));
                  $('input[name="products_trade_price"]').val(new_trade_price.toFixed(4));
                  $('input[name="products_trade_price_discount"]').val('0');

                  $('.base-amount-grams').text('(' + base_amount_grams + ' grams)');
                  $('input[name="products_price_per_gram"]').val(price_per_gram);

                }

                function startup_calc_tobacco_boxes() {

                  <?php echo $jquery_function_string; ?>

                  $('select[name="product_type"]').prop('selected', function() {
                    <?php echo $jquery_string; ?>
                  });


                }

                $(document).ready(function() {

                  // page load
                  <?php if ($current_product_type > '1') {  ?>

                    ghost_boxes(<?php echo $current_product_type; ?>);
                    startup_calc_tobacco_boxes();

                  <?php } else { ?>

                    un_ghost_boxes();

                  <?php } ?>

                  // select box
                  $('select[name="product_type"]').on('change', function() {
                    if ($(this).val() > '1') {
                      ghost_boxes($(this).val());
                      calc_tobacco_boxes();
                      <?php echo $jquery_string; ?>
                    } else {
                      un_ghost_boxes();
                    }
                  });

                  // input box
                  $('input[name="bulk_cost_price"]').on('input', function() {
                    calc_tobacco_boxes();
                  });


                });
              </script>
              <?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?>
            </td>
          </tr>
          <tr>
            <td class="main" colspan="2">Product Type:<?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('product_type', $product_type_array, $current_product_type) . tep_draw_separator('pixel_trans.gif', '24', '15'); ?><span class="bulk-cost-price"> Bulk Trade Price (ex vat): <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '' . tep_draw_input_field('bulk_cost_price', '', ''); ?> <span class="bulk-text"></span>&nbsp;&nbsp;<?php echo tep_draw_button('Save', 'disk', null, 'primary', array('params' => 'type="submit" name="return" value="1"')); ?> </span>
              <span class="tobacco-edit-button"><u><a class="update-tobacco-prices" href="<?php echo tep_href_link('quick_tobacco_price_updates.php', 'single_product_id=' . $pInfo->products_id); ?>">Update Tobacco Prices</a></u></span>
            </td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_hidden_field('products_price_per_gram', '') . tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td colspan="2">
              <?php
              $quick_products_extra_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . $pInfo->products_id . "'");

              if (tep_db_num_rows($quick_products_extra_query)) {
                $ho_extra_price = tep_get_extra_products_price($pInfo->products_id, '2');
                $hs_extra_price = tep_get_extra_products_price($pInfo->products_id, '3');
                $wm_extra_price = tep_get_extra_products_price($pInfo->products_id, '6');
                $af_extra_price = tep_get_extra_products_price($pInfo->products_id, '7');
                $tt_extra_price = tep_get_extra_products_price($pInfo->products_id, '8');
              } else {
                $ho_extra_price = '';
                $hs_extra_price = '';
                $wm_extra_price = '';
                $af_extra_price = '';
                $tt_extra_price = '';
              }

              $arr = array($ho_extra_price, $hs_extra_price, $wm_extra_price, $af_extra_price, $tt_extra_price);

              if (count(array_unique($arr)) == 1) {
                $total_extra_price = $af_extra_price;
              } else {
                $total_extra_price = 'Various Prices Set';
              }

              ?>
              <div id="wrapper">

                <ul class="pricetabs">
                  <li><a href="#" class="defaultpricetab" rel="pricetabs1">CG</a></li>
                  <li><a href="#" rel="pricetabs6">WM</a></li>
                </ul>

                <div class="pricetab-content" id="pricetabs1">
                  <table>
                    <tr bgcolor="#ebebff">
                      <td class="main">Tax Class</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_tax_class_id', $tax_class_array, $pInfo->products_tax_class_id, 'onchange="updateGross()"'); ?></td>
                    </tr>
                    <tr bgcolor="#ebebff">
                      <td class="main">Products Price (Net)</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_price', $pInfo->products_price, 'onKeyUp="updateGross()"') . tep_draw_hidden_field('products_price_old', $pInfo->products_price); ?> <span class="base-amount-grams"></span></td>
                    </tr>
                    <tr bgcolor="#ebebff">
                      <td class="main">Products Price (Gross)</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_price_gross', $pInfo->products_price, 'OnKeyUp="updateNet()"'); ?> <span class="base-amount-grams"></span></td>
                    </tr>
                  </table>
                </div>

                <div class="pricetab-content" id="pricetabs6">
                  <table>
                    <tr bgcolor="#ebebff">
                      <td class="main">WM Tax Class</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('wm_products_tax_class_id', $tax_class_array, (isset($pInfo->wm_products_tax_class_id) ? $pInfo->wm_products_tax_class_id : tep_get_extra_products_tax_class_id($pInfo->products_id, '6')), 'onchange="wm_updateGross()"'); ?></td>
                    </tr>
                    <tr bgcolor="#ebebff">
                      <td class="main">WM Products Price (Net)</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('wm_products_price', $wm_extra_price, 'onKeyUp="wm_updateGross()" id="wm_products_price"'); ?></td>
                    </tr>
                    <tr bgcolor="#ebebff">
                      <td class="main">WM Products Price (Gross)</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('wm_products_price_gross', $pInfo->wm_products_price, 'OnKeyUp="wm_updateNet()"'); ?></td>
                    </tr>
                  </table>
                </div>

              </div>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <div class="group-update-box">
                <table>
                  <tr>
                    <td class="main" width="146" align="left">Trade Price (ex vat):</td>
                    <td class="main" align="left"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_trade_price', $pInfo->products_trade_price, '') . tep_draw_hidden_field('products_trade_price_old', $pInfo->products_trade_price); ?> <span class="base-amount-grams"></span></td>
                  </tr>
                  <tr>
                    <td class="main" width="146" align="left">Trade Price Discount:</td>
                    <td class="main" align="left"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_trade_price_discount', number_format((float)$pInfo->products_trade_price_discount, 2), '') . tep_draw_hidden_field('products_trade_price_discount_old', number_format((float)$pInfo->products_trade_price_discount, 2)); ?> %</td>
                  </tr>
                  <?php

                  if (!is_numeric($pInfo->products_price)) {
                    $pInfo->products_price = 0;
                  }
                  if (!is_numeric($pInfo->products_trade_price)) {
                    $pInfo->products_trade_price = 0;
                  }

                  $sale_price_ex_vat = $pInfo->products_price / 1.2;
                  $trade_price_ex_vat = $pInfo->products_trade_price;
                  $trade_price_discount = $pInfo->products_trade_price_discount;

                  $profit_calc_text = '';
                  $profit_calc_text .= 'Sale Price (Ex Vat) : &pound;' . number_format((float)$sale_price_ex_vat, 2) . '<br />';
                  $profit_calc_text .= 'Trade Price (Ex Vat) : &pound;' . number_format((float)$trade_price_ex_vat, 2) . '<br />';

                  if ($trade_price_discount != 0) {
                    $trade_price_discount_amount = $trade_price_ex_vat * ($trade_price_discount / 100);
                    $trade_price_ex_vat_with_discount = $trade_price_ex_vat - $trade_price_discount_amount;

                    $profit_calc_text .= 'Trade Price Discount : ' . number_format((float)$trade_price_discount, 2) . '% of &pound;' . number_format((float)$trade_price_ex_vat, 2) . ' = &pound;' . number_format((float)$trade_price_discount_amount, 2) . '<br />';
                    $profit_calc_text .= 'Trade Price with Discount : &pound;' . number_format((float)$trade_price_ex_vat_with_discount, 2) . '<br />';
                    $profit_in_sterling = $sale_price_ex_vat - $trade_price_ex_vat_with_discount;
                  } else {
                    $trade_price_ex_vat_with_discount = $trade_price_ex_vat;
                    $profit_in_sterling = $sale_price_ex_vat - $trade_price_ex_vat;
                  }

                  if ($sale_price_ex_vat) {
                    $profit_margin = ($profit_in_sterling / $sale_price_ex_vat) * 100;
                  } else {
                    $profit_margin = 0;
                  }

                  $profit_calc_text .= 'Profit in Sterling (Ex Vat) : &pound;' . number_format((float)$profit_in_sterling, 2) . '<br /><br />';

                  $profit_calc_text .= 'Profit Margin (&pound;' . number_format((float)$profit_in_sterling, 2) . ' Profit / &pound;' . number_format((float)$sale_price_ex_vat, 2) . ' Sale Price) x 100 : <strong>' . number_format((float)$profit_margin, 2) . '%</strong><br />';



                  ?>
                  <tr>
                    <td class="main" width="146" align="left">Profit Margin:</td>
                    <td class="main" align="left"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15'); ?>
                      <span style="cursor: pointer;" onMouseover="ddrivetip('<?php echo $profit_calc_text; ?>', 'white', 300)" ; onMouseout="hideddrivetip()"><?php echo number_format((float)$profit_margin, 2); ?>%</span>
                    </td>
                  </tr>
                </table>
              </div>
            </td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <script language="javascript">
            <!--
            updateGross();
            /*
            ho_updateGross();
            hs_updateGross();
            wm_updateGross();
            af_updateGross();
            tt_updateGross();
            */
            wm_updateGross();
            //
            -->
          </script>
          <!-- HTC BOC //-->
          <tr>
            <td colspan="2">

              <div id="wrapper">

                <ul class="maintabs">
                  <li><a href="#" class="defaultmaintab" rel="maintabs1">Product Description</a></li>
                  <li><a href="#" rel="maintabs2">Tasting Notes</a></li>
                  <li><a href="#" rel="maintabs3">Taste Test</a></li>
                  <li><a href="#" rel="maintabs4">Awards</a></li>
                  <li><a href="#" rel="maintabs5">Read More</a></li>
                </ul>

                <div class="maintab-content" id="maintabs1">
                  <!-- START PRODUCT DESCRIPTION -->
                  <div id="wrapper">
                    <ul class="desctabs">
                      <li><a href="#" class="defaultdesctab" rel="desctabs1">CG</a></li>
                      <li><a href="#" rel="desctabs8">TT</a></li>
                    </ul>

                    <div class="desctab-content" id="desctabs1">
                      <?php
                      for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                      ?>
                        <table border="0" cellspacing="0" cellpadding="0">
                          <tr>
                            <td class="main" valign="top"></td>
                            <td class="main"><?php echo tep_draw_textarea_field('products_description[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (isset($products_description[$languages[$i]['id']]) ? stripslashes($products_description[$languages[$i]['id']]) : tep_get_products_description($pInfo->products_id, $languages[$i]['id'])), 'class="tinymce"'); ?></td>
                          </tr>
                        </table>
                      <?php
                      }
                      ?>
                    </div>
                    <div class="desctab-content" id="desctabs2">
                      <table>
                        <tr>
                          <td class="main">HO Description</td>
                          <td class="main"><?php echo tep_draw_textarea_field('ho_products_description', 'soft', '70', '15', (isset($ho_products_description) ? $ho_products_description : tep_get_extra_products_description($pInfo->products_id, '2'))); ?></td>
                        </tr>
                      </table>
                    </div>
                    <div class="desctab-content" id="desctabs3">
                      <table>
                        <tr>
                          <td class="main">HS Description</td>
                          <td class="main"><?php echo tep_draw_textarea_field('hs_products_description', 'soft', '70', '15', (isset($hs_products_description) ? $hs_products_description : tep_get_extra_products_description($pInfo->products_id, '3'))); ?></td>
                        </tr>
                      </table>
                    </div>
                    <div class="desctab-content" id="desctabs6">
                      <table>
                        <tr>
                          <td class="main">WM Description</td>
                          <td class="main"><?php echo tep_draw_textarea_field('wm_products_description', 'soft', '70', '15', (isset($wm_products_description) ? $wm_products_description : tep_get_extra_products_description($pInfo->products_id, '6'))); ?></td>
                        </tr>
                      </table>
                    </div>
                    <div class="desctab-content" id="desctabs7">
                      <table>
                        <tr>
                          <td class="main">DC Description</td>
                          <td class="main"><?php echo tep_draw_textarea_field('af_products_description', 'soft', '70', '15', (isset($af_products_description) ? $af_products_description : tep_get_extra_products_description($pInfo->products_id, '7'))); ?></td>
                        </tr>
                      </table>
                    </div>
                    <div class="desctab-content" id="desctabs8">
                      <table>
                        <tr>
                          <td class="main">TT Description</td>
                          <td class="main"><?php echo tep_draw_textarea_field('tt_products_description', 'soft', '70', '15', (isset($tt_products_description) ? $tt_products_description : tep_get_extra_products_description($pInfo->products_id, '8'))); ?></td>
                        </tr>
                      </table>
                    </div>
                  </div>
                  <!-- END PRODUCT DESCRIPTION -->
                </div>
                <div class="maintab-content" id="maintabs2">
                  <!-- START TASTING NOTES -->
                  <table border="0" cellspacing="0" cellpadding="0">
                    <tr>
                      <td class="main" valign="top">
                        <?php
                        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                        ?>
                          <table border="0" cellspacing="0" cellpadding="0">
                            <tr>
                              <td class="main"><?php echo tep_draw_textarea_field('products_tasting_notes[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (isset($products_tasting_notes[$languages[$i]['id']]) ? stripslashes($products_tasting_notes[$languages[$i]['id']]) : tep_get_products_tasting_notes($pInfo->products_id, $languages[$i]['id'])), 'class="tinymce"'); ?></td>
                            </tr>
                          </table>
                        <?php
                        }
                        ?>
                      </td>
                    </tr>
                  </table>
                  <!-- END TASTING NOTES -->
                </div>
                <div class="maintab-content" id="maintabs3">
                  <!-- START TASTE TEST -->
                  <?php
                  for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                  ?>
                    <table border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td class="main"><?php echo tep_draw_textarea_field('products_taste_test[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (isset($products_taste_test[$languages[$i]['id']]) ? stripslashes($products_taste_test[$languages[$i]['id']]) : tep_get_products_taste_test($pInfo->products_id, $languages[$i]['id'])), 'class="tinymce"'); ?></td>
                      </tr>
                    </table>
                  <?php
                  }
                  ?>
                  <!-- END TASTE TEST -->
                </div>
                <div class="maintab-content" id="maintabs4">
                  <!-- START REWARDS -->
                  <?php
                  for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                  ?>
                    <table border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td class="main"><?php echo tep_draw_textarea_field('products_awards[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (isset($products_awards[$languages[$i]['id']]) ? stripslashes($products_awards[$languages[$i]['id']]) : tep_get_products_awards($pInfo->products_id, $languages[$i]['id'])), 'class="tinymce"'); ?></td>
                      </tr>
                    </table>
                  <?php
                  }
                  ?>
                  <!-- END REWARDS -->
                </div>
                <div class="maintab-content" id="maintabs5">
                  <!-- START REWARDS -->
                  <?php
                  for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                  ?>
                    <table border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td class="main"><?php echo tep_draw_textarea_field('products_read_more[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (isset($products_read_more[$languages[$i]['id']]) ? stripslashes($products_read_more[$languages[$i]['id']]) : tep_get_products_read_more($pInfo->products_id, $languages[$i]['id'])), 'class="tinymce"'); ?></td>
                      </tr>
                    </table>
                  <?php
                  }
                  ?>
                  <!-- END REWARDS -->
                </div>
              </div>
              <br /><br />
              <div id="wrapper">

                <ul class="secondarytabs">
                  <li><a href="#" class="defaultsecondarytab" rel="secondarytabs1">General Options</a></li>
                  <li><a href="#" rel="secondarytabs2">Cigar Options</a></li>
                  <li><a href="#" rel="secondarytabs3">Whisky Options</a></li>
                  <li><a href="#" rel="secondarytabs4">Sampler Options</a></li>
                  <li><a href="#" rel="secondarytabs5">Event Options</a></li>
                </ul>

                <div class="secondarytab-content" id="secondarytabs1">
                  <script>
                    $(document).ready(function() {

                      $("select[name*='products_cigar']").change(function() {
                        if ($(this).val() == 1) {
                          $("#tdb2 .ui-button-text").html('Update <strong>Non Tobacco</strong> Product');
                          $("#tdb2 .ui-button-text strong").addClass('flashing');

                          $(".flashing").each(function() {
                            var elem = $(this);
                            setInterval(function() {
                              if (elem.css('visibility') == 'hidden') {
                                elem.css('visibility', 'visible');
                              } else {
                                elem.css('visibility', 'hidden');
                              }
                            }, 1000);
                          });
                        } else {
                          $("#tdb2 .ui-button-text").html('Update');
                          $("#tdb2 .ui-button-text strong").removeClass('flashing');
                        }
                      });
                    });

                    var form = "";
                    var submitted = false;
                    var error = false;
                    var error_message = "";

                    function check_select(field_name, field_default, message) {
                      if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
                        var field_value = form.elements[field_name].value;

                        if (field_value == field_default) {
                          error_message = error_message + "* " + message + "\n";
                          error = true;
                        }
                      }
                    }
                  </script>


                  <table>
                    <tr>
                      <td class="main">Is this a tobacco product?</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_cigar', $tobacco_array, $pInfo->products_cigar); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Allow Earning of Points?</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_earn_points', $earn_points_array, $pInfo->products_earn_points); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Remove VAT outside UK?</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_vat_deductable', $vat_deductable_array, $pInfo->products_vat_deductable); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Allow PayPal?</td>
                      <td class="main"><?php

                                        if ($pInfo->products_paypal != 1) { // does not equal PayPal YES
                                          $products_paypal_selection = 2; // 2 default rather than 0
                                        }

                                        echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_paypal', $paypal_dropdown_array, $pInfo->products_paypal) . tep_draw_hidden_field('old_products_paypal', $pInfo->products_paypal); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Hide Product?</td>
                      <td class="main"><?php
                                        echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('hide_navigation', $hide_navigation_array, $pInfo->hide_navigation);

                                        if ($pInfo->hide_navigation == '1') {
                                          echo ' <a href="' . tep_catalog_href_link('product_info.php', 'products_id=' . $pInfo->products_id, 'SSL') . '" target="_blank">prod link</a> | <a href="' . tep_catalog_href_link('cgars_hidden.php', '', 'SSL') . '" target="_blank">all hidden</a>';
                                        }

                                        ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Order Type:</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_order_type', $order_type_array, $pInfo->products_order_type); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Shipping:</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_free_shipping', $free_shipping_array, $pInfo->products_free_shipping); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main"><?php echo TEXT_PRODUCTS_GEO_ZONES; ?></td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('products_geo_zones', $geo_zones_array, $pInfo->products_geo_zones); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Supplier:</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('suppliers_id', $suppliers_array, $pInfo->suppliers_id); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Add on items category:</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('prod_add_on_cat_id', tep_get_category_tree(), $pInfo->prod_add_on_cat_id); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main"><?php echo TEXT_PRODUCTS_MODEL; ?></td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_model', $pInfo->products_model); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <?php run_hook('instant_mpn_checker'); ?>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main"><?php echo TEXT_PRODUCTS_WEIGHT; ?></td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_weight', $pInfo->products_weight); ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">"New In" page status:</td>
                      <td class="main"><?php
                                        echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_pull_down_menu('hide_new_in', $hide_new_in_array, $pInfo->hide_new_in);
                                        ?></td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Product Tags (comma separated):</td>
                      <td class="main">
                        <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('tags', (isset($pInfo->product_tags) ? $pInfo->product_tags : ''), 'style="width: 400px;" placeholder="e.g. fathers day, halloween, christmas"'); ?>
                        <span style="color: #888; font-size: 11px;">For search occasions like 'fathers day', 'halloween', etc.</span>
                      </td>
                    </tr>
                    <tr>
                      <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                  </table>


                </div>

                <div class="secondarytab-content" id="secondarytabs2">

                  <table>
                    <tr>
                      <td class="main" bgcolor="#ebebff">Cigar Ring Gauge:</td>
                      <td class="main" bgcolor="#ebebff"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_cigar_ring_gauge', $pInfo->products_cigar_ring_gauge); ?></td>
                      <td class="main" bgcolor="#ebebff"> e.g <strong>47</strong></td>
                    </tr>
                    <tr>
                      <td colspan="3"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <script language="javascript">
                      <!--
                      function toDecimal(x) {
                        if (x.indexOf('/') != -1) {
                          var parts = x.split(" ")
                          var decParts;
                          if (parts.length > 1) {
                            decParts = parts[1].split("/");
                          } else {
                            decParts = parts[0].split("/");
                            parts[0] = 0;
                          }

                          return parseInt(parts[0], 10) + (parseInt(decParts[0], 10) / parseInt(decParts[1], 10))

                          // 1 inch = 25.4 mm
                          // return (parseInt(parts[0], 10) * 25.4) + ((parseInt(decParts[0], 10) / parseInt(decParts[1], 10)) * 25.4)


                        } else {
                          return x
                        }
                      }

                      function update_decimal() {
                        document.forms["new_product"].products_cigar_length.value = toDecimal(document.forms["new_product"].products_cigar_length_fraction.value);
                      }


                      function toFraction(x) {
                        var n = x;
                        if (!isNaN(n) && n !== '' && n !== '0' && n !== '0.0000000000' && n !== 'Infinity' && n !== '-Infinity') {
                          var tolerance = 1.0E-6;
                          var h1 = 1;
                          var h2 = 0;
                          var k1 = 0;
                          var k2 = 1;
                          do {
                            var a = Math.floor(n);
                            var aux = h1;
                            h1 = a * h1 + h2;
                            h2 = aux;
                            aux = k1;
                            k1 = a * k1 + k2;
                            k2 = aux;
                            n = 1 / (n - a);
                          } while (Math.abs(x - h1 / k1) > x * tolerance);


                          var num = h1;
                          var den = k1;

                          var num2 = num % den;
                          var den2 = den;
                          var fr = (num - num2) / den;

                          if (fr !== 0) {
                            if (num2 !== 0) {
                              return '' + fr + ' ' + num2 + '/' + den2 + '';
                            } else {
                              return '' + fr + '';
                            }
                          } else {
                            return '' + num + '/' + den + '';
                          }

                        } else {
                          return 0;
                        }
                      }

                      function update_fraction() {
                        document.forms["new_product"].products_cigar_length_fraction.value = toFraction(document.forms["new_product"].products_cigar_length.value);
                      }

                      //
                      -->
                    </script>
                    <tr bgcolor="#ebebff">
                      <td class="main">Cigar length: </td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_cigar_length_fraction', '', 'OnKeyUp="update_decimal()"'); ?> </td>
                      <td> e.g <strong>9 1/4</strong> <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_cigar_length', $pInfo->products_cigar_length, 'style="color: #fff; background: none; border: none;" OnKeyUp="update_fraction()" '); ?></td>
                    </tr>
                    <tr>
                      <td colspan="3"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main" bgcolor="#ebebff">Cigar Smoke Time:</td>
                      <td class="main" bgcolor="#ebebff"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_cigar_smoke_time', $pInfo->products_cigar_smoke_time); ?></td>
                      <td class="main" bgcolor="#ebebff"> <strong>Minutes</strong></td>
                    </tr>
                    <tr>
                      <td colspan="3"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <?php echo tep_display_additional_info($_GET['pID'], 1); ?>
                  </table>

                </div>
                <div class="secondarytab-content" id="secondarytabs3">
                  <table>
                    <tr>
                      <td class="main">Max Order: (0 = unlimited) &nbsp;&nbsp;</td>
                      <td class="main"><?php echo tep_draw_input_field('products_max_order', $pInfo->products_max_order, 'class="num-only"'); ?></td>
                      <td class="main"></td>
                    </tr>
                    <tr>
                      <td colspan="3"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <?php echo tep_display_additional_info($_GET['pID'], 2); ?>
                    <tr>
                      <td colspan="3"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                  </table>


                </div>

                <div class="secondarytab-content" id="secondarytabs4">
                  <?php require(DIR_WS_MODULES . 'samplers_and_bundles.php'); ?>
                </div>

                <div class="secondarytab-content" id="secondarytabs5">
                  <table>
                    <tr>
                      <td colspan="3"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                    <tr>
                      <td class="main">Event Date &amp; Time: </td>
                      <td class="main"><?php echo tep_draw_input_field('products_event_date', $pInfo->products_event_date, 'id="products_event_date"'); ?>
                        <a href="javascript:NewCal('products_event_date','ddmmmyyyy',true,24)"><img src="cal.gif" width="16" height="16" border="0" alt="Pick a date"></a>
                      </td>
                      <td class="main"></td>
                    </tr>
                    <tr>
                      <td colspan="3"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                    </tr>
                  </table>
                </div>

              </div>

            </td>
          </tr>
        </table>

        <script language="javascript">
          <!--
          // run it!
          update_fraction();
          //
          -->
        </script>

        <br /><br />

        <table>
          <tr>
            <td colspan="2" class="main">
              <hr>
            </td>
          </tr>
          <!-- HTC EOC //-->
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td class="main">Stock:</td>
            <td class="main">
              <table>
                <tr>
                  <td align="center">Transfer Only</td>
                  <td align="center">Liverpool</td>
                  <td align="center">Lpl. Stashed</td>
                  <td align="center">Chester</td>
                  <td align="center">C. Stashed</td>
                  <td align="center">London</td>
                  <td align="center">St James</td>
                  <td align="center">Mayfair</td>
                  <td align="center">Norfolk</td>
                  <td align="center">N. Stashed</td>
                  <td align="center">Knutsford</td>
                  <td align="center">Leeds</td>
                  <td align="center">Lds. Stashed</td>
                  <td align="center">Mash Tun</td>
                  <td align="center">MT. Stashed</td>
                  <td align="center">Lime Street</td>
                  <td align="center">L. Stashed</td>
                  <td align="center">Edinburgh</td>
                  <td align="center">Ed. Stashed</td>
                  <td align="center">Drop Ship</td>
                  <td align="center">Total Quantity</td>
                </tr>
                <tr>
                  <td align="center"><?php echo tep_draw_ms_checkbox_field('stock_transfer', '1', ''); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_liverpool', $pInfo->stock_liverpool, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_liverpool_stashed', $pInfo->stock_liverpool_stashed, 'size="4"'); // dont total up this stock value 
                                      ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_chester', $pInfo->stock_chester, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_chester_stashed', $pInfo->stock_chester_stashed, 'size="4"'); // dont total up this stock value 
                                      ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_london', $pInfo->stock_london, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_london_cg', $pInfo->stock_london_cg, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_mayfair', $pInfo->stock_mayfair, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_norfolk', $pInfo->stock_norfolk, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_norfolk_stashed', $pInfo->stock_norfolk_stashed, 'size="4"'); // dont total up this stock value 
                                      ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_knutsford', $pInfo->stock_knutsford, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_leeds', $pInfo->stock_leeds, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_leeds_stashed', $pInfo->stock_leeds_stashed, 'size="4"');  // dont total up this stock value 
                                      ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_mashtun', $pInfo->stock_mashtun, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_mashtun_stashed', $pInfo->stock_mashtun_stashed, 'size="4"');  // dont total up this stock value 
                                      ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_lime_street', $pInfo->stock_lime_street, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_lime_street_stashed', $pInfo->stock_lime_street_stashed, 'size="4"'); // dont total up this stock value 
                                      ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_edinburgh', $pInfo->stock_edinburgh, 'size="4" class="costpriceinput"'); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('stock_edinburgh_stashed', $pInfo->stock_edinburgh_stashed, 'size="4"'); // dont total up this stock value 
                                      ?></td>
                  <?php if ($pInfo->stock_supplier == 1) {
                    $prod_checked_supplier = 'CHECKED';
                  } else {
                    $prod_checked_supplier = '';
                  } ?>
                  <td align="center"><?php echo tep_draw_ms_checkbox_field('stock_supplier', '1', $prod_checked_supplier); ?></td>
                  <td align="center"><?php echo tep_draw_input_field('products_quantity', $pInfo->products_quantity, 'size="10" class="grandtotal"') . tep_draw_hidden_field('old_products_quantity', $pInfo->products_quantity); ?></td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <tr>
            <td colspan="2" class="main">
              <hr>
            </td>
          </tr>

          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
          <?php
          for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
          ?>
            <tr>
              <td class="main">Youtube video link:</td>
              <td class="main"><?php echo tep_image(DIR_WS_ICONS . 'youtube_icon.png', ICON_FOLDER, 20, 15) . '&nbsp;&nbsp;' . tep_draw_input_field('products_url[' . $languages[$i]['id'] . ']', (isset($products_url[$languages[$i]['id']]) ? $products_url[$languages[$i]['id']] : tep_get_products_url($pInfo->products_id, $languages[$i]['id']))); ?></td>
            </tr>
          <?php
          }
          ?>
          <tr>
            <td colspan="2">
              <div id="formerror"></div><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?>
            </td>
          </tr>
          <tr>
            <td colspan="2">

              <div id="wrapper">
                <ul class="imagetabs">
                  <li><a href="#" class="defaultimagetab" rel="imagetabs1">CG</a></li>
                  <li><a href="#" rel="imagetabs6">WM</a></li>
                </ul>
                <div class="imagetab-content" id="imagetabs1">

                  <ul id="piList">
                    <?php



                    echo '<li id="piId1" class="ui-state-default">

	     <table width="100%">
	      <tr>
            <td class="main">';

                    echo '<div class="image-thumb">' . tep_image(HTTPS_SERVER . DIR_WS_CATALOG_IMAGES . $pInfo->products_image, $pInfo->products_image, '', '', 'style="width: 70px;"') . '</div>';

                    echo '</td><td class="main" width="100%">';

                    echo '<span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>

	  <a href="#" onclick="showPiDelConfirm(1);return false;" class="ui-icon ui-icon-trash" style="float: right;"></a>

	  ' . tep_draw_file_field('products_image') . tep_draw_hidden_field('old_products_image', $pInfo->products_image) . '

	  <a href="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '" target="_blank">' . $pInfo->products_image . '</a>


           </td>
          </tr>
	     </table>

	  </li>';

                    $pi_counter = 1;

                    foreach ($pInfo->products_larger_images as $pi) {
                      $pi_counter++;

                      echo '<li id="piId' . $pi_counter . '" class="ui-state-default">

	     <table width="100%">
	      <tr>
            <td class="main">';

                      echo '<div class="image-thumb">' . tep_image(HTTPS_SERVER . DIR_WS_CATALOG_IMAGES . $pi['image'], $pi['image'], '', '', 'style="width: 70px;"') . '</div>';

                      echo '</td><td class="main" width="100%">';

                      echo '<span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>

	  <a href="#" onclick="showPiDelConfirm(' . $pi_counter . ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a>

	  ' . tep_draw_file_field('products_image_large_' . $pi['id']) . tep_draw_hidden_field('old_products_image_large_' . $pi['id'], $pi['image']) . '

	  <a href="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '" target="_blank">' . $pi['image'] . '</a><br /><br />

   ' . tep_draw_pull_down_menu('products_image_type_' . $pi['id'], $image_type_array, $pi['type']) . '

           </td>
          </tr>
	     </table>

	  </li>';
                    }
                    ?>
                  </ul>

                  <a href="#" onclick="addNewPiForm();return false;"><span class="ui-icon ui-icon-plus" style="float: left;"></span>Add New Image</a>

                  <div id="piDelConfirm" title="<?php echo TEXT_PRODUCTS_LARGE_IMAGE_DELETE_TITLE; ?>">
                    <p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span><?php echo TEXT_PRODUCTS_LARGE_IMAGE_CONFIRM_DELETE; ?></p>
                  </div>

                  <style type="text/css">
                    #piList {
                      list-style-type: none;
                      margin: 0;
                      padding: 0;
                    }

                    #piList li {
                      margin: 5px 0;
                      padding: 2px;
                    }
                  </style>

                  <script type="text/javascript">
                    $('#piList').sortable({
                      containment: 'parent'
                    });

                    var piSize = <?php echo $pi_counter; ?>;

                    function addNewPiForm() {
                      piSize++;

                      const selectHTML = '<select name="products_image_type_new_' + piSize + '">' +
                        '<option value="0">Product Image</option>' +
                        '<option value="1">Promotional Image</option>' +
                        '</select>';

                      $('#piList').append(
                        '<li id="piId' + piSize + '" class="ui-state-default">' +
                        '<table width="100%"><tr>' +
                        '<td class="main"><div class="image-thumb"></div></td>' +
                        '<td class="main" width="100%">' +
                        '<span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>' +
                        '<a href="#" onclick="showPiDelConfirm(' + piSize + ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a>' +
                        '<input type="file" name="products_image_large_new_' + piSize + '" /><br /><br />' +
                        selectHTML +
                        '</td>' +
                        '</tr></table>' +
                        '</li>'
                      );
                    }

                    var piDelConfirmId = 0;

                    $('#piDelConfirm').dialog({
                      autoOpen: false,
                      resizable: false,
                      draggable: false,
                      modal: true,
                      buttons: {
                        'Delete': function() {
                          $('#piId' + piDelConfirmId).effect('blind').remove();
                          $(this).dialog('close');
                        },
                        Cancel: function() {
                          $(this).dialog('close');
                        }
                      }
                    });

                    function showPiDelConfirm(piId) {
                      piDelConfirmId = piId;

                      $('#piDelConfirm').dialog('open');
                    }
                  </script>


                </div>
                <div class="imagetab-content" id="imagetabs6">
                  <table>
                    <tr>
                      <td class="main">WM Products Image</td>
                      <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_file_field('wm_products_image', false, 'id="upload-field"') . tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_get_extra_products_image($pInfo->products_id, 6) . tep_draw_hidden_field('wm_products_previous_image', tep_get_extra_products_image($pInfo->products_id, 6));  ?>
                        <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . tep_draw_checkbox_field('wm_delete_image_1', 'yes', false) . TEXT_DELETE_IMAGE; ?></td>
                    </tr>
                  </table>
                </div>
              </div>

            </td>
          </tr>
          <?php if ($rp_temp_remove) { ?>
            <tr>
              <td colspan="2">(Images uploaded that are wider than 500px will show on the site instead of any image using the large image slot below.)</td>
            </tr>
          <?php } ?>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '20'); ?></td>
          </tr>
          <tr>
            <td colspan="2">


            </td>
          </tr>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>

          <?php if (tep_not_null($pInfo->products_subimage7)) { ?>

            <tr>
              <td class="main">Delete Old Popup image?</td>
              <td class="main" bgcolor="#ebebff"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . $pInfo->products_subimage7 . '&nbsp;' . tep_draw_separator('pixel_trans.gif', '58', '15') . tep_draw_checkbox_field('delete_image_14', 'yes', false) . TEXT_DELETE_IMAGE; ?></td>
            </tr>
            <tr>
              <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
            </tr>

          <?php } ?>

        </table>
      </td>
    </tr>
    <tr>
      <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
    </tr>
    <tr>
      <td>

        <table width="800" border="0" cellspacing="5" cellpadding="0">
          <tr>
            <td class="main" align="right">
              <?php

              if (isset($_GET['pID'])) {
                $button_go = tep_draw_button(IMAGE_UPDATE, 'disk', null, 'primary');
              } else {
                $button_go =  tep_draw_button(IMAGE_INSERT, 'disk', null, 'primary');
              }

              echo tep_draw_hidden_field('products_date_added', (tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d'))) . $button_go . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')), null);

              ?>
            </td>
          </tr>
        </table>


        <table width="800" border="0" cellspacing="5" cellpadding="0">
          <!-- AJAX Attribute Manager  -->
          <tr>
            <td colspan="2">
              <div id="attribute-message-box" style="padding:5px 3px 5px 0px; float: right; color: #990000;"></div>
            </td>
          </tr>
          <tr>
            <td colspan="2"><?php require_once('attributeManager/includes/attributeManagerPlaceHolder.inc.php') ?></td>
          </tr>
          <!-- AJAX Attribute Manager end -->
        </table>
        </form>

        <script>
          $(function() {
            $("#extra-form").bind('submit', function() {
              $.ajax({
                type: 'post',
                url: 'sampler_update.php?action=add',
                data: $('form').serialize(),
                dataType: 'json',
                success: function(data) {

                  $('#extra-data').empty();
                  $('#extra-data').append(data.list_output);

                  $("input[name='dummy_extra_name']").val('');
                  $("input[name='dummy_extra_price']").val('');
                  $("input[name='dummy_extra_cost_price']").val('');
                  $("input[name='dummy_extra_show_in_desc']").prop('checked', false);

                  // Set the value, creating a new option if necessary
                  if ($("select[name='subproduct_selector_extra']").find("option[value='" + data.new_id + "']").length) {
                    $("select[name='subproduct_selector_extra']").val(data.new_id).trigger('change');
                  } else {
                    // Create a DOM Option and pre-select by default
                    var newOption = new Option(data.new_text, data.new_id, true, true);
                    // Append it to the select
                    $("select[name='subproduct_selector_extra']").append(newOption);
                    $("select[name='subproduct_selector_extra'] option[value=" + data.new_id + "]").attr("dd-price", data.new_price).attr("dd-trade", data.new_trade).attr("dd-desc", data.new_desc).attr("dd-type", data.new_type).attr("dd-margin", data.new_margin).trigger('change');

                  }

                  $("input[name='extra_name']").val('');
                  $("input[name='extra_price']").val('');
                  $("input[name='extra_cost_price']").val('');
                  $("input[name='extra_show_in_desc']").val('');

                }

              });

              return false;

            });
          });
        </script>
        <form action="sampler_update.php" method="post" id="extra-form">
          <?php echo tep_draw_hidden_field('extra_name'); ?>
          <?php echo tep_draw_hidden_field('extra_price'); ?>
          <?php echo tep_draw_hidden_field('extra_cost_price'); ?>
          <?php echo tep_draw_hidden_field('extra_show_in_desc'); ?>
        </form>

        <?php if (isset($_GET['pID'])) {
          $products_id = $_GET['pID'];
        ?>


          <?php if ($pInfo->products_status >= 2) {

            $product_notify_query = tep_db_query("select customers_name, customers_email_address, date_added from " . TABLE_PRODUCTS_NOTIFICATIONS . " where products_id = '" . $pInfo->products_id . "' order by date_added");

            if (tep_db_num_rows($product_notify_query)) {

          ?>
              <table border="0" width="50%" cellspacing="2" cellpadding="2" style="margin-top: 30px;">
                <tr>
                  <td colspan="3" width="100%" valign="top"><strong>Interested Customers</strong><br /><br /></td>
                </tr>
                <tr>
                  <td valign="top"><strong>Name</strong></td>
                  <td valign="top"><strong>Email Address</strong></td>
                  <td valign="top"><strong>Date Added</strong></td>
                </tr>
                <?php while ($product_notify = tep_db_fetch_array($product_notify_query)) { ?>
                  <tr>
                    <td valign="top"><?php echo $product_notify['customers_name']; ?></td>
                    <td valign="top"><?php echo $product_notify['customers_email_address']; ?></td>
                    <td valign="top"><?php echo $product_notify['date_added']; ?></td>
                  </tr>
                <?php } ?>
              </table>

          <?php }
          } ?>

          <table border="0" width="100%" cellspacing="2" cellpadding="2" style="margin-top: 30px;">
            <tr>
              <td width="100%" valign="top"><strong>Price Matching</strong></td>
              <td></td>
            </tr>
            <tr>
              <td width="100%" valign="top">

                <div class="pm-url">
                  <form action="price_match.php" method="post" id="collect_form">
                    <label>ADD URL: </label>
                    <input type="text" name="url" id="url" value="" style="width: 600px;" />
                    <input type="submit" name="submit" value="Get Prices" />
                    <input type="hidden" name="products_id" value="<?php echo $products_id; ?>" />
                  </form>
                </div>

                <form action="price_match_add.php" method="post" id="add_form">
                  <div id="server-results" class="pm-add">
                    <ul></ul>
                  </div>
                  <input type="hidden" name="raw_url" id="raw_url" value="" />
                  <div class="pm-add">
                    <input type="submit" name="submit" value="Add to Price Match" style="display: none;" id="add-match" />
                  </div>
                </form>

                <div class="pm-list">
                  <form action="price_match_del.php" method="post" id="del_form">
                    <div id="saved-data"></div>
                    <input type="hidden" name="products_id" value="<?php echo $products_id; ?>" />
                  </form>
                </div>

                <?php
                $product_name_plus = str_replace(' ', '+', $products_name);
                ?>

              </td>
              <td width="250" valign="top">
                <div class="pm-note">
                  <strong>Price Match Compatible Sites:</strong><br />
                  <a href="https://www.jjfox.co.uk/#/dfclassic/query=<?php echo $products_name; ?>" target="_blank">https://www.jjfox.co.uk</a><br />
                  <a href="http://www.mysmokingshop.co.uk/index2.php?q=<?php echo $products_name; ?>&mod=search&Submit=Go" target="_blank">http://www.mysmokingshop.co.uk</a><br />
                  <a href="https://www.cascnation.com/search?q=<?php echo $product_name_plus; ?>" target="_blank">https://www.cascnation.com</a><br />
                  <a href="https://www.havanahouse.co.uk/?s=<?php echo $product_name_plus; ?>&post_type=product" target="_blank">https://www.havanahouse.co.uk</a><br />
                  <a href="https://www.thebackyshop.co.uk/search/results?utf8=%E2%9C%93&search%5Bname_cont%5D=<?php echo $products_name; ?>&commit=GO" target="_blank">https://www.thebackyshop.co.uk</a><br />
                  <a href="https://www.thewhiskyexchange.com/search?q=<?php echo $products_name; ?>" target="_blank">https://www.thewhiskyexchange.com</a><br />
                  <a href="https://www.masterofmalt.com/search/#search=<?php echo $products_name; ?>" target="_blank">https://www.masterofmalt.com</a><br />
                  <a href="https://www.humidordiscount.co.uk/search?controller=search&orderby=position&orderway=desc&search_query=<?php echo $products_name; ?>" target="_blank">https://www.humidordiscount.co.uk</a><br />
                </div>
              </td>
            </tr>
          </table>

          <script>
            $(function() {
              $("#collect_form").bind('submit', function() {

                var raw_url = $("#url").val();
                $("#raw_url").val(raw_url);

                $.ajax({
                  type: 'post',
                  url: 'price_match_call.php',
                  data: $('form').serialize(),
                  dataType: 'json',
                  success: function(data) {

                    $('#server-results ul').empty();

                    var count = 1;

                    $.each(data, function(key, value) { // First Level

                      var radioBtn = $('<li><input type="radio" name="type" value="' + count + '_' + value.price + '_' + value.type + '" id="' + count + '" /><label for="' + count + '">' + value.type + ' : <strong>�' + value.price + '</strong></label></li>');
                      radioBtn.appendTo('#server-results ul');

                      $("#add-match").show();

                      count++;
                    });

                  }

                });

                return false;

              });
            });


            $(function() {
              $("#add_form").bind('submit', function() {
                $.ajax({
                  type: 'post',
                  url: 'price_match_update.php?action=add',
                  data: $('form').serialize(),
                  dataType: 'json',
                  success: function(data) {

                    $('#saved-data').empty();
                    $('#saved-data').append(data.list_output);



                  }

                });

                return false;

              });
            });


            function getdata(products_id) {

              $.ajax({
                url: 'price_match_update.php?products_id=' + products_id,
                dataType: 'json',
                cache: false,
                timeout: 10000,
                success: function(data) {

                  $('#saved-data').empty();
                  $('#saved-data').append(data.list_output);

                }
              });
            }

            $('body').on('click', '#engage-price-match', function() {

              var new_matched_price = $('input[name=lowest_price_match]').val();

              $('input[name="products_price"]').val(new_matched_price);

              updateGross();

              $.ajax({
                url: 'price_match_update.php?action=match&products_id=<?php echo $products_id; ?>',
                dataType: 'json',
                cache: false,
                success: function(data) {

                  $('#saved-data').empty();
                  $('#saved-data').append(data.list_output);

                }
              });
            });

            $(function() {
              $("#del_form").bind('submit', function() {
                $.ajax({
                  type: 'post',
                  url: 'price_match_update.php?action=del',
                  data: $('form').serialize(),
                  dataType: 'json',
                  success: function(data) {

                    $('#saved-data').empty();
                    $('#saved-data').append(data.list_output);

                  }

                });

                return false;

              });
            });

            $(document).ready(function() {
              getdata(<?php echo $products_id; ?>);
            });
          </script>

        <?php } ?>



        <!-- HTC BOC //-->
      <?php
    } elseif ($action == 'new_product_preview') {

      // PRODUCT PREVIEW REMOVED

    } else {


      // if (!tep_session_is_registered('psl_filter')) tep_session_register('psl_filter'); // makes it perminent
      if (isset($_GET['psl_filter']) && tep_not_null($_GET['psl_filter'])) {
        $psl_filter = $_GET['psl_filter'];
      }

      // if (!tep_session_is_registered('site_filter')) tep_session_register('site_filter'); // makes it perminent
      if (isset($_GET['site_filter']) && tep_not_null($_GET['site_filter'])) {
        $site_filter = $_GET['site_filter'];
      }



      ?>
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
          <tr>
            <td>
              <table border="0" width="100%" cellspacing="0" cellpadding="0">
                <tr>
                  <td class="pageHeading"><?php
                                          if (isset($_GET['search']) && tep_not_null($_GET['search'])) {
                                            echo 'Searching: ' . $_GET['search'];
                                          } elseif ($current_category_id == 0) {
                                            echo HEADING_TITLE;
                                          } else {
                                            echo tep_get_category_name($current_category_id, 1);
                                          }
                                          ?></td>
                  <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', 1, HEADING_IMAGE_HEIGHT); ?></td>
                  <td align="right">

                    <table align="right" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td>

                          <table align="right" border="0" cellspacing="0" cellpadding="5">
                            <tr>
                              <td class="smallText" align="right">

                              </td>
                              <td class="smallText" align="right">
                                <?php
                                echo tep_draw_form('goto', FILENAME_CATEGORIES_MASTER, '', 'get');
                                echo HEADING_TITLE_GOTO . ' ' . tep_draw_pull_down_menu('cPath', tep_get_category_tree(), $current_category_id, 'onChange="this.form.submit();"');
                                echo '</form>';
                                ?>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <table align="right" border="0" cellspacing="0" cellpadding="5">
                            <tr>
                              <td class="smallText" align="right">
                                <?php
                                echo tep_draw_form('search', FILENAME_CATEGORIES_MASTER, '', 'get');
                                echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('search');
                                //echo '</form>';
                                ?>
                              </td>

                              <td class="smallText" align="right">
                                <?php //echo tep_draw_form('filters', FILENAME_CATEGORIES_MASTER, '', 'get');

                                $psl_filter_array = array(array('id' => '0', 'text' => 'All locations'), array('id' => '1', 'text' => 'Liverpool'), array('id' => '2', 'text' => 'Chester'), array('id' => '3', 'text' => 'London'), array('id' => '8', 'text' => 'St James'), array('id' => '4', 'text' => 'Mayfair'), array('id' => '5', 'text' => 'Norfolk'), array('id' => '7', 'text' => 'Knutsford'), array('id' => '9', 'text' => 'Leeds'), array('id' => '10', 'text' => 'Mash Tun'), array('id' => '11', 'text' => 'Lime Street'), array('id' => '12', 'text' => 'Edinburgh'), array('id' => '6', 'text' => 'Order from Supplier'));

                                $site_filter_array = array(array('id' => '1', 'text' => 'CG'), array('id' => '0', 'text' => 'All sites'), array('id' => '4', 'text' => 'HO'), array('id' => '5', 'text' => 'HS'), array('id' => '8', 'text' => 'WM'), array('id' => '9', 'text' => 'DC'), array('id' => '10', 'text' => 'TT'));

                                if (isset($_GET['cPath'])) {
                                  echo '<input type="hidden" name="cPath" value="' . $_GET['cPath'] . '">';
                                }
                                if (isset($_GET['cID'])) {
                                  echo '<input type="hidden" name="cID" value="' . $_GET['cID'] . '">';
                                }
                                //if (isset($_GET['search'])) {   echo '<input type="hidden" name="search" value="' . $_GET['search'] . '">'; }

                                echo 'Show Products from: ' . tep_draw_pull_down_menu('site_filter', $site_filter_array, $site_filter, '');
                                // onChange="this.form.submit();"
                                ?>
                              </td>
                              <td class="smallText" align="right">

                                <?php
                                echo 'Product Stock Location: ' . tep_draw_pull_down_menu('psl_filter', $psl_filter_array, $psl_filter, '');
                                // onChange="this.form.submit();"

                                ?>
                              </td>
                              <td>
                                <?php
                                echo '<input type="submit" value="GO">';
                                echo '</form>';
                                ?>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td><?php echo tep_draw_separator('pixel_trans.gif', 1, 31); ?></td>
          </tr>
          <tr>
            <td>
              <table border="0" width="100%" cellspacing="0" cellpadding="0">
                <tr>
                  <td valign="top" style="position: relative;">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">

                      <?php if ($sitecatselecting == true) { ?>
                        <?php echo tep_draw_form('setsitecatselect', FILENAME_CATEGORIES_MASTER, 'action=setsitecatselect'); ?><input type="hidden" name="cPath" value="<?php echo $cPath; ?>">
                      <?php } ?>

                      <?php if ($siteselecting == true) { ?>
                        <?php echo tep_draw_form('setsiteselect', FILENAME_CATEGORIES_MASTER, 'action=setsiteselect'); ?><input type="hidden" name="cPath" value="<?php echo $cPath; ?>">
                      <?php } ?>

                      <?php if ($sorting == true) { ?>
                        <?php echo tep_draw_form('setsortorder', FILENAME_CATEGORIES_MASTER, 'action=setsortorder'); ?><input type="hidden" name="cPath" value="<?php echo $cPath; ?>">
                      <?php } ?>

                      <tr class="dataTableHeadingRow">
                        <?php if (isset($_GET['search']) && tep_not_null($_GET['search'])) { ?>
                          <td class="dataTableHeadingContent" align="center" width="40">Cat ID</td>
                        <?php } ?>
                        <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_CATEGORIES_PRODUCTS; ?></td>
                        <!--master site select //-->
                        <td class="dataTableHeadingContent" align="center" width="15">CG</td>
                        <td class="dataTableHeadingContent" align="center" width="15">TT</td>
                        <!--end master site select//-->
                        <td class="dataTableHeadingContent" align="center" width="116"><?php echo TABLE_HEADING_STATUS; ?></td>
                        <td class="dataTableHeadingContent" align="center" width="100"><?php echo TABLE_HEADING_SORT_ORDER; ?></td>

                        <td class="dataTableHeadingContent" align="center" width="100"># Cats</td>

                        <td class="dataTableHeadingContent" align="right" width="100"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
                      </tr>
                      <?php if (($sitecatselecting) or ($siteselecting)) { ?>
                        <tr>
                          <td align="right"> Check all&nbsp;</td>
                          <td align="center" width="15"><input type="checkbox" class="selectall-cg" id="selectall-cg" /></td>
                          <td align="center" width="15"><input type="checkbox" class="selectall-tt" id="selectall-tt" /></td>
                          <td></td>
                          <td></td>
                          <td></td>
                        </tr>
                      <?php } ?>
                      <?php
                      $categories_count = 0;
                      $rows = 0;
                      if (isset($_GET['search']) && tep_not_null($_GET['search'])) {
                        $search = tep_db_prepare_input($_GET['search']);
                        // HTC BOC
                        $categories_query = tep_db_query("select c.categories_id, cd.categories_name, cd.categories_description, cd.category_admin_notes, cd.category_admin_notes_modified, c.categories_image, c.parent_id, c.sort_order, c.date_added, c.last_modified, cd.categories_htc_title_tag, cd.categories_htc_desc_tag, cd.categories_htc_keywords_tag, c.categories_status, c.mm_col, c.sub_cats_show_image, cs.store_cg, cs.store_ho, cs.store_hs, cs.store_wm, cs.store_dc, cs.store_tt from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd, " . TABLE_CATEGORIES_TO_STORES . " cs where c.categories_id = cd.categories_id and c.categories_id = cs.categories_id and cd.language_id = '" . (int)$languages_id . "' and cd.categories_name like '%" . tep_db_input($search) . "%' order by FIELD(c.categories_status,'1','0'), c.sort_order, cd.categories_name");
                      } else {
                        $categories_query = tep_db_query("select c.categories_id, cd.categories_name, cd.categories_description, cd.category_admin_notes, cd.category_admin_notes_modified, c.categories_image, c.parent_id, c.sort_order, c.date_added, c.last_modified, cd.categories_htc_title_tag, cd.categories_htc_desc_tag, cd.categories_htc_keywords_tag, c.categories_status, c.mm_col, c.sub_cats_show_image, cs.store_cg, cs.store_ho, cs.store_hs, cs.store_wm, cs.store_dc, cs.store_tt from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd, " . TABLE_CATEGORIES_TO_STORES . " cs where c.parent_id = '" . (int)$current_category_id . "' and c.categories_id = cd.categories_id and c.categories_id = cs.categories_id and cd.language_id = '" . (int)$languages_id . "' order by FIELD(c.categories_status,'1','0'), c.sort_order, cd.categories_name");
                        // HTC EOC
                      }

                      $sites_categories_query = tep_db_query("select cs.categories_id, cs.store_cg, cs.store_ho, cs.store_hs, cs.store_wm, cs.store_dc, cs.store_tt from " . TABLE_CATEGORIES_TO_STORES . " cs where cs.categories_id = '" . (int)$current_category_id . "'");
                      $sites_categories = tep_db_fetch_array($sites_categories_query);

                      while ($categories = tep_db_fetch_array($categories_query)) {
                        $categories_count++;
                        $rows++;

                        // Get parent_id for subcategories if search
                        if (isset($_GET['search'])) $cPath = $categories['parent_id'];

                        if ((!isset($_GET['cID']) && !isset($_GET['pID']) || (isset($_GET['cID']) && ($_GET['cID'] == $categories['categories_id']))) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
                          $category_childs = array('childs_count' => tep_childs_in_category_count($categories['categories_id']));
                          $category_products = array('products_count' => tep_products_in_category_count($categories['categories_id']));

                          $cInfo_array = array_merge($categories, $category_childs, $category_products);
                          $cInfo = new objectInfo($cInfo_array);
                        }


                        if ($sitecatselecting == true) {

                          if (isset($cInfo) && is_object($cInfo) && ($categories['categories_id'] == $cInfo->categories_id)) {
                            echo '              <tr id="defaultSelected" class="dataTableRowSelected">' . "\n";
                          } else {
                            echo '              <tr class="dataTableRow">' . "\n";
                          }
                        } else {

                          if (isset($cInfo) && is_object($cInfo) && ($categories['categories_id'] == $cInfo->categories_id)) {
                            echo '              <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_CATEGORIES_MASTER, tep_get_path($categories['categories_id'])) . '\'">' . "\n";
                          } else {
                            echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $categories['categories_id']) . '\'">' . "\n";
                          }
                        }

                        if (isset($_GET['search']) && tep_not_null($_GET['search'])) {

                          echo '<td class="dataTableContent" align="center">' . $categories['categories_id'] . '</td>';
                        }

                      ?>
                        <td class="dataTableContent"><?php echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, tep_get_path($categories['categories_id'])) . '">' . tep_image(DIR_WS_ICONS . 'folder.gif', ICON_FOLDER) . '</a>&nbsp;<b>' . $categories['categories_name'] . '</b>'; ?></td>
                        <!--master site select //-->
                        <?php
                        if ((int)$current_category_id != 0) {
                          $light_class = 'class="dataTableContentLight"';
                          $dark_class = 'class="dataTableContentDark"';
                        } else {
                          $dark_class = 'class="dataTableContent"'; // top level defaults to Dark as theres no parent
                        }

                        if (!$sitecatselecting) {

                          if ($categories['store_cg'] == 1) {
                            $checked = 'CHECKED';
                          } else {
                            $checked = '';
                          }
                          if ($sites_categories['store_cg'] == 1) {
                            $cat_class = $light_class;
                          } else {
                            $cat_class = $dark_class;
                          }
                          echo '<td ' . $cat_class . ' align="center" width="15">' . tep_draw_ms_disabled_checkbox_field('sitecatselect_cg[]', $categories['categories_id'], $checked) . '</td>';

                          if ($categories['store_tt'] == 1) {
                            $checked = 'CHECKED';
                          } else {
                            $checked = '';
                          }
                          if ($sites_categories['store_tt'] == 1) {
                            $cat_class = $light_class;
                          } else {
                            $cat_class = $dark_class;
                          }
                          echo '<td ' . $cat_class . ' align="center" width="15">' . tep_draw_ms_disabled_checkbox_field('sitecatselect_tt[]', $categories['categories_id'], $checked) . '</td>';
                        } else {

                          if ($categories['store_cg'] == 1) {
                            $checked = 'CHECKED';
                            $old_value = 1;
                          } else {
                            $checked = '';
                            $old_value = 9;
                          }
                          if ($sites_categories['store_cg'] == 1) {
                            $cat_class = $light_class;
                          } else {
                            $cat_class = $dark_class;
                          }
                          echo '<td ' . $cat_class . ' align="center" width="15">' . tep_draw_ms_checkbox_field('sitecatselect_new_cg[' . $categories['categories_id'] . ']', '1', $checked, '', 'class="vert-cg"') . tep_draw_hidden_field('sitecatselect_old_cg[' . $categories['categories_id'] . ']', $old_value) . '</td>';

                          if ($categories['store_tt'] == 1) {
                            $checked = 'CHECKED';
                            $old_value = 1;
                          } else {
                            $checked = '';
                            $old_value = 9;
                          }
                          if ($sites_categories['store_tt'] == 1) {
                            $cat_class = $light_class;
                          } else {
                            $cat_class = $dark_class;
                          }
                          echo '<td ' . $cat_class . ' align="center" width="15">' . tep_draw_ms_checkbox_field('sitecatselect_new_tt[' . $categories['categories_id'] . ']', '1', $checked, '', 'class="vert-tt"') . tep_draw_hidden_field('sitecatselect_old_tt[' . $categories['categories_id'] . ']', $old_value) . '</td>';
                        }

                        ?>
                        <!--end master site select//-->
                        <td class="dataTableContent" align="center" width="130">
                          <?php

                          if ($categories['categories_status'] == '1') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=1&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>';
                          }

                          echo '&nbsp;&nbsp;';

                          if ($categories['categories_status'] == '0') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=0&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
                          }

                          echo '&nbsp;&nbsp;';

                          if ($categories['categories_status'] == '2') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_blue.gif', IMAGE_ICON_STATUS_BLUE_LIGHT, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=2&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_blue_light.gif', IMAGE_ICON_STATUS_BLUE_LIGHT, 10, 10) . '</a>';
                          }

                          echo '&nbsp;&nbsp;';

                          if ($categories['categories_status'] == '3') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_pink.gif', IMAGE_ICON_STATUS_PINK_LIGHT, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=3&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_pink_light.gif', IMAGE_ICON_STATUS_PINK_LIGHT, 10, 10) . '</a>';
                          }

                          echo '&nbsp;&nbsp;';

                          if ($categories['categories_status'] == '4') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_STATUS_PURPLE_LIGHT, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=4&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_yellow_light.gif', IMAGE_ICON_STATUS_PURPLE_LIGHT, 10, 10) . '</a>';
                          }

                          echo '&nbsp;&nbsp;';

                          if ($categories['categories_status'] == '5') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_museum.gif', IMAGE_ICON_STATUS_MUSEUM_LIGHT, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=5&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_museum_light.gif', IMAGE_ICON_STATUS_MUSEUM_LIGHT, 10, 10) . '</a>';
                          }

                          echo '&nbsp;&nbsp;';

                          if ($categories['categories_status'] == '6') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_stash.gif', IMAGE_ICON_STATUS_STASH_LIGHT, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=6&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_stash_light.gif', IMAGE_ICON_STATUS_STASH_LIGHT, 10, 10) . '</a>';
                          }

                          echo '&nbsp;&nbsp;';

                          if ($categories['categories_status'] == '7') {
                            echo tep_image(DIR_WS_IMAGES . 'icon_status_showcase.gif', IMAGE_ICON_STATUS_SHOWCASE, 10, 10);
                          } else {
                            echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag_cat&flag=7&cID=' . $categories['categories_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_showcase_light.gif', IMAGE_ICON_STATUS_SHOWCASE_LIGHT, 10, 10) . '</a>';
                          }

                          ?>
                        </td>
                        <td class="dataTableContent" align="center" width="100"><span style="color:#CCC; font-size: 12px;"><?php echo $categories['sort_order'] . ' col: ' . $categories['mm_col']; ?></span></td>
                        <td class="dataTableContent" align="center" width="100"></td>
                        <td class="dataTableContent" align="right" width="100"><?php if (isset($cInfo) && is_object($cInfo) && ($categories['categories_id'] == $cInfo->categories_id)) {
                                                                                  echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', '');
                                                                                } else {
                                                                                  echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $categories['categories_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>';
                                                                                } ?>&nbsp;</td>
                </tr>
              <?php
                      }

                      $products_count = 0;

                      $table_query = '';
                      $where_query = '';


                      if (isset($_GET['search']) && tep_not_null($_GET['search'])) {
                        $where_query .= " and (pd.products_name like '%" . tep_db_input($search) . "%' or p.products_model like '%" . tep_db_input($search) . "%')";
                      } else {
                        $where_query .= " and p2c.categories_id = '" . (int)$current_category_id . "'";
                      }

                      if (($psl_filter) && ($psl_filter != '0')) {

                        $table_query .= ", " . TABLE_PRODUCTS_TO_STOCK_LOCATION . " psl";
                        $where_query .= " and p.products_id = psl.products_id";

                        if ($psl_filter == '1') {
                          $where_query .= " and psl.stock_liverpool != '0'";
                        } elseif ($psl_filter == '2') {
                          $where_query .= " and psl.stock_chester != '0'";
                        } elseif ($psl_filter == '3') {
                          $where_query .= " and psl.stock_london != '0'";
                        } elseif ($psl_filter == '4') {
                          $where_query .= " and psl.stock_mayfair != '0'";
                        } elseif ($psl_filter == '5') {
                          $where_query .= " and psl.stock_norfolk != '0'";
                        } elseif ($psl_filter == '7') {
                          $where_query .= " and psl.stock_knutsford != '0'";
                        } elseif ($psl_filter == '6') {
                          $where_query .= " and psl.stock_supplier = '1'";
                        } elseif ($psl_filter == '8') {
                          $where_query .= " and psl.stock_mayfair_cg = '1'";
                        } elseif ($psl_filter == '9') {
                          $where_query .= " and psl.stock_leeds = '1'";
                        } elseif ($psl_filter == '10') {
                          $where_query .= " and psl.stock_mashtun = '1'";
                        } elseif ($psl_filter == '11') {
                          $where_query .= " and psl.stock_lime_street = '1'";
                        } elseif ($psl_filter == '12') {
                          $where_query .= " and psl.stock_edinburgh = '1'";
                        }
                      }

                      if (($site_filter) && ($site_filter != '0')) {

                        if ($site_filter == '1') {
                          $where_query .= " and ps.store_cg = '1'";
                        } elseif ($site_filter == '4') {
                          $where_query .= " and ps.store_ho = '1'";
                        } elseif ($site_filter == '5') {
                          $where_query .= " and ps.store_hs = '1'";
                        } elseif ($site_filter == '8') {
                          $where_query .= " and ps.store_wm = '1'";
                        } elseif ($site_filter == '9') {
                          $where_query .= " and ps.store_dc = '1'";
                        } elseif ($site_filter == '10') {
                          $where_query .= " and ps.store_tt = '1'";
                        }
                      }


                      $products_query = tep_db_query("select p.products_id, p.products_model, pd.products_name, pd.products_admin_notes, pd.products_admin_notes_modified, p.products_tweet_date, p.products_quantity, p.products_image, p.products_subimage7, p.products_price, p.products_trade_price, p.products_trade_price_discount, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_created_date, p.products_status, p.suppliers_id, p.products_sort_order, p2c.categories_id, p.products_free_shipping, p.products_order_type, p.products_cigar, p.products_earn_points, p.products_vat_deductable, p.products_paypal, p.hide_navigation, p.hide_new_in, p.products_event_date, ps.store_cg, ps.store_ho, ps.store_hs, ps.store_wm, ps.store_dc, ps.store_tt from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_PRODUCTS_TO_STORES . " ps " . $table_query . " where p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = p2c.products_id and p.products_id = ps.products_id " . $where_query . " order by FIELD(p.products_status,'1','2','4','6','0','5'), p.products_sort_order, pd.products_name");


                      if ($action == 'move_product') {
                        echo tep_draw_form('products', FILENAME_CATEGORIES_MASTER, 'action=move_product_confirm&cPath=' . $cPath);
                        echo "<input type='hidden' name='move_from_category_id' value='$cPath'>";
                      } elseif ($action == 'copy_to') {
                        echo tep_draw_form('products', FILENAME_CATEGORIES_MASTER, 'action=copy_to_confirm&cPath=' . $cPath);
                        //echo "<input type='hidden' name='move_from_category_id' value='$cPath'>";
                      } elseif ($action == 'delete_product') {
                        echo tep_draw_form('products', FILENAME_CATEGORIES_MASTER, 'action=delete_product_confirm&cPath=' . $cPath);
                        echo "<input type='hidden' name='product_categories' value='" . (int)$current_category_id . "'>";
                      }

                      $sites_categories_query = tep_db_query("select cs.categories_id, cs.store_cg, cs.store_ho, cs.store_hs, cs.store_wm, cs.store_dc, cs.store_tt from " . TABLE_CATEGORIES_TO_STORES . " cs where cs.categories_id = '" . (int)$current_category_id . "'");
                      $sites_categories = tep_db_fetch_array($sites_categories_query);

                      while ($products = tep_db_fetch_array($products_query)) {
                        $products_count++;
                        $rows++;

                        // Get categories_id for product if search
                        if (isset($_GET['search']) && tep_not_null($_GET['search'])) $cPath = $products['categories_id'];

                        if ((!isset($_GET['pID']) && !isset($_GET['cID']) || (isset($_GET['pID']) && ($_GET['pID'] == $products['products_id']))) && !isset($pInfo) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
                          // find out the rating average from customer reviews
                          $reviews_query = tep_db_query("select (avg(reviews_rating) / 5 * 100) as average_rating from " . TABLE_REVIEWS . " where products_id = '" . (int)$products['products_id'] . "'");
                          $reviews = tep_db_fetch_array($reviews_query);
                          $pInfo_array = array_merge($products, $reviews);
                          $pInfo = new objectInfo($pInfo_array);
                        }

                        //sort order
                        if (($sorting == true) or ($siteselecting == true)) {

                          // sorting / site select code
                          if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id)) {
                            echo '              <tr id="defaultSelected" class="dataTableRowSelected">' . "\n";
                          } else {
                            echo '              <tr class="dataTableRow">' . "\n";
                          }
                        } else {

                          // move / copy code
                          if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id)) {
                            echo '              <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" ';
                            echo tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products['products_id'] . '') . '\'">' . "\n";
                          } else {
                            echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" >';
                          }
                        }
                        //end sort order

                        if (isset($_GET['search']) && tep_not_null($_GET['search'])) {
                          echo '<td class="dataTableContent" align="center"><u><a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '" target="_blank"/>' . $products['categories_id'] . '</a></u></td>';
                        }

                        echo '<td class="dataTableContent" style="cursor: pointer;" ';

                        if ($action != 'move_product' && $action != 'copy_to' && $action != 'delete_product') {
                          echo 'onclick="document.location.href=\'';
                        }

                        echo tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '\'">' . "\n";

                        if ($action == 'move_product' || $action == 'copy_to' || $action == 'delete_product') {
                          echo '              <input type="checkbox" name="products_id[]" value="' . $products['products_id'] . '"';
                          if ($pID == $products['products_id']) {
                            echo " checked";
                          }
                          echo '>&nbsp;' . $products['products_name'];
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products['products_id'] . '&action=new_product_preview&read=only') . '">' . tep_image(DIR_WS_ICONS . 'preview.gif', ICON_PREVIEW) . '</a>&nbsp;' . $products['products_name'];
                        }
              ?>
            </td>
            <!-- master site//-->

            <?php

                        if (!$siteselecting) {

                          if ($products['store_cg'] == 1) {
                            $checked = 'CHECKED';
                          } else {
                            $checked = '';
                          }
                          if ($sites_categories['store_cg'] == 1) {
                            $prod_class = 'class="dataTableContentLight"';
                          } else {
                            $prod_class = 'class="dataTableContentDark"';
                          }
                          echo '<td ' . $prod_class . ' align="center" width="15">' . tep_draw_ms_disabled_checkbox_field('siteselect_cg[]', $products['products_id'], $checked) . '</td>';

                          if ($products['store_tt'] == 1) {
                            $checked = 'CHECKED';
                          } else {
                            $checked = '';
                          }
                          if ($sites_categories['store_tt'] == 1) {
                            $prod_class = 'class="dataTableContentLight"';
                          } else {
                            $prod_class = 'class="dataTableContentDark"';
                          }
                          echo '<td ' . $prod_class . ' align="center" width="15">' . tep_draw_ms_disabled_checkbox_field('siteselect_tt[]', $products['products_id'], $checked) . '</td>';
                        } else {

                          if ($products['store_cg'] == 1) {
                            $checked = 'CHECKED';
                          } else {
                            $checked = '';
                          }
                          if ($sites_categories['store_cg'] == 1) {
                            $prod_class = 'class="dataTableContentLight"';
                          } else {
                            $prod_class = 'class="dataTableContentDark"';
                          }
                          echo '<td ' . $prod_class . ' align="center" width="15">' . tep_draw_ms_checkbox_field('siteselect_cg[]', $products['products_id'], $checked, '', 'class="vert-cg"') . tep_draw_hidden_field('products_id_cg[]', $products['products_id']) . '</td>';

                          if ($products['store_tt'] == 1) {
                            $checked = 'CHECKED';
                          } else {
                            $checked = '';
                          }
                          if ($sites_categories['store_tt'] == 1) {
                            $prod_class = 'class="dataTableContentLight"';
                          } else {
                            $prod_class = 'class="dataTableContentDark"';
                          }
                          echo '<td ' . $prod_class . ' align="center" width="15">' . tep_draw_ms_checkbox_field('siteselect_tt[]', $products['products_id'], $checked, '', 'class="vert-tt"') . tep_draw_hidden_field('products_id_tt[]', $products['products_id']) . '</td>';
                        }
            ?>
            <!--end master site//-->
            <td class="dataTableContent" align="center" width="130">
              <?php

                        if ($products['products_status'] == '1') { // dark green
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=1&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>';
                        }

                        echo '&nbsp;&nbsp';

                        if ($products['products_status'] == '0') { // dark red
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=0&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
                        }

                        echo '&nbsp;&nbsp';

                        if ($products['products_status'] == '2') { // dark blue
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_blue.gif', IMAGE_ICON_STATUS_BLUE, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=2&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_blue_light.gif', IMAGE_ICON_STATUS_BLUE_LIGHT, 10, 10) . '</a>';
                        }

                        echo '&nbsp;&nbsp';

                        if ($products['products_status'] == '3') { // dark pink
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_pink.gif', IMAGE_ICON_STATUS_PINK, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=3&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_pink_light.gif', IMAGE_ICON_STATUS_PINK_LIGHT, 10, 10) . '</a>';
                        }

                        echo '&nbsp;&nbsp';

                        if ($products['products_status'] == '4') { // dark purple
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_STATUS_PURPLE, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=4&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_yellow_light.gif', IMAGE_ICON_STATUS_PURPLE_LIGHT, 10, 10) . '</a>';
                        }

                        echo '&nbsp;&nbsp';

                        if ($products['products_status'] == '5') { // dark purple
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_museum.gif', IMAGE_ICON_STATUS_MUSEUM, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=5&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_museum_light.gif', IMAGE_ICON_STATUS_MUSEUM_LIGHT, 10, 10) . '</a>';
                        }

                        echo '&nbsp;&nbsp';

                        if ($products['products_status'] == '6') { // dark purple
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_stash.gif', IMAGE_ICON_STATUS_STASH, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=6&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_stash_light.gif', IMAGE_ICON_STATUS_STASH_LIGHT, 10, 10) . '</a>';
                        }

                        echo '&nbsp;&nbsp';

                        if ($products['products_status'] == '7') { // dark purple
                          echo tep_image(DIR_WS_IMAGES . 'icon_status_showcase.gif', IMAGE_ICON_STATUS_SHOWCASE, 10, 10);
                        } else {
                          echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'action=setflag&flag=7&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_showcase_light.gif', IMAGE_ICON_STATUS_SHOWCASE_LIGHT, 10, 10) . '</a>';
                        }

              ?></td>
            <!--sort order//-->
            <td class="dataTableContent" align="center" width="100">
              <?php
                        if (!$sorting) {
                          echo tep_draw_input_field('sortorder[]', $products['products_sort_order'],  'SIZE=3 Disabled') . '</td>';
                        } else {
                          echo tep_draw_input_field('sortorder[]', $products['products_sort_order'],  'SIZE=3') . tep_draw_hidden_field('products_id[]', $products['products_id']) . '</td>';
                        }
              ?>
              <!--end sort order//-->

            <td class="dataTableContent" align="center" width="100">
              <?php
                        $multi_cat_query = tep_db_query("select count(*) as total from products_to_categories where products_id = '" . (int)$products['products_id'] . "'");
                        $multi_cat = tep_db_fetch_array($multi_cat_query);
                        if ($multi_cat['total'] < 2) {
                          echo '<strong>' . $multi_cat['total'] . '</strong>';
                        } else {
                          echo '<u><a href="categories_master.php?search=' . $products['products_name'] . '">' . $multi_cat['total'] . '</a></u>';
                        } ?>
            </td>
            <td class="dataTableContent" align="right" width="100"><?php if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id)) {
                                                                      echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', '');
                                                                    } else {
                                                                      echo '<a href="' . tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>';
                                                                    } ?>&nbsp;</td>
          </tr>
        <?php
                      }

                      $cPath_back = '';
                      if (sizeof($cPath_array) > 0) {
                        for ($i = 0, $n = sizeof($cPath_array) - 1; $i < $n; $i++) {
                          if (empty($cPath_back)) {
                            $cPath_back .= $cPath_array[$i];
                          } else {
                            $cPath_back .= '_' . $cPath_array[$i];
                          }
                        }
                      }

                      $cPath_back = (tep_not_null($cPath_back)) ? 'cPath=' . $cPath_back . '&' : '';
        ?>
        <tr>
          <td colspan="7"><?php echo tep_draw_separator('pixel_trans.gif', 1, 5); ?></td>
        </tr>
        <tr>
          <!--sort order//-->
          <td class="smallText" align="left">
            <?php

            if (($sorting != true) && ($siteselecting != true) && ($sitecatselecting != true)) {

              if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cID=' . $current_category_id), null) . '&nbsp;';
              if (!isset($_GET['search'])) echo tep_draw_button(IMAGE_NEW_CATEGORY, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=new_category'), null) . '&nbsp;' . tep_draw_button(IMAGE_NEW_PRODUCT, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=new_product'), null);
            }
            ?>
          </td>
          <td colspan="7" align="left">
            <?php
            if (($sorting != true) && ($siteselecting != true) && ($sitecatselecting != true)) {
              if (($products_count != 0) && ($categories_count != 0)) {
                echo 'category and product mix';
              } else {
                if ($products_count > 0) {
                  echo tep_draw_button('Select Sites', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=beginsiteselect'), null);
                }
                if ($categories_count > 0) {
                  echo tep_draw_button('Select Sites', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=beginsitecatselect'), null);
                }
              }
            }

            if ($siteselecting == true) {

              if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cPath=' . $cPath), null) . '</a>&nbsp;';
              if (!isset($_GET['search'])) echo tep_draw_button('Save Sites', 'disk', null, 'primary') . '&nbsp;';
            }

            if ($sitecatselecting == true) {

              if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cPath=' . $cPath), null) . '&nbsp;';
              if (!isset($_GET['search'])) echo tep_draw_button('Save Sites', 'disk', null, 'primary') . '&nbsp;';
            }

            ?>
            <?php
            if (($sorting != true) && ($siteselecting != true) && ($sitecatselecting != true)) {
              if (($products_count != 0) && ($categories_count != 0)) {
                echo '';
              } else {
                if ($products_count > 0) {
                  echo tep_draw_button('Change Sort Order', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=beginsort'), null);
                }
              }
            }
            if ($sorting == true) {

              if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cPath=' . $cPath), null);
              if (!isset($_GET['search'])) echo tep_draw_button(IMAGE_BUTTON_SAVE_SORT, 'disk', null, 'primary');
            }
            ?>
          </td>
        </tr>
        <tr>
          <td colspan="3"><?php echo TEXT_CATEGORIES . '&nbsp;' . $categories_count . '<br />' . TEXT_PRODUCTS . '&nbsp;' . $products_count; ?></td>
        </tr>
        </table>

        <table class="extra-buttons">
          <tr>
            <!--sort order//-->
            <td class="smallText" align="center">
              <?php

              if (($sorting != true) && ($siteselecting != true) && ($sitecatselecting != true)) {

                if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cID=' . $current_category_id), null) . '&nbsp;';
                if (!isset($_GET['search'])) echo tep_draw_button(IMAGE_NEW_CATEGORY, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=new_category'), null) . '&nbsp;' . tep_draw_button(IMAGE_NEW_PRODUCT, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=new_product'), null);
              }
              ?> </td>
            <td colspan="3" align="center">
              <?php
              if (($sorting != true) && ($siteselecting != true) && ($sitecatselecting != true)) {
                if (($products_count != 0) && ($categories_count != 0)) {
                  echo 'category and product mix';
                } else {
                  if ($products_count > 0) {
                    echo tep_draw_button('Select Sites', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=beginsiteselect'), null);
                  }
                  if ($categories_count > 0) {
                    echo tep_draw_button('Select Sites', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=beginsitecatselect'), null);
                  }
                }
              }

              if ($siteselecting == true) {

                if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cPath=' . $cPath), null) . '&nbsp;';
                if (!isset($_GET['search'])) echo tep_draw_button('Save Sites', 'disk', null, 'primary') . '&nbsp;';
              }

              if ($sitecatselecting == true) {

                if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cPath=' . $cPath), null) . '&nbsp;';
                if (!isset($_GET['search'])) echo tep_draw_button('Save Sites', 'disk', null, 'primary') . '&nbsp;';
              }

              ?>

              <?php
              if (($sorting != true) && ($siteselecting != true) && ($sitecatselecting != true)) {
                if (($products_count != 0) && ($categories_count != 0)) {
                  echo '';
                } else {
                  if ($products_count > 0) {
                    echo tep_draw_button('Change Sort Order', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&action=beginsort'), null);
                  }
                }
              }
              if ($sorting == true) {

                if (sizeof($cPath_array) > 0) echo tep_draw_button(IMAGE_BACK, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, $cPath_back . 'cPath=' . $cPath), null) . '&nbsp;';
                if (!isset($_GET['search'])) echo tep_draw_button(IMAGE_BUTTON_SAVE_SORT, 'disk', null, 'primary') . '&nbsp;';
              }
              ?> </td>
            <td></td>
          </tr>
        </table>
        <div class="transfer-bar">
          <ul>
            <li><img src="images/but_transfer_data.png" /></li>
            <li><a class='iframe' href="create_site_tables.php?store_id=8&action=export"><img border="0" src="images/but_turmeaus.jpg" /></a></li>
          </ul>
        </div>
        <?php if (($sorting == true) or ($siteselecting == true) or ($sitecatselecting == true)) { ?></form><?php } ?>
      </td>
      <!--end sort order//-->
      <?php
      $heading = array();
      $contents = array();
      switch ($action) {
        case 'new_category':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW_CATEGORY . '</b>');

          $contents = array('form' => tep_draw_form('newcategory', FILENAME_CATEGORIES_MASTER, 'action=insert_category&cPath=' . $cPath, 'post', 'enctype="multipart/form-data"'));
          $contents[] = array('text' => TEXT_NEW_CATEGORY_INTRO);

          $category_inputs_string = '';
          $languages = tep_get_languages();
          for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
            $category_inputs_string .= '<br />' . tep_draw_input_field('categories_name[' . $languages[$i]['id'] . ']');
            $category_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_name_highlight[' . $languages[$i]['id'] . ']', '1', '') . ' Highlight Red';
            $category_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_name_highlight_black[' . $languages[$i]['id'] . ']', '1', '') . ' Highlight Black';
            $category_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_name_gap[' . $languages[$i]['id'] . ']', '1', '') . ' Add Space Above';
            // HTC BOC
            $category_htc_title_string .= '<br />' . tep_draw_input_field('categories_htc_title_tag[' . $languages[$i]['id'] . ']');
            $category_htc_desc_string .= '<br />' . tep_draw_input_field('categories_htc_desc_tag[' . $languages[$i]['id'] . ']');
            $category_htc_keywords_string .= '<br />' . tep_draw_input_field('categories_htc_keywords_tag[' . $languages[$i]['id'] . ']');
            // HTC EOC

          }
          $category_desc_inputs_string = '';
          $languages = tep_get_languages();
          for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
            $category_desc_inputs_string .= '<br />' . tep_draw_textarea_field('categories_description[' . $languages[$i]['id'] . ']', '', 20, 5);
            $category_desc_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_desc_switch[' . $languages[$i]['id'] . ']', '1', '') . ' Override Link';
            $category_desc_inputs_string .= '<br />Read More / SEO text<br />' . tep_draw_textarea_field('categories_read_more[' . $languages[$i]['id'] . ']', '', 20, 5);
          }

          $tabs_name_string .= '
  <div id="wrapper">
    <ul class="tabs">
        <li><a href="#" class="defaulttab" rel="tabs1">CG</a></li>
		    <li><a href="#" rel="tabs8">TT</a></li>
    </ul>
		<div class="tab-content" id="tabs1"><br />CG Category Name' . '' . $category_inputs_string . '</div>
		<div class="tab-content" id="tabs8"><br />' . 'TT Category Name' . '<br />' . tep_draw_input_field('tt_categories_name') . '</div>
	</div>';

          $contents[] = array('text' => $tabs_name_string);

          $tabs_desc_string .= '
  <div id="wrapper">
    <ul class="desctabs">
        <li><a href="#" class="defaultdesctab" rel="desctabs1">CG</a></li>
		<li><a href="#" rel="desctabs8">TT</a></li>
    </ul>
		<div class="desctab-content" id="desctabs1"><br />' . 'CG Category Desc' . '' . $category_desc_inputs_string . '</div>
		<div class="desctab-content" id="desctabs8"><br />' . 'TT Category Desc' . '<br />' . tep_draw_textarea_field('tt_categories_description', '', 20, 5) . '</div>
	</div>';

          $contents[] = array('text' => $tabs_desc_string);

          $tabs_image_string .= '
  <div id="wrapper">
    <ul class="imagetabs">
        <li><a href="#" class="defaultimagetab" rel="imagetabs1">CG</a></li>
		<li><a href="#" rel="imagetabs8">TT</a></li>
    </ul>
		<div class="imagetab-content" id="imagetabs1"><br />' . 'CG Category Image' . '<br />' . tep_draw_file_field('categories_image') . '</div>
		<div class="imagetab-content" id="imagetabs8"><br />' . 'TT Category Image' . '<br />' . tep_draw_file_field('tt_categories_image') . '</div>
	</div>';

          $contents[] = array('text' => $tabs_image_string);
          #
          $tabs_meta_string = '
  <div id="wrapper">
    <ul class="metatabs">
        <li><a href="#" class="defaultmetatab" rel="metatabs1">CG</a></li>
		<li><a href="#" rel="metatabs8">TT</a></li>
    </ul>
	<div class="metatab-content" id="metatabs1"><br />' . 'Header Tags Category Title' . $category_htc_title_string . '<br />' . 'Header Tags Category Description' . $category_htc_desc_string . '<br />' . 'Header Tags Category Keywords' . $category_htc_keywords_string . '</div>
	<div class="metatab-content" id="metatabs8"><br />' . 'TT Header Tags Category Title' . '<br />' . tep_draw_input_field('tt_categories_htc_title_tag') . '<br />' . 'TT Header Tags Category Description' . '<br />' . tep_draw_input_field('tt_categories_htc_desc_tag') . '<br />' . 'TT Header Tags Category Keywords' . '<br />' . tep_draw_input_field('tt_categories_htc_keywords_tag') . '</div>
	</div>';

          $contents[] = array('text' => $tabs_meta_string);

          $contents[] = array('text' => '<br />Sites:<br />

			<table>
			  <tr>
			    <td>CG</td>
				<td>TT</td>
			  </tr>
			  <tr>
			    <td>' .  tep_draw_ms_checkbox_field('store_cg', '1', 'CHECKED') . '</td>
				<td>' .  tep_draw_ms_checkbox_field('store_tt', '1', '') . '</td>
			  </tr>
			</table>');

          $live_cat_tree = array();
          $live_cat_tree = tep_get_category_tree();
          $cat_tree_blank = array('id' => '', 'text' => 'Use Default');
          array_unshift($live_cat_tree, $cat_tree_blank);

          $contents[] = array('text' => '
  <div id="wrapper">
    <ul class="sorttabs">
        <li><a href="#" class="defaultsorttab" rel="sorttabs1">CG</a></li>
		<li><a href="#" rel="sorttabs8">TT</a></li>
    </ul>
		<div class="sorttab-content" id="sorttabs1"><br />CG Category Sort Order ' . tep_draw_input_field('sort_order', $cInfo->sort_order, 'size="2"') . '</div>
		<div class="sorttab-content" id="sorttabs8"><br />' . 'TT Category Sort Order ' . tep_draw_input_field('tt_sort_order', '', 'size="2"') . '<br />TT Category Parent ' . tep_draw_pull_down_menu('tt_parent_id', $live_cat_tree, '') . '</div>
	</div>');

          $contents[] = array('text' => '<br />Show images for categories under this category? ' . tep_draw_ms_checkbox_field('sub_cats_show_image', '1', '') . '');

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath), null) . '<br /><br /><br /><br />');
          break;

        case 'edit_category':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_CATEGORY . '</b>');

          $contents = array('form' => tep_draw_form('categories', FILENAME_CATEGORIES_MASTER, 'action=update_category&cPath=' . $cPath, 'post', 'enctype="multipart/form-data"') . tep_draw_hidden_field('categories_id', $cInfo->categories_id));
          $contents[] = array('text' => TEXT_EDIT_INTRO);

          $category_inputs_string = '';
          $languages = tep_get_languages();
          for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
            $category_inputs_string .= '<br />' . tep_draw_input_field('categories_name[' . $languages[$i]['id'] . ']', tep_get_category_name($cInfo->categories_id, $languages[$i]['id']));
            $category_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_name_highlight[' . $languages[$i]['id'] . ']', '1', tep_get_category_name_highlight($cInfo->categories_id, $languages[$i]['id'])) . ' Highlight Red';
            $category_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_name_highlight_black[' . $languages[$i]['id'] . ']', '1', tep_get_category_name_highlight_black($cInfo->categories_id, $languages[$i]['id'])) . ' Highlight Black';
            $category_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_name_gap[' . $languages[$i]['id'] . ']', '1', tep_get_category_name_gap($cInfo->categories_id, $languages[$i]['id'])) . ' Add Space Above';

            // HTC BOC
            $category_htc_title_string .= '<br />' . tep_draw_input_field('categories_htc_title_tag[' . $languages[$i]['id'] . ']', tep_get_category_htc_title($cInfo->categories_id, $languages[$i]['id']));
            $category_htc_desc_string .= '<br />' . tep_draw_input_field('categories_htc_desc_tag[' . $languages[$i]['id'] . ']', tep_get_category_htc_desc($cInfo->categories_id, $languages[$i]['id']));
            $category_htc_keywords_string .= '<br />' . tep_draw_input_field('categories_htc_keywords_tag[' . $languages[$i]['id'] . ']', tep_get_category_htc_keywords($cInfo->categories_id, $languages[$i]['id']));
            // HTC EOC
          }
          $category_desc_inputs_string = '';
          $languages = tep_get_languages();
          for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
            $category_desc_inputs_string .= '<br />' . tep_draw_textarea_field('categories_description[' . $languages[$i]['id'] . ']', '', 20, 5, tep_get_category_description($cInfo->categories_id, $languages[$i]['id']));
            $category_desc_inputs_string .= '' . tep_draw_ms_checkbox_field('categories_desc_switch[' . $languages[$i]['id'] . ']', '1', tep_get_category_desc_switch($cInfo->categories_id, $languages[$i]['id'])) . ' Override Link';
            $category_desc_inputs_string .= '<br />Read More / SEO text<br />' . tep_draw_textarea_field('categories_read_more[' . $languages[$i]['id'] . ']', '', 20, 5, tep_get_category_read_more($cInfo->categories_id, $languages[$i]['id']));
          }

          $tabs_name_string .= '
  <div id="wrapper">
    <ul class="tabs">
        <li><a href="#" class="defaulttab" rel="tabs1">CG</a></li>
		<li><a href="#" rel="tabs8">TT</a></li>
    </ul>
		<div class="tab-content" id="tabs1"><br />' . 'CG Category Name' . '' . $category_inputs_string . '</div>
		<div class="tab-content" id="tabs8"><br />' . 'TT Category Name' . '<br />' . tep_draw_input_field('tt_categories_name', tep_get_category_extra_name($cInfo->categories_id, 8)) . '</div>
	</div>';

          $contents[] = array('text' => $tabs_name_string);

          $tabs_desc_string .= '
  <div id="wrapper">
    <ul class="desctabs">
        <li><a href="#" class="defaultdesctab" rel="desctabs1">CG</a></li>
		<li><a href="#" rel="desctabs8">TT</a></li>
    </ul>
		<div class="desctab-content" id="desctabs1"><br />' . 'CG Category Desc' . '' . $category_desc_inputs_string . '</div>
		<div class="desctab-content" id="desctabs8"><br />' . 'TT Category Desc' . '<br />' . tep_draw_textarea_field('tt_categories_description', 'soft', 20, 5, tep_get_category_extra_description($cInfo->categories_id, 8)) . '</div>
	</div>';

          $contents[] = array('text' => $tabs_desc_string);

          $tabs_image_string .= '
  <div id="wrapper">
    <ul class="imagetabs">
        <li><a href="#" class="defaultimagetab" rel="imagetabs1">CG</a></li>
		<li><a href="#" rel="imagetabs8">TT</a></li>
    </ul>
		<div class="imagetab-content" id="imagetabs1"><br />' . tep_image(DIR_WS_CATALOG_IMAGES . $cInfo->categories_image, $cInfo->categories_name) . '<br />' . DIR_WS_CATALOG_IMAGES . '<br /><b>' . $cInfo->categories_image . '</b><br />' . TEXT_CATEGORIES_IMAGE . tep_draw_file_field('categories_image') . '</div>
		<div class="imagetab-content" id="imagetabs8"><br />' . tep_image(DIR_WS_CATALOG_IMAGES . tep_get_category_extra_image($cInfo->categories_id, 8), $cInfo->categories_name) . '<br />' . DIR_WS_CATALOG_IMAGES . '<br /><b>' . tep_get_category_extra_image($cInfo->categories_id, 8) . '</b><br />' . 'TT Category Image' . '<br />' . tep_draw_file_field('tt_categories_image') . '</div>
	</div>';

          $contents[] = array('text' => $tabs_image_string);

          // BOF: More Pics 6 not working rp removed
          // $contents[] = array('text' => '<br />' . tep_draw_checkbox_field('delete_category_image', 'yes', false) . TEXT_DELETE_IMAGE);
          // EOF: More Pics 6

          $tabs_meta_string = '
  <div id="wrapper">
    <ul class="metatabs">
        <li><a href="#" class="defaultmetatab" rel="metatabs1">CG</a></li>
		<li><a href="#" rel="metatabs8">TT</a></li>
    </ul>
	<div class="metatab-content" id="metatabs1"><br />' . 'Header Tags Category Title' . $category_htc_title_string . '<br />' . 'Header Tags Category Description' . $category_htc_desc_string . '<br />' . 'Header Tags Category Keywords' . $category_htc_keywords_string . '</div>

	<div class="metatab-content" id="metatabs8"><br />' . 'TT Header Tags Category Title' . '<br />' . tep_draw_input_field('tt_categories_htc_title_tag', tep_get_category_extra_htc_title_tag($cInfo->categories_id, 8)) . '<br />' . 'TT Header Tags Category Description' . '<br />' . tep_draw_input_field('tt_categories_htc_desc_tag', tep_get_category_extra_htc_desc_tag($cInfo->categories_id, 8)) . '<br />' . 'TT Header Tags Category Keywords' . '<br />' . tep_draw_input_field('tt_categories_htc_keywords_tag', tep_get_category_extra_htc_keywords_tag($cInfo->categories_id, 8)) . '</div>

	</div>';

          $contents[] = array('text' => $tabs_meta_string);

          if ($cInfo->store_cg == 1) {
            $cat_checked_cg = 'CHECKED';
          } else {
            $cat_checked_cg = '';
          }
          if ($cInfo->store_ho == 1) {
            $cat_checked_ho = 'CHECKED';
          } else {
            $cat_checked_ho = '';
          }
          if ($cInfo->store_hs == 1) {
            $cat_checked_hs = 'CHECKED';
          } else {
            $cat_checked_hs = '';
          }
          if ($cInfo->store_wm == 1) {
            $cat_checked_wm = 'CHECKED';
          } else {
            $cat_checked_wm = '';
          }
          if ($cInfo->store_dc == 1) {
            $cat_checked_dc = 'CHECKED';
          } else {
            $cat_checked_dc = '';
          }
          if ($cInfo->store_tt == 1) {
            $cat_checked_tt = 'CHECKED';
          } else {
            $cat_checked_tt = '';
          }

          $contents[] = array('text' => '<br />Sites:<br />

			<table>
			  <tr>
			    <td>CG</td>
				<td>TT</td>
			  </tr>
			  <tr>
			    <td>' .  tep_draw_ms_checkbox_field('store_cg', '1', $cat_checked_cg) . '</td>
				<td>' .  tep_draw_ms_checkbox_field('store_tt', '1', $cat_checked_tt) . '</td>
			  </tr>
			</table>');

          $live_cat_tree = array();
          $live_cat_tree = tep_get_category_tree();
          $cat_tree_blank = array('id' => '', 'text' => 'Use Default');
          array_unshift($live_cat_tree, $cat_tree_blank);

          $contents[] = array('text' => '
  <div id="wrapper">
    <ul class="sorttabs">
        <li><a href="#" class="defaultsorttab" rel="sorttabs1">CG</a></li>
		<li><a href="#" rel="sorttabs8">TT</a></li>
    </ul>
		<div class="sorttab-content" id="sorttabs1"><br />CG Category Sort Order ' . tep_draw_input_field('sort_order', $cInfo->sort_order, 'size="2"') . '</div>
		<div class="sorttab-content" id="sorttabs8"><br />' . 'TT Category Sort Order ' . tep_draw_input_field('tt_sort_order', tep_get_category_extra_sort_order($cInfo->categories_id, 8), 'size="2"') . '<br />TT Category Parent ' . tep_draw_pull_down_menu('tt_parent_id', $live_cat_tree, tep_get_category_extra_parent_id($cInfo->categories_id, 8)) . '</div>
	</div>');


          // ###################### Added Categories Enable / Disable #################
          $contents[] = array('text' => '<br />' . TEXT_EDIT_STATUS . '<br />' . tep_draw_input_field('categories_status', $cInfo->categories_status, 'size="2"') . '1=Enabled 0=Disabled');

          // Mega Menu Columns
          $contents[] = array('text' => '<br />Mega Menu Column<br />' . tep_draw_input_field('mm_col', $cInfo->mm_col, 'size="2"') . ' Column 1 - 4');
          $contents[] = array('text' => '<br />Show images for categories under this category? ' . tep_draw_ms_checkbox_field('sub_cats_show_image', '1', $cInfo->sub_cats_show_image) . '');

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id), null) . '<br /><br /><br /><br />');

          // ##################### End Added Categories Enable / Disable ###################
          break;
        case 'delete_category':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_CATEGORY . '</b>');

          $contents = array('form' => tep_draw_form('categories', FILENAME_CATEGORIES_MASTER, 'action=delete_category_confirm&cPath=' . $cPath) . tep_draw_hidden_field('categories_id', $cInfo->categories_id));
          $contents[] = array('text' => TEXT_DELETE_CATEGORY_INTRO);
          $contents[] = array('text' => '<br /><b>' . $cInfo->categories_name . '</b>');
          if ($cInfo->childs_count > 0) $contents[] = array('text' => '<br />' . sprintf(TEXT_DELETE_WARNING_CHILDS, $cInfo->childs_count));
          if ($cInfo->products_count > 0) $contents[] = array('text' => '<br />' . sprintf(TEXT_DELETE_WARNING_PRODUCTS, $cInfo->products_count));

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id), null));
          break;
        case 'move_category':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_MOVE_CATEGORY . '</b>');

          $contents = array('form' => tep_draw_form('categories', FILENAME_CATEGORIES_MASTER, 'action=move_category_confirm&cPath=' . $cPath) . tep_draw_hidden_field('categories_id', $cInfo->categories_id));
          $contents[] = array('text' => sprintf(TEXT_MOVE_CATEGORIES_INTRO, $cInfo->categories_name));
          $contents[] = array('text' => '<br />' . sprintf(TEXT_MOVE, $cInfo->categories_name) . '<br />' . tep_draw_pull_down_menu('move_to_category_id', tep_get_category_tree(), $current_category_id));

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_MOVE, 'disk', null, 'primary') . '&nbsp;' . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id), null));
          break;
        case 'delete_single_product':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_PRODUCT . '</b>');

          $contents = array('form' => tep_draw_form('products', FILENAME_CATEGORIES_MASTER, 'action=delete_single_product_confirm&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id));
          $contents[] = array('text' => TEXT_DELETE_PRODUCT_INTRO);
          $contents[] = array('text' => '<br /><b>' . $pInfo->products_name . '</b>');

          $product_categories_string = '';
          $product_categories = tep_generate_category_path($pInfo->products_id, 'product');
          for ($i = 0, $n = sizeof($product_categories); $i < $n; $i++) {
            $category_path = '';
            for ($j = 0, $k = sizeof($product_categories[$i]); $j < $k; $j++) {
              $category_path .= $product_categories[$i][$j]['text'] . '&nbsp;&gt;&nbsp;';
            }
            $category_path = substr($category_path, 0, -16);
            $product_categories_string .= tep_draw_checkbox_field('product_categories[]', $product_categories[$i][sizeof($product_categories[$i]) - 1]['id'], true) . '&nbsp;' . $category_path . '<br />';
          }
          $product_categories_string = substr($product_categories_string, 0, -4);

          $contents[] = array('text' => '<br />' . $product_categories_string);

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'disk', null, 'primary') . '&nbsp;' . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id), null));
          break;
        case 'delete_product':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_PRODUCT . '</b>');

          $all_categories_string = tep_draw_checkbox_field('delete_from_all_cats', '1', false) . '&nbsp;Delete from all categories?<br />';

          $contents[] = array('text' => TEXT_DELETE_PRODUCT_INTRO);
          $contents[] = array('text' => '<br /><b>' . $all_categories_string . '</b>');

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'disk', null, 'primary') . '&nbsp;' . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id), null));

          break;
        case 'move_product':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_MOVE_PRODUCT . '</b>');

          $contents[] = array('text' => sprintf(TEXT_MOVE_PRODUCTS_INTRO, $pInfo->products_name));
          $contents[] = array('text' => '<br />' . TEXT_INFO_CURRENT_CATEGORIES . '<br /><b>' . tep_output_generated_category_path($pInfo->products_id, 'product') . '</b>');

          $contents[] = array('text' => tep_draw_pull_down_menu('move_to_category_id', tep_get_category_tree(), $current_category_id));

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_MOVE, 'disk', null, 'primary') . '&nbsp;' . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id), null));

          break;
        case 'copy_to':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_COPY_TO . '</b>');

          $contents[] = array('text' => TEXT_INFO_COPY_TO_INTRO);
          $contents[] = array('text' => '<br />' . TEXT_INFO_CURRENT_CATEGORIES . '<br /><b>' . tep_output_generated_category_path($pInfo->products_id, 'product') . '</b>');
          $contents[] = array('text' => '<br />' . TEXT_CATEGORIES . '<br />' . tep_draw_pull_down_menu('categories_id', tep_get_category_tree(), $current_category_id));
          $contents[] = array('text' => '<br />' . TEXT_HOW_TO_COPY . '<br />' . tep_draw_radio_field('copy_as', 'link', true) . ' ' . TEXT_COPY_AS_LINK . '<br />' . tep_draw_radio_field('copy_as', 'duplicate') . ' ' . TEXT_COPY_AS_DUPLICATE);

          $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button(IMAGE_COPY, 'disk', null, 'primary') . ' ' . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id), null));
          break;
        default:
          if ($rows > 0) {
            if (isset($cInfo) && is_object($cInfo)) { // category info box contents
              $heading[] = array('text' => '<b>' . $cInfo->categories_name . '</b>');

              $contents[] = array('align' => 'center', 'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id . '&action=edit_category'), null) . ' ' .  tep_draw_button(IMAGE_DELETE, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id . '&action=delete_category'), null) . ' ' . tep_draw_button(IMAGE_MOVE, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id . '&action=move_category'), null));

              $contents[] = array('text' => tep_draw_form('categories', FILENAME_CATEGORIES_MASTER, 'action=save_category_notes&cPath=' . $cPath) . tep_draw_hidden_field('categories_id', $cInfo->categories_id));
              $contents[] = array('align' => 'center', 'text' => 'Category Notes');
              $contents[] = array('align' => 'center', 'text' => tep_draw_textarea_field('category_admin_notes', 'soft', 20, 10, $cInfo->category_admin_notes, 'style="width:90%;"'));
              if (tep_not_null($cInfo->category_admin_notes_modified)) $contents[] = array('align' => 'center', 'text' => '<br />Notes last modified: ' . tep_datetime_short($cInfo->category_admin_notes_modified));
              $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button('Save Notes', 'disk', null, 'primary'));
              $contents[] = array('text' => '</form><br />');

              $contents[] = array('text' => '<br />' . TEXT_DATE_ADDED . ' ' . tep_date_short($cInfo->date_added));
              if (tep_not_null($cInfo->last_modified)) $contents[] = array('text' => TEXT_LAST_MODIFIED . ' ' . tep_date_short($cInfo->last_modified));
              $contents[] = array('text' => '<br />' . tep_info_image($cInfo->categories_image, $cInfo->categories_name, HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT) . '<br />' . $cInfo->categories_image);
              $contents[] = array('text' => '<br />' . TEXT_SUBCATEGORIES . ' ' . $cInfo->childs_count . '<br />' . TEXT_PRODUCTS . ' ' . $cInfo->products_count);
            } elseif (isset($pInfo) && is_object($pInfo)) { // product info box contents
              $heading[] = array('text' => '<b>' . tep_get_products_name($pInfo->products_id, $languages_id) . '</b>');

              $contents[] = array('align' => 'center', 'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=new_product'), null) . ' ' . tep_draw_button('Multi Delete', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=delete_product'), null) . ' ' . tep_draw_button('Single Delete', 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=delete_single_product'), null) . ' ' . tep_draw_button(IMAGE_MOVE, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=move_product'), null) . ' ' . tep_draw_button(IMAGE_COPY_TO, 'document', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=copy_to'), null) . ' ' . tep_draw_button(IMAGE_RELATED_PRODUCTS, 'document', tep_href_link(FILENAME_RELATED_REVIEWS, 'cPath=' . $cPath . '&products_id=' . $pInfo->products_id), null, array('newwindow' => true)) . ' ' . tep_draw_button('Mitchell Recommends', 'document', tep_href_link(FILENAME_MITCHELL_PRODUCTS, 'products_id_view=' . $pInfo->products_id), null, array('newwindow' => true)) . ' ' . tep_draw_button('Cross Sell Products', 'document', tep_href_link(FILENAME_UPDATE_CROSS_SELL, 'cPath=' . $cPath . '&products_id=' . $pInfo->products_id), null, array('newwindow' => true)));

              //////////////////////////////////////

              $contents[] = array('text' => tep_draw_form('products', FILENAME_CATEGORIES_MASTER, 'action=save_product_notes&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id));
              $contents[] = array('align' => 'center', 'text' => 'Product Notes');
              $contents[] = array('align' => 'center', 'text' => tep_draw_textarea_field('products_admin_notes', 'soft', 20, 10, $pInfo->products_admin_notes, 'style="width:90%;"'));
              if (tep_not_null($pInfo->products_admin_notes_modified)) $contents[] = array('align' => 'center', 'text' => '<br />Notes last modified: ' . tep_datetime_short($pInfo->products_admin_notes_modified));
              $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button('Save Notes', 'disk', null, 'primary'));
              $contents[] = array('text' => '</form><br /><br />');

              $contents[] = array('align' => 'center', 'text' => tep_draw_button('Copy to WM', 'disk', tep_href_link(FILENAME_CATEGORIES_MASTER, 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=copy_to_wm'), null) . '<br /><br />');

              if ($pInfo->products_status == '1' || $pInfo->products_status == '2') {

                $contents[] = array('text' => tep_draw_form('products', FILENAME_CATEGORIES_MASTER, 'action=send_product_tweet&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id));
                $contents[] = array('align' => 'center', 'text' => '<img src="images/icons/twitter.png"><br /><strong>' . $pInfo->products_name . '</strong><br />Tweet Product (optional message):');
                $contents[] = array('align' => 'center', 'text' => tep_draw_textarea_field('products_admin_tweet', 'soft', 10, 5, $pInfo->products_admin_tweet, 'style="width:90%;"'));

                require_once('includes/modules/twitter/URLResolver.php');

                $resolver = new URLResolver();

                $catalog_raw_url = tep_catalog_href_link('product_info.php', 'products_id=' . $pInfo->products_id);

                $contents[] = array('align' => 'center', 'text' => '<a href="' . $resolver->resolveURL($catalog_raw_url)->getURL() . '" target="_blank">' . $resolver->resolveURL($catalog_raw_url)->getURL() . '</a>');

                if (tep_not_null($pInfo->products_tweet_date)) $contents[] = array('align' => 'center', 'text' => '<br />Last Tweeted: ' . tep_datetime_short($pInfo->products_tweet_date));
                $contents[] = array('align' => 'center', 'text' => '<br />' . tep_draw_button('Tweet', 'disk', null, 'primary'));
                $contents[] = array('text' => '</form>');
              }

              if (tep_not_null($pInfo->products_model)) $contents[] = array('text' => 'Model / Ref: ' . $pInfo->products_model);

              if (tep_not_null($pInfo->products_created_date)) {
                $contents[] = array('text' => '<br />' . TEXT_DATE_ADDED . '* ' . tep_date_short($pInfo->products_created_date));
              } elseif (tep_not_null($pInfo->products_date_added) && (strtotime($pInfo->products_date_added) < strtotime($pInfo->products_last_modified) || !tep_not_null($pInfo->products_last_modified))) {
                $contents[] = array('text' => '<br />' . TEXT_DATE_ADDED . ' ' . tep_date_short($pInfo->products_date_added));
              }

              if (tep_not_null($pInfo->products_last_modified)) $contents[] = array('text' => TEXT_LAST_MODIFIED . ' ' . tep_date_short($pInfo->products_last_modified));

              if (date('Y-m-d') < $pInfo->products_date_available) $contents[] = array('text' => TEXT_DATE_AVAILABLE . ' ' . tep_date_short($pInfo->products_date_available));

              $oos_query = tep_db_query("select date_out_of_stock from products_stock_date where products_id = '" . (int)$pInfo->products_id . "'");
              while ($oos = tep_db_fetch_array($oos_query)) {
                $contents[] = array('text' => 'Last OOS:' . tep_date_short($oos['date_out_of_stock']));
              }

              $contents[] = array('text' => '<br />' . tep_info_image($pInfo->products_image, $pInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '<br />' . $pInfo->products_image);
              // BOF: More Pics 6
              $contents[] = array('text' => '<br />' . tep_info_image($pInfo->products_subimage7, $pInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '<br />' . $pInfo->products_subimage7);
              // EOF: More Pics 6
              $contents[] = array('text' => '<br />' . TEXT_PRODUCTS_PRICE_INFO . ' ' . $currencies->format($pInfo->products_price) . '<br />' . TEXT_PRODUCTS_QUANTITY_INFO . ' ' . $pInfo->products_quantity);
              $contents[] = array('text' => '<br />' . TEXT_PRODUCTS_AVERAGE_RATING . ' ' . number_format((float)$pInfo->average_rating, 2) . '%');

              if ($pInfo->products_free_shipping == "1") {
                $free_shipping_indicator = TEXT_YES;
              } else {
                $free_shipping_indicator = TEXT_NO;
              }
              $contents[] = array('text' => '<br />' . TEXT_PRODUCTS_SHIPPING . ' ' . $free_shipping_indicator . '');

              if ($pInfo->suppliers_id > 0) {
                $suppliers_query = tep_db_query("select suppliers_name from " . TABLE_SUPPLIERS . " where suppliers_id = '" . $pInfo->suppliers_id . "' ");
                $suppliers = tep_db_fetch_array($suppliers_query);
                $contents[] = array('text' => '<br />Supplier: ' . $suppliers['suppliers_name'] . '');
              }
            }
          } else { // create category/product info
            $heading[] = array('text' => '<b>' . EMPTY_CATEGORY . '</b>');

            $contents[] = array('text' => TEXT_NO_CHILD_CATEGORIES_OR_PRODUCTS);
          }
          break;
      }

      if ((tep_not_null($heading)) && (tep_not_null($contents))) {
        echo '            <td width="25%" valign="top">' . "\n";

        $box = new box;
        echo $box->infoBox($heading, $contents);

        echo '            </td>' . "\n";
      }
      ?>
    </tr>
  </table>
  </td>
  </tr>
  </table>
<?php
    }
    require(DIR_WS_INCLUDES . 'template_bottom.php');
    require(DIR_WS_INCLUDES . 'application_bottom.php');
?>