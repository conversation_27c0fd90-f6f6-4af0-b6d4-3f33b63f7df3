<?php

    date_default_timezone_set('Europe/London');

	$current_time = date("Y-m-d") . '_' . date("G-i");

	$yesterday_date = date("Y-m-d", strtotime("yesterday"));

    $time1 = strtotime("-1 week", time());
    $date1 = date("Y-m-d", $time1);

  function dblink1_input($string) {
    global $dblink1;
    return mysqli_real_escape_string($dblink1, $string);
  }

  function dblink2_input($string) {
    global $dblink2;
    return mysqli_real_escape_string($dblink2, $string);
  }

  function tep_calculate_profit_margin_new($sale_price_ex_vat, $trade_price_ex_vat, $trade_price_discount){

    if ($sale_price_ex_vat > 0) {

    if ($trade_price_discount != 0) {
      $trade_price_discount_amount = $trade_price_ex_vat * ($trade_price_discount / 100);
      $trade_price_ex_vat_with_discount = $trade_price_ex_vat - $trade_price_discount_amount;
      $product_cost_price = $trade_price_ex_vat_with_discount;
    } else {
      $product_cost_price = $trade_price_ex_vat;
    }

      $margin = ((($sale_price_ex_vat - $product_cost_price) / $sale_price_ex_vat) * 100);

    } else {
      $margin = 0;
    }


      return $margin;

  }

  // 1) Copy data from all sites into report table

    // Connect to Turmeaus

    // $dblink1_Host = "**************:3306"; // use your real host name
    $dblink1_Host = "**************:3306"; // use your real host name
    $dblink1_UserName = "turmeaus_user";   // use your real login user name
    $dblink1_Password = "VZR3KAhWCSSG9gb9Cvp7";   // use your real login password
    $dblink1_DataBaseName = "turmeaus_db"; // use your real database name

    $dblink1 = mysqli_connect( "$dblink1_Host", "$dblink1_UserName", "$dblink1_Password", "$dblink1_DataBaseName" );
    if(!$dblink1){ die("connection object 1 not created: ".mysqli_error($dblink1)); }
    if(mysqli_connect_errno()) { die("Connect failed: ".mysqli_connect_errno()." : ". mysqli_connect_error()); }
	mysqli_select_db($dblink1, $dblink1_DataBaseName);  // select database 1

    // Connect to C.Gars

    $dblink2_Host = "localhost"; // use your real host name
    $dblink2_UserName = "devcgars_person";   // use your real login user name
    $dblink2_Password = "_vb}pU&~I2#n";   // use your real login password
    $dblink2_DataBaseName = "devcgars_db"; // use your real database name

    $dblink2 = mysqli_connect( "$dblink2_Host", "$dblink2_UserName", "$dblink2_Password", "$dblink2_DataBaseName" );
    if(!$dblink2){ die("connection object 2 not created: ".mysqli_error($dblink2)); }
    if(mysqli_connect_errno()) { die("Connect failed: ".mysqli_connect_errno()." : ". mysqli_connect_error()); }
	mysqli_select_db($dblink2, $dblink2_DataBaseName);  // select database 2

	// select from Turmeaus
	$turmeaus_wire_sales = 0;
  $turmeaus_paypal_sales = 0;
  $turmeaus_sage_sales = 0;

	$order_query = mysqli_query($dblink1,"SELECT o.orders_id, o.payment_method FROM orders o WHERE DATE(o.date_purchased) = DATE(NOW() - INTERVAL 1 DAY) ORDER BY o.orders_id"); // select all content
    while ($order_row = mysqli_fetch_array($order_query, MYSQLI_ASSOC) ) {

    $order_id_output .= $order_row['orders_id'] . ' - TT<br />';

	  $order_totals_query = mysqli_query($dblink1,"select ot.value from orders_total ot where ot.orders_id = " . $order_row['orders_id'] . " and class = 'ot_total'"); // select all content
	  while ($order_totals_row = mysqli_fetch_array($order_totals_query, MYSQLI_ASSOC) ) {
      if (strpos($order_row['payment_method'], 'Transfer') !== false) {
        $turmeaus_wire_sales += $order_totals_row['value'];
      } elseif (strpos($order_row['payment_method'], 'PayPal') !== false) {
        $turmeaus_paypal_sales += $order_totals_row['value'];
      } else { // its Sage
        $turmeaus_sage_sales += $order_totals_row['value'];
      }
    }

	  }

    // Close Turmeaus Connection

    mysqli_close($dblink1);

	// select from CGARS DBLINK2
  $cgars_wire_sales = 0;
  $cgars_phone_sales = 0;
  $cgars_paypal_sales = 0;
  $cgars_sage_sales = 0;
  $cgars_margin_sum = array(); // initialize
  $cgars_product_count = 0;

  $count_attributes = 0;

    $order_query = mysqli_query($dblink2,"SELECT o.orders_id, o.payment_method FROM orders o WHERE DATE(o.date_purchased) = DATE(NOW() - INTERVAL 1 DAY) ORDER BY o.orders_id"); // select all content
    while ($order_row = mysqli_fetch_array($order_query, MYSQLI_ASSOC) ) {

	   $order_id_output .= $order_row['orders_id'] . ' - CG<br />';

     $order_totals_query = mysqli_query($dblink2,"select ot.value from orders_total ot where ot.orders_id = " . $order_row['orders_id'] . " and class = 'ot_total'"); // select all content
     while ($order_totals_row = mysqli_fetch_array($order_totals_query, MYSQLI_ASSOC) ) {
       if (strpos($order_row['payment_method'], 'Transfer') !== false) {
         $cgars_wire_sales += $order_totals_row['value'];
       } elseif (strpos($order_row['payment_method'], 'Phone') !== false) {
         $cgars_phone_sales += $order_totals_row['value'];
       } elseif (strpos($order_row['payment_method'], 'PayPal') !== false) {
         $cgars_paypal_sales += $order_totals_row['value'];
       } else { // its Sage
         $cgars_sage_sales += $order_totals_row['value'];
       }
     }

    }

	// Close CG Connection

    mysqli_close($dblink2);

  // START APPLICATION TOP

  chdir('/home/<USER>/public_html/cgadmin');

  $language = 'english';

// Set the level of error reporting
  error_reporting(E_ALL & ~E_NOTICE);

// Set the local configuration parameters - mainly for developers
  if (file_exists('includes/local/configure.php')) include('includes/local/configure.php');

// Include application configuration parameters
  require('includes/configure.php');

// some code to solve compatibility issues
  require(DIR_WS_FUNCTIONS . 'compatibility.php');

// include the list of project filenames
  require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
  require(DIR_WS_INCLUDES . 'database_tables.php');

// include the database functions
  require(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the database... now
  tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
  $configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
  while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
  }

// define our general functions used application-wide
  require(DIR_WS_FUNCTIONS . 'general.php');
  require(DIR_WS_FUNCTIONS . 'html_output.php');

// define how the session functions will be used
  require(DIR_WS_FUNCTIONS . 'sessions.php');

// include the language translations
  require(DIR_WS_LANGUAGES . $language . '.php');

// define our localization functions
  require(DIR_WS_FUNCTIONS . 'localization.php');

// Include validation functions (right now only email address)
  require(DIR_WS_FUNCTIONS . 'validations.php');

// setup our boxes
  require(DIR_WS_CLASSES . 'table_block.php');
  require(DIR_WS_CLASSES . 'box.php');

// email classes
  require(DIR_WS_CLASSES . 'mime.php');
  require(DIR_WS_CLASSES . 'email.php');

// language
  include(DIR_WS_CLASSES . 'language.php');
  $lng = new language();
  $lng->set_language($language);
  $language = $lng->language['directory'];
  $languages_id = $lng->language['id'];

  require(DIR_WS_CLASSES . 'currencies.php');
  $currencies = new currencies();

// END APPLICATION TOP

      $cgars_wire_sales = 0; // disabled 10/04/2025
      $turmeaus_wire_sales = 0; // disabled 10/04/2025

      $cgars_sales = ($cgars_wire_sales + $cgars_phone_sales + $cgars_paypal_sales + $cgars_sage_sales);
      $turmeaus_sales = ($turmeaus_wire_sales + $turmeaus_paypal_sales + $turmeaus_sage_sales);

      $total_wire_sales = ($cgars_wire_sales + $turmeaus_wire_sales);
      $total_phone_sales = $cgars_phone_sales;
      $total_paypal_sales = ($cgars_paypal_sales + $turmeaus_paypal_sales);
      $total_sage_sales = ($cgars_sage_sales + $turmeaus_sage_sales);

      $total_sales = ($cgars_sales + $turmeaus_sales);

      $email_generic_heading = tep_date_long($yesterday_date);
      $email_generic_sub_heading = '';

      // load email templates
      include('../' . DIR_WS_LANGUAGES . 'english/email_generic_template.php');

      $email_message = '';
	    $email_message .= '<br />Yesterday\'s gross order value received:<br /><br /><strong style="font-size: 32px">&nbsp;&nbsp;&pound;' . number_format((float)$total_sales, 2, '.', ',') . '</strong><br /><br />';
      $email_message .= 'SagePay:&nbsp;&nbsp;&pound;' . number_format((float)$total_sage_sales, 2, '.', ',') . '<br />';
      // $email_message .= 'PayPal:&nbsp;&nbsp;&pound;' . number_format((float)$total_paypal_sales, 2, '.', ',') . '<br />';
      // $email_message .= 'Bank Transfers:&nbsp;&nbsp;&pound;' . number_format((float)$total_wire_sales, 2, '.', ',') . '<br />'; // disabled 10/04/2025
      $email_message .= 'Phone Sales:&nbsp;&nbsp;&pound;' . number_format((float)$total_phone_sales, 2, '.', ',') . '<br /><br />';

      $email_message .= 'This figure includes sales from C.Gars and Turmeaus. ';
	    $email_message .= 'All order statuses are included (e.g Dispatched, Partially Shipped, Cancelled, Refunded etc). ';
	    $email_message .= 'Shipping costs, VAT, loyalty point, gift voucher and promotion code deductions are all included.<br /><br />';

      $email_text  = EMAIL_GENERIC_TEMPLATE_START;
      $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;
      $email_text .= '<h4 style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 20px;margin-bottom: 8px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Dear Ron,</h4><p class="mbe" style="font-family: Helvetica, Arial, sans-serif;font-size: 16px;line-height: 23px;color: #616161;mso-line-height-rule: exactly;display: block;margin-top: 0;margin-bottom: 0;">' . $email_message . '</p>';
      $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
      $email_text .= EMAIL_GENERIC_TEMPLATE_END;
	
  // tep_mail('Richard Passey', '<EMAIL>', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

  tep_mail('Laura Bitters', '<EMAIL>', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
  tep_mail('Sales - C.Gars', '<EMAIL>', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	tep_mail('Amy - C.Gars', '<EMAIL>', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
  tep_mail('Martin', '<EMAIL>', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, '<EMAIL>');
  tep_mail('Dominique', '<EMAIL>', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
  tep_mail('Frederic', '<EMAIL>', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
  tep_mail('Andy - C.Gars', '<EMAIL> ', 'Your Daily Order Value Report is Ready', $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

    // echo '<a href="https://www.cgarsltd.co.uk/reports/' . $export_filename . ((EP_EXCEL_SAFE_OUTPUT == true)?'.csv':'.txt') . '">Click Here to Download</a>';

	// echo $order_id_output;

  // echo $email_text;

	die();

// RP END DOWNLOAD

   require(DIR_WS_INCLUDES . 'application_bottom.php');
?>