<?php
chdir('../');

require('includes/application_top.php');
header('Content-Type: application/json; charset=utf-8');

// Get the search query from the 'q' parameter
$q = isset($_GET['q']) ? trim($_GET['q']) : '';

// Prepare SQL for tag search (case-insensitive, partial match)
if ($q !== '') {
    $tags_query = tep_db_query("SELECT DISTINCT tag FROM products_tags WHERE tag LIKE '%" . tep_db_input($q) . "%' ORDER BY tag LIMIT 10");
} else {
    $tags_query = tep_db_query("SELECT DISTINCT tag FROM products_tags ORDER BY tag LIMIT 10");
}

// Collect tags into an array
$tags = [];
while ($row = tep_db_fetch_array($tags_query)) {
    $tags[] = $row['tag'];
}

// Return tags as JSON
echo json_encode($tags);
exit;
