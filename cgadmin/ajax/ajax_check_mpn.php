<?php
// change the directory upone for application top includes
chdir('../');
require_once('includes/application_top.php');
header('Content-Type: application/json');

$mpn = isset($_POST['mpn']) ? trim($_POST['mpn']) : '';
$products_id = isset($_POST['products_id']) ? (int)$_POST['products_id'] : 0;

if ($mpn === '') {
    echo json_encode(['exists' => false]);
    exit;
}

$where = "products_mpn = '" . tep_db_input($mpn) . "'";
if ($products_id > 0) {
    $where .= " AND p.products_id != '" . (int)$products_id . "'";
}

$query = tep_db_query("SELECT p.products_id, p.products_model, pd.products_name FROM " . TABLE_PRODUCTS . " p LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd ON p.products_id = pd.products_id WHERE $where LIMIT 1");

if (tep_db_num_rows($query) > 0) {
    $row = tep_db_fetch_array($query);
    echo json_encode([
        'exists' => true,
        'name' => $row['products_name'],
        'model' => $row['products_model'],
        'id' => $row['products_id']
    ]);
} else {
    echo json_encode(['exists' => false]);
}
