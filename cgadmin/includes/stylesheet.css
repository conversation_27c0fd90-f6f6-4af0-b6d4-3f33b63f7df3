/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

/* links */
a { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #000000; font-weight: normal; text-decoration: none; }
a:hover { text-decoration: underline; }

a.headerLink { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #ffffff; font-weight: bold; text-decoration: none; }
a.headerLink:hover { text-decoration: underline; }

a.menuBoxHeadingLink { font-size: 12px; color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:hover { }

a.menuBoxContentLink { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:hover { text-decoration: underline; }

a.splitPageLink { font-family: <PERSON><PERSON>, Verdana, sans-serif; font-size: 12px; color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:hover { text-decoration: underline; background-color: #FFFF33; }

/* menu box */
.menuBoxHeading { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #616060; background-color: #ffffff; }
.menuBoxContent { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #616060; }

/* page */
body { background-color: #ffffff; color: #000000; margin: 0px; }
.headerBar { background-color: #006699; }
.headerBarContent { font-family: Arial, Verdana, sans-serif; font-size: 14px; color: #ffffff; font-weight: bold; padding: 5px; }
.headerBarContent a:hover { #CCC; }
.headerBarContentTitle { color:#F0F1F1; float: left; width: 220px; }
.columnLeft { background-color: #F0F1F1; border-color: #999999; border-width: 1px; border-style: solid; padding: 2px; }
.pageHeading { font-family: Arial, Verdana, sans-serif; font-size: 18px; color: #333; font-weight: bold; }

/* data table */
.dataTableHeadingRow { background-color: #90B4D4; }
.dataTableHeadingRowBlue { background-color: #0099CC; }
.dataTableHeadingContent { font-family:  Arial, sans-serif; font-size: 12px; color: #ffffff; font-weight: bold; }
.dataTableRow { background-color: #F0F1F1; }
.dataTableRow2 { background-color: #F9F9F9; }
.dataTableRowSelected { background-color: #DEE4E8; }
.dataTableRowOver { background-color: #FFFFFF; cursor: pointer; cursor: hand; }
.dataTableContent { font-family:  Arial, sans-serif; font-size: 12px; color: #000000; }
.dataTableContentRedAlert { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #FF0000; font-weight: bold; }

.regular-checkbox { height: 1.5em; width: 1.5em; }

.vert-cg { height: 1.5em; width: 1.5em; }
.vert-ho { height: 1.5em; width: 1.5em; }
.vert-hs { height: 1.5em; width: 1.5em; }
.vert-rg { height: 1.5em; width: 1.5em; }
.vert-gws { height: 1.5em; width: 1.5em; }
.vert-cu { height: 1.5em; width: 1.5em; }
.vert-af { height: 1.5em; width: 1.5em; }
.vert-tt { height: 1.5em; width: 1.5em; }

.selectall-cg { height: 1.5em; width: 1.5em; }
.selectall-ho { height: 1.5em; width: 1.5em; }
.selectall-hs { height: 1.5em; width: 1.5em; }
.selectall-rg { height: 1.5em; width: 1.5em; }
.selectall-gws { height: 1.5em; width: 1.5em; }
.selectall-cu { height: 1.5em; width: 1.5em; }
.selectall-af { height: 1.5em; width: 1.5em; }
.selectall-tt { height: 1.5em; width: 1.5em; }

/* info box */
.infoBoxHeading { font-family:  Arial, sans-serif; font-size: 12px; color: #ffffff; background-color: #88ACCC; }
.infoBoxContent { font-family:  Arial, sans-serif; font-size: 12px; color: #000000; background-color: #DEE4E8; }

/* message box */

.messageBox { font-family:  Arial, sans-serif; font-size: 12px; }
.messageStackError, .messageStackWarning {
	font-family:  Arial, sans-serif;
	background-color: #CC0000;
	color: #FFFFFF;
	padding: 5px;
}
.messageStackSuccess {
	font-family:  Arial, sans-serif;
	font-size: 12px;
	background-color: #00CC33;
	color: #FFFFFF;
}
.messageStackQuantity {
	font-family:  Arial, sans-serif;
	font-size: 18px;
	font-weight: 700;
	background-color: #327FFD;
	color: #FFFFFF;
	padding: 0 0 10px 0;
	line-height: 30px;
	text-align: center;
}
.messageStackQuantityIncrease {
	color: #666;
	background-color: #FFF;
	margin-bottom: 10px;
}
.messageStackQuantityIncrease strong {
	color: #009966;
}
.messageStackQuantityDecrease {
	color: #666;
	background-color: #FFF;
	margin-bottom: 10px;
}
.messageStackQuantityDecrease strong {
	color: #CC0000;
}
.messageStackQuantity span {
	color: #222;
}
/* forms */
CHECKBOX, INPUT, RADIO, SELECT, TEXTAREA, FILE { font-family: Arial, Verdana, sans-serif; font-size: 13px; }
FORM { display: inline; }

/* account */
.formArea { background-color: #f1f9fe; border-color: #7b9ebd; border-style: solid; border-width: 1px; }
.formAreaTitle { font-family: Tahoma, Verdana, Arial, sans-serif; font-size: 12px; font-weight: bold; }

/* attributes */
.attributes-odd { background-color: #f4f7fd; }
.attributes-even { background-color: #ffffff; }

/* miscellaneous */
.specialPrice { color: #ff0000; }
.oldPrice { text-decoration: line-through; }
.fieldRequired { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #ff0000; }
.smallText { font-family: Arial, Verdana, sans-serif; font-size: 12px; }
.main { font-family: Arial, Verdana, sans-serif; font-size: 12px; }
.errorText { font-family: Arial, Verdana, sans-serif; font-size: 12px; color: #ff0000; }

/* new messageStack styles */
.secInfo, .secSuccess, .secWarning, .secError {
  border: 1px solid;
  margin: 10px 0px;
  padding: 5px 10px 5px 50px;
  background-repeat: no-repeat;
  background-position: 10px center;
  border-radius: 10px;
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
}

.secInfo {
  border-color: #00529B;
  background-image: url('../images/ms_info.png');
  background: url('../images/ms_info.png') no-repeat 10px center, url('../images/ms_info_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #BDE5F8;
}

.secSuccess {
  border-color: #4F8A10;
  background-image: url('../images/ms_success.png');
  background: url('../images/ms_success.png') no-repeat 10px center, url('../images/ms_success_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #DFF2BF;
}

.secWarning {
  border-color: #9F6000;
  background-color: #FEEFB3;
  background-image: url('../images/ms_warning.png');
  background: url('../images/ms_warning.png') no-repeat 10px center, url('../images/ms_warning_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #FEEFB3;
}

.secError {
  border-color: #D8000C;
  background-image: url('../images/ms_error.png');
  background: url('../images/ms_error.png') no-repeat 10px center, url('../images/ms_error_bg.png') repeat-x; /* css3 multiple backgrounds */
  background-color: #FFBABA;
}

.secInfo p, .secSuccess p, .secWarning p, .secError p {
  padding: 2px;
}

#adminAppMenu {
  float: left;
  width: 210px;
  padding: 5px;
}

#adminAppMenu h3 {
}

#adminAppMenu ul {
  list-style: none;
  margin: -5px 0 -5px -10px;
  padding: 0;
}

#contentText {
  margin-left: 230px;
}

/* Custom jQuery UI */
.ui-widget { font-family: Lucida Grande, Lucida Sans, Arial, sans-serif; font-size: 13px; }

/* buttons */

.tdbLink a { }

.tdbLink button { }

/* links */
#dhtmltooltip{
position: absolute;
width: 150px;
border: 2px solid black;
padding: 2px;
background-color: white;
visibility: hidden;
z-index: 100;
}
.transfer-bar {
   position:fixed;
   left:0px;
   bottom:0px;
   height:36px;
   width:100%;
   text-align: right;
}
.transfer-bar li {
   float: left;
   list-style-type: none;
   display: inline-block;
   margin: 0px;
   padding: 0px;
}
.transfer-bar ul {
   float: right;
   margin: 0px;
   padding: 0px;
}
.extra-buttons {
    position: absolute;
	top: -50px;
	left: 0px;
}
/* IE 6 */
* html .transfer-bar {
   position:absolute;
   top:expression((0-(footer.offsetHeight)+(document.documentElement.clientHeight ? document.documentElement.clientHeight : document.body.clientHeight)+(ignoreMe = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop))+'px');
}
#formerror {
   color: #CC0000;
   font-size: 18px;
}
.box {
float: left;
margin-right: 10px;
width: 100%;
}
.box-container {
float: left;
width: 25%;
}
.locker_warning {
	color: #009966;
	background-color: #ECECEC;
	padding: 5px;
	margin-top: 10px;
	border: 1px solid #999999;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 14px;
}
.locker_warning a {
	font-size: 14px;
}

.linkageRowOdd {
background-color:#f2f1ee;
}
.linkageRowEven {
background-color:#FFFFFF;
}
#fg_form_div {
margin-left: 10px;
}
.fg_explain {
margin-left: 10px;
}
.inactive_warning {
color:#FF0000;
}

.courier{
background-color: #f3f3f3;
border: solid 1px #e5e5e5;
padding: 10px;
font-size: 11px;
margin: 10px 0 0 0;
width: auto;
font-family: Verdana, Arial, sans-serif;
}

.hiddenDiv {
display: none;
}

.pm-url  { float: left; background-color: #ECECEC; padding: 10px; margin-top: 10px; clear: both; min-width: 770px; border:1px solid #CCC; }
.pm-list { float: left; margin-top: 10px; clear: both; min-width: 770px; }
.pm-add { float: left; clear: both; }
.pm-add ul { float: left;  list-style-type: none; padding-left: 0; }
.pm-add ul li {  }

.pm-note { background-color: #FFFF99; padding: 15px; }

#saved-data table, #saved-data th, #saved-data td {
    border:1px solid #CCC;
}
#saved-data table {
    border-bottom:0;
    border-left:0;
	background-color: #EEE;
	min-width: 790px;
}
#saved-data td, #saved-data th {
    border-top:0;
    border-right:0;
}

.pure-button { border: none; padding: 8px; cursor: pointer; }

.ui-widget-content a strong { float: left; display: block; margin-top: 7px; text-decoration: none; width: 100%; cursor: default;  }
.additional-info-text-field {
  border: #AAAAAA 1px solid;
  border-radius: 4px;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  padding: 6px 8px;
  margin: 0;
  color: #333;
}

#despatch-data {
  float: left;
  margin: 15px 5px 0px 0px;
}

#despatch-data span {
  float: left;
  font-family:Arial, Helvetica, sans-serif;
  display: none;
  padding: 5px;
}

.despatch-data-saved {
  display: block;
  color: #FFF;
  background-color: #009966;
}

.despatch-data-not-saved {
  display: block;
  color: #FFF;
  background-color: #990000;
}

.despatch-data-changed {
  display: block;
  color: #333;
  background-color: #FFF;
}
.despatch-amount {
  text-align: center;
}
#despatch-update {
  float: left;
  margin: 15px 5px 0px 0px;
  padding: 5px;
}
.despatch-stock-quantity {
  background: none;
  color: #666;
  padding: 2px;
  text-align: center;
}
.stock-border {
  border: none;
  margin: 0 1px;
}
.stock-border-on {
  border: 1px solid #CCC;
  margin: 0;
}
.stock-total {
  background-color: #DCDDDD;
}
.stock-products-quantity {
  background: none;
  color: #666;
  padding: 2px;
  text-align: center;
}
.stock-total-heading {
  background-color: #7CA0C0;
}
.despatch-alt-column {
  background-color: #F7F8F8;
}
.despatch-heading-alt-column {
  background-color: #97BBDB;
}
.all_ship {
  margin: 6px 0 6px 12px;
}
.despatch-notice {
  font-family:Arial, Helvetica, sans-serif;
  font-size: 14px;
  float: right;
  padding: 15px;
}
.courier-details {
 float: right;
 padding: 5px 5px 5px 0;
}
#refund-message {
  display: none;
  color: #990000;
  font-weight: 700;
  font-family:Arial, Helvetica, sans-serif;
  font-size: 16px;
  padding: 15px;
  background-color: #EEE;
}
#refund-message a {
  color: #009933;
  font-weight: 700;
  font-size: 16px;
}

.navbar {
  overflow: hidden;
  background-color: #006699;
  font-family: Arial, Helvetica, sans-serif;
}

.navbar a, .navbar span {
  float: left;
  font-size: 16px;
  color: white;
  text-align: center;
  padding: 7px 12px;
  text-decoration: none;
}

.dropdown {
  float: left;
  overflow: hidden;
}

.dropdown .dropbtn {
  cursor: pointer;
  font-size: 16px;
  border: none;
  outline: none;
  color: white;
  padding: 7px 12px;
  background-color: inherit;
  font-family: inherit;
  margin: 0;
}

.navbar a:hover, .dropdown:hover .dropbtn, .dropbtn:focus {
  background-color: #88ACCC;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1;
  padding: 10px;
}

.dropdown-content a {
  float: none;
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  text-align: left;
}

.dropdown-content a:hover {
  background-color: #ddd;
}

.show {
  display: block;
}
.navright { float: right; }
.calc-grossMargin, .calc-profit { border: none; background: none; }
.calc-update { float: right; background-color: #EEE; padding: 5px; cursor: pointer; }

.products_image_holder { border: none; background: none; }
.up-btn { width: 30px; }
.down-btn { width: 30px; }
#admin_user_page {border: 5px solid #006699;border-top: 0;}
#admin_user_page ul {margin: 0; padding: 0; list-style: none; }


/* Pagination styles */
  .modern-pagination {
    margin: 24px 0;
    text-align: center;
    user-select: none;
  }
  .modern-pagination a,
  .modern-pagination span {
    display: inline-block;
    padding: 7px 14px;
    margin: 0 2px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background: #fff;
    text-decoration: none;
    font-weight: 500;
    color: #337ab7;
    transition: background 0.2s, color 0.2s;
  }
  .modern-pagination span.active {
    background: #337ab7;
    color: #fff;
    border-color: #337ab7;
    cursor: default;
  }
  .modern-pagination a:hover:not(.active) {
    background: #f5f5f5;
    color: #23527c;
  }
  .modern-pagination span.disabled {
    color: #aaa;
    background: #f9f9f9;
    border-color: #eee;
    cursor: default;
  }