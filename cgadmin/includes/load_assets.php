<?php
// Ensure $features is defined and is an array
$features = isset($features) && is_array($features) ? $features : [];

// JavaScript file URLs
$scripts = [
    'tailwind' => 'https://cdn.tailwindcss.com',
    'chart'    => 'https://cdn.jsdelivr.net/npm/chart.js',
    'tagify'   => 'https://cdn.jsdelivr.net/npm/@yaireo/tagify',
];

// CSS file URLs
$stylesheets = ['tagify' => 'https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css'];

// Loop through each feature and load associated assets
foreach ($features as $feature) {
    // Load CSS if defined
    if (isset($stylesheets[$feature])) {
        echo '<link rel="stylesheet" href="' . htmlspecialchars($stylesheets[$feature], ENT_QUOTES, 'UTF-8') . '">' . PHP_EOL;
    }

    // Load JavaScript if defined
    if (isset($scripts[$feature])) {
        echo '<script src="' . htmlspecialchars($scripts[$feature], ENT_QUOTES, 'UTF-8') . '"></script>' . PHP_EOL;
    }
}
