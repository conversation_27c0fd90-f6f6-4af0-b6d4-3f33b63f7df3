<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2008 osCommerce

  Released under the GNU General Public License
*/

// Start the clock for the page parse time log
define('PAGE_PARSE_START_TIME', microtime());

// Set the level of error reporting
error_reporting(E_ALL & ~E_NOTICE);

// check support for register_globals
if (function_exists('ini_get') && (ini_get('register_globals') == false) && (PHP_VERSION < 4.3)) {
  exit('Server Requirement Error: register_globals is disabled in your PHP configuration. This can be enabled in your php.ini configuration file or in the .htaccess file in your catalog directory. Please use PHP 4.3+ if register_globals cannot be enabled on the server.');
}

// load server configuration parameters
if (file_exists('includes/local/configure.php')) { // for developers
  include('includes/local/configure.php');
} else {
  include('includes/configure.php');
}

// Define the project version --- obsolete, now retrieved with tep_get_version()
define('PROJECT_VERSION', 'osCommerce Online Merchant v2.3');

// some code to solve compatibility issues
require(DIR_WS_FUNCTIONS . 'compatibility.php');

// set php_self in the local scope
$PHP_SELF = (((strlen(ini_get('cgi.fix_pathinfo')) > 0) && ((bool)ini_get('cgi.fix_pathinfo') == false)) || !isset($_SERVER['SCRIPT_NAME'])) ? basename($_SERVER['PHP_SELF']) : basename($_SERVER['SCRIPT_NAME']);

if (isset($login_request)){
  // Security Pro by FWR Media
//  include_once('../includes/modules/fwr_media_security_pro.php') ;
//  $security_pro = new Fwr_Media_Security_Pro;
//  $security_pro->cleanse( $PHP_SELF );
  // End - Security Pro by FWR Media
}

// Used in the "Backup Manager" to compress backups
define('LOCAL_EXE_GZIP', 'gzip');
define('LOCAL_EXE_GUNZIP', 'gunzip');
define('LOCAL_EXE_ZIP', 'zip');
define('LOCAL_EXE_UNZIP', 'unzip');

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// Define how do we update currency exchange rates
// Possible values are 'oanda' 'xe' or ''
define('CURRENCY_SERVER_PRIMARY', 'oanda');
define('CURRENCY_SERVER_BACKUP', 'xe');

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
  define($configuration['cfgKey'], $configuration['cfgValue']);
}

// define our general functions used application-wide
require(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_FUNCTIONS . 'html_output.php');

// initialize the logger class
require(DIR_WS_CLASSES . 'logger.php');

if (!strstr($_SERVER['PHP_SELF'], 'create_order_process.php')) { // this page uses the catlogue side api
  // graith mailchimp - start
  require('includes/runtime/applicationtop/mailchimp_applicationtop_bottom.php');
  // graith mailchimp - end
}

// include shopping cart class
require(DIR_WS_CLASSES . 'shopping_cart.php');

// define how the session functions will be used
require(DIR_WS_FUNCTIONS . 'sessions.php');

// set the session name and save path
tep_session_name('osCAdminID');
tep_session_save_path(SESSION_WRITE_DIRECTORY);

// set the session cookie parameters
if (function_exists('session_set_cookie_params')) {
  session_set_cookie_params(0, DIR_WS_ADMIN);
} elseif (function_exists('ini_set')) {
  ini_set('session.cookie_lifetime', '0');
  ini_set('session.cookie_path', DIR_WS_ADMIN);
}

@ini_set('session.use_only_cookies', (SESSION_FORCE_COOKIE_USE == 'True') ? 1 : 0);

// lets start our session
tep_session_start();

if ((PHP_VERSION >= 4.3) && function_exists('ini_get') && (ini_get('register_globals') == false)) {
  extract($_SESSION, EXTR_OVERWRITE + EXTR_REFS);
}

// set the language
if (!tep_session_is_registered('language') || isset($_GET['language'])) {
  if (!tep_session_is_registered('language')) {
    tep_session_register('language');
    tep_session_register('languages_id');
  }

  include(DIR_WS_CLASSES . 'language.php');
  $lng = new language();

  if (isset($_GET['language']) && tep_not_null($_GET['language'])) {
    $lng->set_language($_GET['language']);
  } else {
    $lng->get_browser_language();
  }

  $language = $lng->language['directory'];
  $languages_id = $lng->language['id'];
}

// redirect to login page if administrator is not yet logged in
if (!tep_session_is_registered('admin')) {
  $redirect = false;

  $current_page = basename($PHP_SELF);

  // if the first page request is to the login page, set the current page to the index page
  // so the redirection on a successful login is not made to the login page again
  if (($current_page == FILENAME_LOGIN) && !tep_session_is_registered('redirect_origin')) {
    $current_page = FILENAME_DEFAULT;
    $_GET = array();
  }

  // if somebody has posted info...
  if (!empty($_POST) && $current_page != 'login.php') {
    $current_page = FILENAME_DEFAULT;
    $_GET = array();
  }

  if ($current_page != FILENAME_LOGIN) {
    if (!tep_session_is_registered('redirect_origin')) {
      tep_session_register('redirect_origin');

      $redirect_origin = array(
        'page' => $current_page,
        'get' => $_GET
      );
    }

    // try to automatically login with the HTTP Authentication values if it exists
    if (!tep_session_is_registered('auth_ignore')) {
      if (isset($_SERVER['PHP_AUTH_USER']) && !empty($_SERVER['PHP_AUTH_USER']) && isset($_SERVER['PHP_AUTH_PW']) && !empty($_SERVER['PHP_AUTH_PW'])) {
        $redirect_origin['auth_user'] = $_SERVER['PHP_AUTH_USER'];
        $redirect_origin['auth_pw'] = $_SERVER['PHP_AUTH_PW'];
      }
    }

    $redirect = true;
  }

  if (!isset($login_request) || isset($_GET['login_request']) || isset($_POST['login_request']) || isset($_COOKIE['login_request']) || isset($_SESSION['login_request']) || isset($_FILES['login_request']) || isset($_SERVER['login_request'])) {
    $redirect = true;
  }

  if ($redirect == true) {
    tep_redirect(tep_href_link(FILENAME_LOGIN, (isset($redirect_origin['auth_user']) ? 'action=process' : '')));
  }

  unset($redirect);
}

// include the language translations
$_system_locale_numeric = setlocale(LC_NUMERIC, 0);
require(DIR_WS_LANGUAGES . $language . '.php');
setlocale(LC_NUMERIC, $_system_locale_numeric); // Prevent LC_ALL from setting LC_NUMERIC to a locale with 1,0 float/decimal values instead of 1.0 (see bug #634)

$current_page = basename($PHP_SELF);
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . $current_page)) {
  include(DIR_WS_LANGUAGES . $language . '/' . $current_page);
}

// define our localization functions
require(DIR_WS_FUNCTIONS . 'localization.php');

// Include validation functions (right now only email address)
require(DIR_WS_FUNCTIONS . 'validations.php');

// setup our boxes
require(DIR_WS_CLASSES . 'table_block.php');
require(DIR_WS_CLASSES . 'box.php');

// initialize the message stack for output messages
require(DIR_WS_CLASSES . 'message_stack.php');
$messageStack = new messageStack;

// split-page-results
require(DIR_WS_CLASSES . 'split_page_results.php');

// entry/item info classes
require(DIR_WS_CLASSES . 'object_info.php');

// email classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

// file uploading class
require(DIR_WS_CLASSES . 'upload.php');

// action recorder
require(DIR_WS_CLASSES . 'action_recorder.php');

require(DIR_FS_CATALOG . 'includes/functions/email_templates.php'); // CGars Plus

require(DIR_FS_CATALOG . 'includes/functions/cgars_plus_redemptions.php'); // CGars Plus
require(DIR_FS_CATALOG . 'includes/functions/redemptions.php'); // Points/Rewards

// calculate category path
if (isset($_GET['cPath'])) {
  $cPath = $_GET['cPath'];
} else {
  $cPath = '';
}

if (tep_not_null($cPath)) {
  $cPath_array = tep_parse_category_path($cPath);
  $cPath = implode('_', $cPath_array);
  $current_category_id = $cPath_array[(sizeof($cPath_array) - 1)];
} else {
  $current_category_id = 0;
}

// initialize configuration modules
require(DIR_WS_CLASSES . 'cfg_modules.php');
$cfgModules = new cfg_modules();

$cache_blocks = array(array('title' => 'Main Drop Down Categories Menu', 'code' => 'mega_menu', 'file' => 'mega_menu-language.cache', 'multiple' => true), array('title' => 'Main Drop Down Categories Menu Mobile', 'code' => 'mega_menu_mobile', 'file' => 'mega_menu_mobile-language.cache', 'multiple' => true), array('title' => 'Main Drop Down Categories Menu Davidoff', 'code' => 'mega_menu_davidoff', 'file' => 'mega_menu_davidoff-language.cache', 'multiple' => true), array('title' => 'Search Filter', 'code' => 'filter_menu', 'file' => 'filter_menu-language.cache', 'multiple' => true));

// $use_old_admin_system = true;

if ($use_old_admin_system) {

  // START OLD ADMIN SYSTEM

  if (is_numeric($admin['id'])) { // if logged in

    $page_name = str_replace(DIR_WS_ADMIN, "", strtok($_SERVER["REQUEST_URI"], '?'));

    $catalogue_section_array = array('categories_master.php', 'products_attributes.php', 'products_attributes_copier.php', 'quick_updates.php', 'quick_price_updates.php', 'quick_meta_updates.php', 'quick_all_attribute_updates.php', 'quick_stock_location_updates.php', 'quick_updates_model.php', 'quick_updates_reverse_auctions.php', 'locker_items.php', 'manufacturers.php', 'suppliers.php', 'featured.php', 'featured2.php', 'featured3.php', 'featured_cubans.php', 'featured_non_cubans.php', 'featured_cigars.php', 'featured_samplers.php', 'featured_whiskies.php', 'featured_budget_cigars.php', 'featured_cigar_week.php', 'optional_related_products.php', 'optional_mitchell_products.php', 'optional_cross_sell_products.php', 'related_reviews.php', 'update_cross_sell.php', 'reviews.php', 'specials.php', 'products_expected.php', 'attributeManager/attributeManager.php', 'attributeManager/ajax/set_product_template.php', 'attributeManager/ajax/update_template_products.php', 'maintenance_products_and_attributes.php', 'quick_tobacco_price_updates.php', 'tobacco_markup.php', 'create_site_tables.php', 'price_match_call.php', 'price_match_update.php', 'notify_customers.php', 'additional_info_options.php', 'additional_info_values.php', 'quick_whisky_updates.php', 'sampler_update.php', 'ajax_image_update.php', 'ajax/ajax_product_search.php');

    $order_section_array = array(
      'orders.php', 'invoice.php', 'packingslip.php',
      'edit_orders.php', 'edit_orders_add_product.php', 'edit_orders_ajax.php', 'id_upload.php',
      'print_orders.php', 'print_batch_process.php', 'print_batch_invoice.php',
      'customers.php', 'customers_points.php', 'customers_points_credit.php', 'customers_points_expire.php', 'customers_points_credit.php',
      'customers_points_pending.php', 'customers_points_pending_batch.php', 'customers_points_referral.php',
      'orders_status.php', 'create_order.php', 'create_order_process.php', 'ajax_free_gift_check.php', 'subscriptions.php', 'subscription_help.php', 'despatch_update.php'
    );

    $reports_section_array = array('stats_products_viewed.php', 'stats_products_purchased.php', 'stats_customers.php', 'stats_products_stock_report.php', 'stats_products_stock_report_email.php', 'stats_products_enabled_zero_stock_report.php', 'stats_advanced_shop_stock_report.php', 'stats_shop_stock_report.php', 'stats_products_disabled_report.php', 'shipping_method_report.php', 'stats_discount_coupons.php', 'stats_wishlists.php', 'stats_gift_voucher_history.php', 'empty_attribute_finder.php', 'stats_shop_customers.php', 'stats_daily_category_sales_report.php', 'stats_daily_category_sales_report2.php', 'stats_daily_products_sales_report.php', 'stats_customers_free_gift.php', 'stats_previous_week_sales_report.php', 'stats_daily_products_sales_report_no_attributes.php', 'stats_norfolk_membership.php');

    $tools_section_array = array('action_recorder.php', 'news_manager.php', 'testimonials_manager.php', 'banner_manager.php', 'banners_products.php', 'cache.php', 'whos_online.php', 'articles.php', 'schedule_manager.php', 'ajax/ajax_product_search.php');

    $vouchers_section_array = array('coupons.php', 'coupons_exclusions.php', 'gv_mail.php', 'gv_sent.php', 'ajax_customer_search.php');

    $free_gifts_section_array = array('free_gifts.php', 'free_gift_chooser_backend.php');

    if ($admin['id'] != 2 && $admin['id'] != 8 && $admin['id'] != 81 && $admin['id'] != 91) { // if not Laura or Richard or Courtney or Agit
      if ($page_name == 'email_server_details.php' || $page_name == 'administrators.php') { // ! IF IT ISSSS THESE PAGES !
        tep_redirect(tep_href_link(FILENAME_ACCESS_DENIED));
        $redirect = 2;
      }
    }

    if ($admin['id'] != 1 && $admin['id'] != 2 && $admin['id'] != 8 && $admin['id'] != 15 && $admin['id'] != 81 && $admin['id'] != 91) { // then users with restrictions

      if ($admin['id'] == 47) { // Sara
        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'quick_stock_location_updates.php');
      }

      if ($admin['id'] == 20) { // Liam

        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'mail.php', 'admin_login_check.php', 'product_export.php', 'raffle.php', 'raffle_tickets.php');
        $allowed_page_array = array_merge($allowed_page_array, $catalogue_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $order_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $reports_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $tools_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $free_gifts_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $vouchers_section_array);
      }

      if ($admin['id'] == 22) { // Anthony

        $allowed_page_array = array(
          'access_denied.php', 'login.php', 'index.php', 'admin_login_check.php', 'orders.php', 'invoice.php', 'packingslip.php',
          'edit_orders.php', 'edit_orders_add_product.php', 'edit_orders_ajax.php', 'id_upload.php',
          'print_orders.php', 'print_batch_process.php', 'print_batch_invoice.php',
          'create_order.php', 'create_order_process.php', 'ajax_free_gift_check.php', 'subscriptions.php', 'subscription_help.php', 'despatch_update.php'
        );
        $allowed_page_array = array_merge($allowed_page_array, $catalogue_section_array);
      }

      if ($admin['id'] == 49) { // Jacob

        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'mail.php', 'admin_login_check.php', 'news_manager.php', 'banner_manager.php', 'articles.php');
        $allowed_page_array = array_merge($allowed_page_array, $catalogue_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $order_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $reports_section_array);
      }

      if ($admin['id'] == 19 || $admin['id'] == 11 || $admin['id'] == 13 || $admin['id'] == 23 || $admin['id'] == 24 || $admin['id'] == 33 || $admin['id'] == 44) {

        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'mail.php', 'admin_login_check.php', 'product_export.php');
        $allowed_page_array = array_merge($allowed_page_array, $catalogue_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $order_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $reports_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $tools_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $free_gifts_section_array);

        if ($admin['id'] == 13) {
          $allowed_page_array = array_merge($allowed_page_array, array('express_to_account.php'));
        }
        if ($admin['id'] == 13 || $admin['id'] == 19 || $admin['id'] == 23) {
          $allowed_page_array = array_merge($allowed_page_array, array('stats_daily_category_sales_and_stock_report.php'));
        }
        if ($admin['id'] == 19 || $admin['id'] == 24) {
          $allowed_page_array = array_merge($allowed_page_array, array('maintenance_cats_find_pipe_tobacco_without_slider.php'));
        }
        if ($admin['id'] == 13 || $admin['id'] == 19) {
          $allowed_page_array = array_merge($allowed_page_array, $vouchers_section_array);
        }
        if ($admin['id'] == 11 || $admin['id'] == 13 || $admin['id'] == 19) { // Georgina 13 - Imogen 11 - Amy 19
          $allowed_page_array = array_merge($allowed_page_array, array('stats_active_oos_samplers.php', 'stats_oos_samplers.php', 'stats_products_stock_report_email_categories.php'));
        }
        if ($admin['id'] == 19) {
          $allowed_page_array = array_merge($allowed_page_array, array('import_products.php', 'raffle.php', 'raffle_tickets.php', 'quick_updates_orders.php', 'stats_monthly_sales_quantity_report.php', 'stats_weekly_sales_quantity_report.php', 'log_viewer.php', 'stats_monthly_products_sales_report.php'));
        }
        if ($admin['id'] == 11) {
          $allowed_page_array = array_merge($allowed_page_array, array('quick_updates_orders.php', 'subscriptions_add.php', 'ajax/ajax_customer_addresses.php', 'ajax/ajax_customer_subscription_search.php'));
        }
      }

      // orders and invoices only
      if ($admin['id'] == 48) {
        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'orders.php', 'invoice.php', 'packingslip.php', 'orders_status.php', 'export_orders.php', 'export_alcohol_orders.php', 'customer_export_prod.php');
      }

      // catalogue only
      if ($admin['id'] == 40 || $admin['id'] == 41) {
        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php');
        $allowed_page_array = array_merge($allowed_page_array, $catalogue_section_array);
        if ($admin['id'] == 40) {
          $allowed_page_array = array_merge($allowed_page_array, $order_section_array);
        }
        if ($admin['id'] == 41) {
          $allowed_page_array = array_merge($allowed_page_array, $order_section_array);
          $allowed_page_array = array_merge($allowed_page_array, array('articles.php'));
        }
      }

      if ($admin['id'] == 6 || $admin['id'] == 10 || $admin['id'] == 16 || $admin['id'] == 17 || $admin['id'] == 18 || $admin['id'] == 28 || $admin['id'] == 29 || $admin['id'] == 31 || $admin['id'] == 34 || $admin['id'] == 38 || $admin['id'] == 39 || $admin['id'] == 43 || $admin['id'] == 46 || $admin['id'] == 52 || $admin['id'] == 54) {

        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'mail.php');
        $allowed_page_array = array_merge($allowed_page_array, $catalogue_section_array);
        $allowed_page_array = array_merge($allowed_page_array, $order_section_array);

        if ($admin['id'] == 28 || $admin['id'] == 34) {
          $allowed_page_array = array_merge($allowed_page_array, array('articles.php'));
          $allowed_page_array = array_merge($allowed_page_array, array('testimonials_manager.php'));
          $allowed_page_array = array_merge($allowed_page_array, array('news_manager.php'));
        }

        if ($admin['id'] == 10 || $admin['id'] == 28) {
          $allowed_page_array = array_merge($allowed_page_array, $vouchers_section_array);
          $allowed_page_array = array_merge($allowed_page_array, array('subscriptions_add.php', 'ajax/ajax_customer_addresses.php', 'ajax/ajax_customer_subscription_search.php'));
        }

        if ($admin['id'] == 18 || $admin['id'] == 38) {
          $allowed_page_array = array_merge($allowed_page_array, array('quick_updates_orders.php'));
        }

        if ($admin['id'] == 46) {
          $allowed_page_array = array_merge($allowed_page_array, $reports_section_array);
        }
      }

      if ($admin['id'] == 4 || $admin['id'] == 27 || $admin['id'] == 35 || $admin['id'] == 36 || $admin['id'] == 37 || $admin['id'] == 50 || $admin['id'] == 51 || $admin['id'] == 53) {
        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'mail.php');
        $allowed_page_array = array_merge($allowed_page_array, $order_section_array);
      }

      if ($admin['id'] == 45) { // packing team
        $packing_team_section_array = array(
          'orders.php', 'invoice.php', 'packingslip.php',
          'print_orders.php', 'print_batch_process.php', 'print_batch_invoice.php',
          'ajax_free_gift_check.php', 'despatch_update.php'
        );
        $allowed_page_array = array('access_denied.php', 'login.php', 'index.php', 'mail.php');
        $allowed_page_array = array_merge($allowed_page_array, $packing_team_section_array);
      }

      if ($admin['id'] == 11 || $admin['id'] == 20 || $admin['id'] == 13 || $admin['id'] == 19) {
        $allowed_page_array = array_merge($allowed_page_array, array('subscriptions_email.php', 'subscriptions_email_preview.php'));
      }

      if ((is_array($allowed_page_array) && in_array($page_name, $allowed_page_array)) || (!$page_name)) {
        // allow access
        $redirect = 1;
      } else { // ! IF IT IS NOTTTT THESE PAGES !
        tep_redirect(tep_href_link(FILENAME_ACCESS_DENIED));
        $redirect = 2;
      }
    }
  }

  // END OLD ADMIN SYSTEM

} else { // use new admin system

  // START NEW ADMIN SYSTEM

  $super_admin_array = array('2', '8', '19', '72', '81', '91', '115', '116'); // Richard + Laura + Leead + Courtney + Agit + Andy + Sheridan

  if (is_numeric($admin['id']) && !in_array($admin['id'], $super_admin_array)) {
   
    $admin_pages_array = array('login.php', 'access_denied.php', 'index.php', 'ajax_product_search.php', 'ajax_customer_addresses.php', 'ajax_customer_subscription_search.php', 'ajax_customer_search.php', 'ajax_free_gift_check.php', 'ajax_image_update.php', 'attributeManager.php', 'set_product_template.php', 'update_template_products.php', 'admin_login_check.php', 'sagepay_reporting.php', 'report_loyalty_points_expire_info.php', 'free_gift_chooser_backend.php', 'coupons_exclusions.php','ajax_dashobard_data.php');
    
    $admin_page_qry = tep_db_query("SELECT apu.admin_pages_id, ap.admin_pages_name FROM `admin_pages_to_user` apu left join `admin_pages` ap on ap.admin_pages_id = apu.admin_pages_id where apu.admin_user_id = " . $admin['id']);
    while ($admin_pages = tep_db_fetch_array($admin_page_qry)) {
      $admin_pages_array[] = $admin_pages['admin_pages_name'];
    }

    $cuur_file_name = basename($_SERVER['SCRIPT_NAME']);
    if ((is_array($admin_pages_array) && in_array($cuur_file_name, $admin_pages_array)) || (!$cuur_file_name)) {
      // allow access
      $redirect = 1;
    } else { // ! IF IT IS NOTTTT THESE PAGES !
      tep_redirect(tep_href_link(FILENAME_ACCESS_DENIED));
      $redirect = 2;
    }
  }

  // END NEW ADMIN SYSTEM

}

if (is_numeric($admin['id'])) {
  
  // Restrict
  if ($admin['id'] == '39' || $admin['id'] == '23' || $admin['id'] == '47' || $admin['id'] == '51' || $admin['id'] == '52' || $admin['id'] == '56' || $admin['id'] == '68' || $admin['id'] == '79' || $admin['id'] == '110') {
    $restrict_user_updates = true;
  }
  if ($admin['id'] == '48' || $admin['id'] == '51' || $admin['id'] == '106') {
    $restrict_user_order_updates = true;
  }
  if ($admin['id'] == '20' || $admin['id'] == '22' || $admin['id'] == '51' || $admin['id'] == '52' || $admin['id'] == '57') {
    $restrict_user_exports = true;
  }
  if ($admin['id'] == '57') {
    $restrict_report_sale_prices = true;
  }

  // Allow
  if ($admin['id'] == '1' || $admin['id'] == '2' || $admin['id'] == '4' || $admin['id'] == '5' || $admin['id'] == '6' || $admin['id'] == '8' || $admin['id'] == '11' || $admin['id'] == '13' || $admin['id'] == '15' || $admin['id'] == '17' || $admin['id'] == '18' || $admin['id'] == '19' || $admin['id'] == '20' || $admin['id'] == '23' || $admin['id'] == '24' || $admin['id'] == '22' || $admin['id'] == '12' || $admin['id'] == '25' || $admin['id'] == '31'  || $admin['id'] == '34' || $admin['id'] == '40' || $admin['id'] == '41' || $admin['id'] == '43' || $admin['id'] == '46' || $admin['id'] == '54' || $admin['id'] == '70' || $admin['id'] == '73' || $admin['id'] == '75' || $admin['id'] == '76' || $admin['id'] == '77' || $admin['id'] == '78' || $admin['id'] == '62' || $admin['id'] == '81' || $admin['id'] == '91' || $admin['id'] == '82' || $admin['id'] == '86' || $admin['id'] == '107' || $admin['id'] == '114') {
    $allow_product_and_category_status_updates = true;
  }
  if ($admin['id'] == '2' || $admin['id'] == '8' || $admin['id'] == '19' || $admin['id'] == '24' || $admin['id'] == '40' || $admin['id'] == '54' || $admin['id'] == '81' || $admin['id'] == '91') { // on quick stock location updates
    $allow_order_location_filter = true;
  }
  
}
// - Load custom hooks - Agit
define('IS_ADMIN', true);
require_once(DIR_FS_CATALOG . 'includes/hooks/hook_manager.php'); 
require_once(DIR_FS_CATALOG . 'includes/hooks/load_custom_hooks.php');

