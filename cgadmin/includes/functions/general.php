<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2012 osCommerce

  Released under the GNU General Public License
*/

////
// Get the installed version number
  function tep_get_version() {
    static $v;

    if (!isset($v)) {
      $v = trim(implode('', file(DIR_FS_CATALOG . 'includes/version.php')));
    }

    return $v;
  }

// #################### Added Enable / Disable Categories ################
// Sets the status of a category and all nested categories and products whithin.
  function tep_set_categories_status($category_id, $status) {
    if ($status == '1') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '1', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '1', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    } elseif ($status == '0') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '0', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '0', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    } elseif ($status == '2') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '2', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '2', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    } elseif ($status == '3') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '3', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '3', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    } elseif ($status == '4') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '4', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '4', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    } elseif ($status == '5') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '5', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '5', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    } elseif ($status == '6') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '6', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '6', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    } elseif ($status == '7') {
      tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '7', last_modified = now() where categories_id = '" . $category_id . "'");
      $tree = tep_get_category_tree($category_id);
      for ($i=1; $i<sizeof($tree); $i++) {
        tep_db_query("update " . TABLE_CATEGORIES . " set categories_status = '7', last_modified = now() where categories_id = '" . $tree[$i]['id'] . "'");
      }
    }


  }
// #################### End Added Enable / Disable Categories ################

////
// Redirect to another page or site
  function tep_redirect($url) {
    global $logger;

    if ( (strstr($url, "\n") != false) || (strstr($url, "\r") != false) ) {
      tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'NONSSL', false));
    }

    if ( strpos($url, '&amp;') !== false ) {
      $url = str_replace('&amp;', '&', $url);
    }

    header('Location: ' . $url);

    if (STORE_PAGE_PARSE_TIME == 'true') {
      if (!is_object($logger)) $logger = new logger;
      $logger->timer_stop();
    }

    exit;
  }

////
// Parse the data used in the html tags to ensure the tags will not break
  function tep_parse_input_field_data($data, $parse) {
    return strtr(trim($data), $parse);
  }

  function tep_output_string($string, $translate = false, $protected = false) {
    if ($protected == true) {
      // return htmlspecialchars($string);
	  return $string;
    } else {
      if ($translate == false) {
        return tep_parse_input_field_data($string, array('"' => '&quot;'));
      } else {
        return tep_parse_input_field_data($string, $translate);
      }
    }
  }

  function tep_output_string_protected($string) {
    return tep_output_string($string, false, true);
  }

  function tep_sanitize_string($string) {
    $patterns = array ('/ +/','/[<>]/');
    $replace = array (' ', '_');
    return preg_replace($patterns, $replace, trim($string));
  }

  function tep_customers_name($customers_id) {
    $customers = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customers_id . "'");
    $customers_values = tep_db_fetch_array($customers);

    return $customers_values['customers_firstname'] . ' ' . $customers_values['customers_lastname'];
  }

  function tep_get_path($current_category_id = '') {
    global $cPath_array;

    if ($current_category_id == '') {
      $cPath_new = implode('_', $cPath_array);
    } else {
      if (sizeof($cPath_array) == 0) {
        $cPath_new = $current_category_id;
      } else {
        $cPath_new = '';
        $last_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int)$cPath_array[(sizeof($cPath_array)-1)] . "'");
        $last_category = tep_db_fetch_array($last_category_query);

        $current_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int)$current_category_id . "'");
        $current_category = tep_db_fetch_array($current_category_query);

        if ($last_category['parent_id'] == $current_category['parent_id']) {
          for ($i = 0, $n = sizeof($cPath_array) - 1; $i < $n; $i++) {
            $cPath_new .= '_' . $cPath_array[$i];
          }
        } else {
          for ($i = 0, $n = sizeof($cPath_array); $i < $n; $i++) {
            $cPath_new .= '_' . $cPath_array[$i];
          }
        }

        $cPath_new .= '_' . $current_category_id;

        if (substr($cPath_new, 0, 1) == '_') {
          $cPath_new = substr($cPath_new, 1);
        }
      }
    }

    return 'cPath=' . $cPath_new;
  }

  function tep_get_all_get_params($exclude_array = '') {
    global $_GET;

    if ($exclude_array == '') $exclude_array = array();

    $get_url = '';

    reset($_GET);
    while (list($key, $value) = each($_GET)) {
      if (($key != tep_session_name()) && ($key != 'error') && (!in_array($key, $exclude_array))) $get_url .= $key . '=' . $value . '&';
    }

    return $get_url;
  }

  function tep_date_long($raw_date) {
    if ( ($raw_date == '0000-00-00 00:00:00') || ($raw_date == '') ) return false;

    $year = (int)substr($raw_date, 0, 4);
    $month = (int)substr($raw_date, 5, 2);
    $day = (int)substr($raw_date, 8, 2);
    $hour = (int)substr($raw_date, 11, 2);
    $minute = (int)substr($raw_date, 14, 2);
    $second = (int)substr($raw_date, 17, 2);

    return strftime(DATE_FORMAT_LONG, mktime($hour, $minute, $second, $month, $day, $year));
  }

////
// Output a raw date string in the selected locale date format
// $raw_date needs to be in this format: YYYY-MM-DD HH:MM:SS
// NOTE: Includes a workaround for dates before 01/01/1970 that fail on windows servers
  function tep_date_short($raw_date) {
    if ( ($raw_date == '0000-00-00 00:00:00') || ($raw_date == '') ) return false;

    $year = substr($raw_date, 0, 4);
    $month = (int)substr($raw_date, 5, 2);
    $day = (int)substr($raw_date, 8, 2);
    $hour = (int)substr($raw_date, 11, 2);
    $minute = (int)substr($raw_date, 14, 2);
    $second = (int)substr($raw_date, 17, 2);

    if (@date('Y', mktime($hour, $minute, $second, $month, $day, $year)) == $year) {
      return date(DATE_FORMAT, mktime($hour, $minute, $second, $month, $day, $year));
    } else {
      return preg_replace('/2037$/', $year, date(DATE_FORMAT, mktime($hour, $minute, $second, $month, $day, 2037)));
    }

  }

  function tep_datetime_short($raw_datetime) {
    if ( ($raw_datetime == '0000-00-00 00:00:00') || ($raw_datetime == '') ) return false;

    $year = (int)substr($raw_datetime, 0, 4);
    $month = (int)substr($raw_datetime, 5, 2);
    $day = (int)substr($raw_datetime, 8, 2);
    $hour = (int)substr($raw_datetime, 11, 2);
    $minute = (int)substr($raw_datetime, 14, 2);
    $second = (int)substr($raw_datetime, 17, 2);

    return strftime(DATE_TIME_FORMAT, mktime($hour, $minute, $second, $month, $day, $year));
  }

  function tep_datetime_long($datetime){
   return date('l jS \of F Y h:i:s A', strtotime($datetime));
  }

  function tep_get_category_tree($parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false) {
    global $languages_id;

    if (!is_array($category_tree_array)) $category_tree_array = array();
    if ( (sizeof($category_tree_array) < 1) && ($exclude != '0') ) $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP);

    if ($include_itself) {
      $category_query = tep_db_query("select cd.categories_name from " . TABLE_CATEGORIES_DESCRIPTION . " cd where cd.language_id = '" . (int)$languages_id . "' and cd.categories_id = '" . (int)$parent_id . "'");
      $category = tep_db_fetch_array($category_query);
      $category_tree_array[] = array('id' => $parent_id, 'text' => $category['categories_name']);
    }

    $categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "' and c.parent_id = '" . (int)$parent_id . "' and ((c.categories_status in (0,1,2,3,4)) or (c.categories_id = '327') or (c.categories_id = '270')) order by c.sort_order, cd.categories_name");
    while ($categories = tep_db_fetch_array($categories_query)) {
      if ($exclude != $categories['categories_id']) $category_tree_array[] = array('id' => $categories['categories_id'], 'text' => $spacing . $categories['categories_name']);
      $category_tree_array = tep_get_category_tree($categories['categories_id'], $spacing . '&nbsp;&nbsp;&nbsp;', $exclude, $category_tree_array);
    }

    return $category_tree_array;
  }

  function tep_draw_products_pull_down($name, $parameters = '', $exclude = '', $selected = '') {
    global $currencies, $languages_id;

    if ($exclude == '') {
      $exclude = array();
    }

    $select_string = '<select name="' . $name . '"';

    if ($parameters) {
      $select_string .= ' ' . $parameters;
    }

    $select_string .= '>';

    $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_price from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_visible = '1' order by products_name");
    while ($products = tep_db_fetch_array($products_query)) {
      if (!in_array($products['products_id'], $exclude)) {

		if($selected == $products['products_id']){
		$select_string .= '<option value="' . $products['products_id'] . '" selected>' . $products['products_name'] . ' (' . $currencies->format($products['products_price']) . ')</option>';
		} else {
		$select_string .= '<option value="' . $products['products_id'] . '">' . $products['products_name'] . ' (' . $currencies->format($products['products_price']) . ')</option>';
		}

      }
    }

    $select_string .= '</select>';

    return $select_string;
  }

  function tep_format_system_info_array($array) {

    $output = '';
    foreach ($array as $section => $child) {
      $output .= '[' . $section . ']' . "\n";
      foreach ($child as $variable => $value) {
        if (is_array($value)) {
          $output .= $variable . ' = ' . implode(',', $value) ."\n";
        } else {
          $output .= $variable . ' = ' . $value . "\n";
        }
      }

    $output .= "\n";
    }
    return $output;

  }



  function tep_options_name($options_id) {
    global $languages_id;

    $options = tep_db_query("select products_options_name from " . TABLE_PRODUCTS_OPTIONS . " where products_options_id = '" . (int)$options_id . "' and language_id = '" . (int)$languages_id . "'");
    $options_values = tep_db_fetch_array($options);

    return $options_values['products_options_name'];
  }

  function tep_values_name($values_id) {
    global $languages_id;

    $values = tep_db_query("select products_options_values_name from " . TABLE_PRODUCTS_OPTIONS_VALUES . " where products_options_values_id = '" . (int)$values_id . "' and language_id = '" . (int)$languages_id . "'");
    $values_values = tep_db_fetch_array($values);

    return $values_values['products_options_values_name'];
  }

  function tep_info_image($image, $alt, $width = '', $height = '') {
    if (tep_not_null($image) && (file_exists(DIR_FS_CATALOG_IMAGES . $image)) ) {
      $image = tep_image(DIR_WS_CATALOG_IMAGES . $image, $alt, $width, $height);
    } else {
      $image = TEXT_IMAGE_NONEXISTENT;
    }

    return $image;
  }

  function tep_break_string($string, $len, $break_char = '-') {
    $l = 0;
    $output = '';
    for ($i=0, $n=strlen($string); $i<$n; $i++) {
      $char = substr($string, $i, 1);
      if ($char != ' ') {
        $l++;
      } else {
        $l = 0;
      }
      if ($l > $len) {
        $l = 1;
        $output .= $break_char;
      }
      $output .= $char;
    }

    return $output;
  }

  function tep_get_country_name($country_id) {
    $country_query = tep_db_query("select countries_name from " . TABLE_COUNTRIES . " where countries_id = '" . (int)$country_id . "'");

    if (!tep_db_num_rows($country_query)) {
      return $country_id;
    } else {
      $country = tep_db_fetch_array($country_query);
      return $country['countries_name'];
    }
  }

  function tep_get_zone_name($country_id, $zone_id, $default_zone) {
    $zone_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int)$country_id . "' and zone_id = '" . (int)$zone_id . "'");
    if (tep_db_num_rows($zone_query)) {
      $zone = tep_db_fetch_array($zone_query);
      return $zone['zone_name'];
    } else {
      return $default_zone;
    }
  }

  function tep_not_null($value) {
    if (is_array($value)) {
      if (sizeof($value) > 0) {
        return true;
      } else {
        return false;
      }
    } else {
      if ( (is_string($value) || is_int($value)) && ($value != '') && ($value != 'NULL') && (strlen(trim($value)) > 0)) {
        return true;
      } else {
        return false;
      }
    }
  }

  function tep_browser_detect($component) {
    global $HTTP_USER_AGENT;

    return stristr($HTTP_USER_AGENT, $component);
  }

  function tep_tax_classes_pull_down($parameters, $selected = '') {
    $select_string = '<select ' . $parameters . '>';
    $classes_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
    while ($classes = tep_db_fetch_array($classes_query)) {
      $select_string .= '<option value="' . $classes['tax_class_id'] . '"';
      if ($selected == $classes['tax_class_id']) $select_string .= ' SELECTED';
      $select_string .= '>' . $classes['tax_class_title'] . '</option>';
    }
    $select_string .= '</select>';

    return $select_string;
  }

  function tep_geo_zones_pull_down($parameters, $selected = '') {
    $select_string = '<select ' . $parameters . '>';
    $zones_query = tep_db_query("select geo_zone_id, geo_zone_name from " . TABLE_GEO_ZONES . " order by geo_zone_name");
    while ($zones = tep_db_fetch_array($zones_query)) {
      $select_string .= '<option value="' . $zones['geo_zone_id'] . '"';
      if ($selected == $zones['geo_zone_id']) $select_string .= ' SELECTED';
      $select_string .= '>' . $zones['geo_zone_name'] . '</option>';
    }
    $select_string .= '</select>';

    return $select_string;
  }

  function tep_get_geo_zone_name($geo_zone_id) {
    $zones_query = tep_db_query("select geo_zone_name from " . TABLE_GEO_ZONES . " where geo_zone_id = '" . (int)$geo_zone_id . "'");

    if (!tep_db_num_rows($zones_query)) {
      $geo_zone_name = $geo_zone_id;
    } else {
      $zones = tep_db_fetch_array($zones_query);
      $geo_zone_name = $zones['geo_zone_name'];
    }

    return $geo_zone_name;
  }

  function tep_address_format($address_format_id, $address, $html, $boln, $eoln) {
    $address_format_query = tep_db_query("select address_format as format from " . TABLE_ADDRESS_FORMAT . " where address_format_id = '" . (int)$address_format_id . "'");
    $address_format = tep_db_fetch_array($address_format_query);

    $company = tep_output_string_protected($address['company']);
    if (isset($address['firstname']) && tep_not_null($address['firstname'])) {
      $firstname = tep_output_string_protected($address['firstname']);
      $lastname = tep_output_string_protected($address['lastname']);
    } elseif (isset($address['name']) && tep_not_null($address['name'])) {
      $firstname = tep_output_string_protected($address['name']);
      $lastname = '';
    } else {
      $firstname = '';
      $lastname = '';
    }
    $street = tep_output_string_protected($address['street_address']);
    $suburb = tep_output_string_protected($address['suburb']);
    $city = tep_output_string_protected($address['city']);
    $state = tep_output_string_protected($address['state']);

    if (isset($address['country_id']) && tep_not_null($address['country_id'])) {
      $country = tep_get_country_name($address['country_id']);

      if (isset($address['zone_id']) && tep_not_null($address['zone_id'])) {
        $state = tep_get_zone_code($address['country_id'], $address['zone_id'], $state);
      }
    } elseif (isset($address['country']) && tep_not_null($address['country'])) {
      $country = tep_output_string_protected($address['country']);
    } else {
      $country = '';
    }
    $postcode = tep_output_string_protected($address['postcode']);
    $zip = $postcode;

    if ($html) {
// HTML Mode
      $HR = '<hr />';
      $hr = '<hr />';
      if ( ($boln == '') && ($eoln == "\n") ) { // Values not specified, use rational defaults
        $CR = '<br />';
        $cr = '<br />';
        $eoln = $cr;
      } else { // Use values supplied
        $CR = $eoln . $boln;
        $cr = $CR;
      }
    } else {
// Text Mode
      $CR = $eoln;
      $cr = $CR;
      $HR = '----------------------------------------';
      $hr = '----------------------------------------';
    }

    if ( (ACCOUNT_COMPANY == 'true') && (tep_not_null($company)) ) {
      $lastname = $lastname . $cr . 'c/o ' . $company;
    }

    $statecomma = '';
    $streets = $street;
    if ($suburb != '') $streets = $street . $cr . $suburb;
    if ($country == '') $country = tep_output_string_protected($address['country']);
    if ($state != '') $statecomma = $state . ', ';

    $fmt = $address_format['format'];
    eval("\$address = \"$fmt\";");

    return $address;
  }

  ////////////////////////////////////////////////////////////////////////////////////////////////
  //
  // Function    : tep_get_zone_code
  //
  // Arguments   : country           country code string
  //               zone              state/province zone_id
  //               def_state         default string if zone==0
  //
  // Return      : state_prov_code   state/province code
  //
  // Description : Function to retrieve the state/province code (as in FL for Florida etc)
  //
  ////////////////////////////////////////////////////////////////////////////////////////////////
  function tep_get_zone_code($country, $zone, $def_state) {

    $state_prov_query = tep_db_query("select zone_code from " . TABLE_ZONES . " where zone_country_id = '" . (int)$country . "' and zone_id = '" . (int)$zone . "'");

    if (!tep_db_num_rows($state_prov_query)) {
      $state_prov_code = $def_state;
    }
    else {
      $state_prov_values = tep_db_fetch_array($state_prov_query);
      $state_prov_code = $state_prov_values['zone_code'];
    }

    return $state_prov_code;
  }

  function tep_get_uprid($prid, $params) {
    $uprid = $prid;
    if ( (is_array($params)) && (!strstr($prid, '{')) ) {
      while (list($option, $value) = each($params)) {
        $uprid = $uprid . '{' . $option . '}' . $value;
      }
    }

    return $uprid;
  }

  function tep_get_prid($uprid) {
    $pieces = explode('{', $uprid);

    return $pieces[0];
  }

  function tep_get_languages() {
    $languages_query = tep_db_query("select languages_id, name, code, image, directory from " . TABLE_LANGUAGES . " order by sort_order");
    while ($languages = tep_db_fetch_array($languages_query)) {
      $languages_array[] = array('id' => $languages['languages_id'],
                                 'name' => $languages['name'],
                                 'code' => $languages['code'],
                                 'image' => $languages['image'],
                                 'directory' => $languages['directory']);
    }

    return $languages_array;
  }

  function tep_get_category_name($category_id, $language_id) {
    $category_query = tep_db_query("select categories_name from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$category_id . "' and language_id = '" . (int)$language_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_name'];
  }

  function tep_get_orders_status_name($orders_status_id, $language_id = '') {
    global $languages_id;

    if (!$language_id) $language_id = $languages_id;
    $orders_status_query = tep_db_query("select orders_status_name from " . TABLE_ORDERS_STATUS . " where orders_status_id = '" . (int)$orders_status_id . "' and language_id = '" . (int)$language_id . "'");
    $orders_status = tep_db_fetch_array($orders_status_query);

    return $orders_status['orders_status_name'];
  }

  function tep_get_orders_status() {
    global $languages_id;

    $orders_status_array = array();
    $orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int)$languages_id . "' order by orders_status_id");
    while ($orders_status = tep_db_fetch_array($orders_status_query)) {
      $orders_status_array[] = array('id' => $orders_status['orders_status_id'],
                                     'text' => $orders_status['orders_status_name']);
    }

    return $orders_status_array;
  }

  function tep_get_products_name($product_id, $language_id = 0) {
    global $languages_id;

    if ($language_id == 0) $language_id = $languages_id;
    $product_query = tep_db_query("select products_name from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_name'];
  }

        function tep_get_products_model($product_id) {
          $product_query = tep_db_query("select products_model from " . TABLE_PRODUCTS . " where products_id = '" . (int)$product_id . "'");
          $product = tep_db_fetch_array($product_query);

          return $product['products_model'];
        }

  function tep_get_products_description($product_id, $language_id) {
    $product_query = tep_db_query("select products_description from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_description'];
  }

// extra sites info

  function tep_get_extra_products_name($product_id, $store_id) {
    $product_query = tep_db_query("select products_name from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_name'];
  }

  function tep_get_extra_products_description($product_id, $store_id) {
    $product_query = tep_db_query("select products_description from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_description'];
  }

  function tep_get_extra_products_head_title_tag($product_id, $store_id) {
    $product_query = tep_db_query("select products_head_title_tag from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_head_title_tag'];
  }

  function tep_get_extra_products_head_desc_tag($product_id, $store_id) {
    $product_query = tep_db_query("select products_head_desc_tag from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_head_desc_tag'];
  }

  function tep_get_extra_products_head_keywords_tag($product_id, $store_id) {
    $product_query = tep_db_query("select products_head_keywords_tag from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_head_keywords_tag'];
  }

  function tep_get_extra_products_price($product_id, $store_id) {
    $product_query = tep_db_query("select products_price from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_price'];
  }

  function tep_get_extra_products_image($product_id, $store_id) {
    $product_query = tep_db_query("select products_image from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);
    return $product['products_image'];
  }

  function tep_get_extra_products_tax_class_id($product_id, $store_id) {
    $product_query = tep_db_query("select products_tax_class_id from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "' and store_id = '" . (int)$store_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_tax_class_id'];
  }

// extra sites info


  function tep_get_products_tasting_notes($product_id, $language_id) {
    $product_query = tep_db_query("select products_tasting_notes from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_tasting_notes'];
  }

  function tep_get_products_taste_test($product_id, $language_id) {
    $product_query = tep_db_query("select products_taste_test from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_taste_test'];
  }

  function tep_get_products_awards($product_id, $language_id) {
    $product_query = tep_db_query("select products_awards from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_awards'];
  }

  function tep_get_products_read_more($product_id, $language_id) {
    $product_query = tep_db_query("select products_read_more from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_read_more'];
  }  

function tep_get_category_htc_title($category_id, $language_id) {
    $category_query = tep_db_query("select categories_htc_title_tag from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$category_id . "' and language_id = '" . (int)$language_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_htc_title_tag'];
  }

  function tep_get_category_htc_desc($category_id, $language_id) {
    $category_query = tep_db_query("select categories_htc_desc_tag from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$category_id . "' and language_id = '" . (int)$language_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_htc_desc_tag'];
  }

  function tep_get_category_htc_keywords($category_id, $language_id) {
    $category_query = tep_db_query("select categories_htc_keywords_tag from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$category_id . "' and language_id = '" . (int)$language_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_htc_keywords_tag'];
  }

//////////// EXTRA STORES

  function tep_get_category_extra_name($category_id, $store_id) {
    $category_query = tep_db_query("select categories_name from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$category_id . "' and store_id = '" . (int)$store_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_name'];
  }

  function tep_get_category_extra_description($category_id, $store_id) {
    $category_query = tep_db_query("select categories_description from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . $category_id . "' and store_id = '" . $store_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_description'];
  }
  function tep_get_category_extra_image($category_id, $store_id) {
    $category_query = tep_db_query("select categories_image from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . $category_id . "' and store_id = '" . $store_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_image'];
  }

  function tep_get_category_extra_htc_title_tag($category_id, $store_id) {
    $category_query = tep_db_query("select categories_htc_title_tag from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$category_id . "' and store_id = '" . (int)$store_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_htc_title_tag'];
  }

  function tep_get_category_extra_htc_desc_tag($category_id, $store_id) {
    $category_query = tep_db_query("select categories_htc_desc_tag from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$category_id . "' and store_id = '" . (int)$store_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_htc_desc_tag'];
  }

  function tep_get_category_extra_htc_keywords_tag($category_id, $store_id) {
    $category_query = tep_db_query("select categories_htc_keywords_tag from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$category_id . "' and store_id = '" . (int)$store_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['categories_htc_keywords_tag'];
  }

  function tep_get_category_extra_sort_order($category_id, $store_id) {
    $category_query = tep_db_query("select sort_order from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$category_id . "' and store_id = '" . (int)$store_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['sort_order'];
  }

  function tep_get_category_extra_parent_id($category_id, $store_id) {
    $category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$category_id . "' and store_id = '" . (int)$store_id . "'");
    $category = tep_db_fetch_array($category_query);

    return $category['parent_id'];
  }

//////////// EXTRA STORES


  function tep_get_products_head_title_tag($product_id, $language_id) {
    $product_query = tep_db_query("select products_head_title_tag from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_head_title_tag'];
  }

  function tep_get_products_head_desc_tag($product_id, $language_id) {
    $product_query = tep_db_query("select products_head_desc_tag from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_head_desc_tag'];
  }

  function tep_get_products_head_keywords_tag($product_id, $language_id) {
    $product_query = tep_db_query("select products_head_keywords_tag from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_head_keywords_tag'];
  }
  function tep_get_manufacturer_htc_title($manufacturer_id, $language_id) {
    $manufacturer_query = tep_db_query("select manufacturers_htc_title_tag from " . TABLE_MANUFACTURERS_INFO . " where manufacturers_id = '" . (int)$manufacturer_id . "' and languages_id = '" . (int)$language_id . "'");
    $manufacturer = tep_db_fetch_array($manufacturer_query);

    return $manufacturer['manufacturers_htc_title_tag'];
  }

  function tep_get_manufacturer_htc_desc($manufacturer_id, $language_id) {
    $manufacturer_query = tep_db_query("select manufacturers_htc_desc_tag from " . TABLE_MANUFACTURERS_INFO . " where manufacturers_id = '" . (int)$manufacturer_id . "' and languages_id = '" . (int)$language_id . "'");
    $manufacturer = tep_db_fetch_array($manufacturer_query);

    return $manufacturer['manufacturers_htc_desc_tag'];
  }

  function tep_get_manufacturer_htc_keywords($manufacturer_id, $language_id) {
    $manufacturer_query = tep_db_query("select manufacturers_htc_keywords_tag from " . TABLE_MANUFACTURERS_INFO . " where manufacturers_id = '" . (int)$manufacturer_id . "' and languages_id = '" . (int)$language_id . "'");
    $manufacturer = tep_db_fetch_array($manufacturer_query);

    return $manufacturer['manufacturers_htc_keywords_tag'];
  }

  function tep_get_manufacturer_htc_description($manufacturer_id, $language_id) {
    $manufacturer_query = tep_db_query("select manufacturers_htc_description from " . TABLE_MANUFACTURERS_INFO . " where manufacturers_id = '" . (int)$manufacturer_id . "' and languages_id = '" . (int)$language_id . "'");
    $manufacturer = tep_db_fetch_array($manufacturer_query);

    return $manufacturer['manufacturers_htc_description'];
  }


  function tep_get_products_url($product_id, $language_id) {
    $product_query = tep_db_query("select products_url from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "' and language_id = '" . (int)$language_id . "'");
    $product = tep_db_fetch_array($product_query);

    return $product['products_url'];
  }

////
// Return the manufacturers URL in the needed language
// TABLES: manufacturers_info
  function tep_get_manufacturer_url($manufacturer_id, $language_id) {
    $manufacturer_query = tep_db_query("select manufacturers_url from " . TABLE_MANUFACTURERS_INFO . " where manufacturers_id = '" . (int)$manufacturer_id . "' and languages_id = '" . (int)$language_id . "'");
    $manufacturer = tep_db_fetch_array($manufacturer_query);

    return $manufacturer['manufacturers_url'];
  }

////
// Wrapper for class_exists() function
// This function is not available in all PHP versions so we test it before using it.
  function tep_class_exists($class_name) {
    if (function_exists('class_exists')) {
      return class_exists($class_name);
    } else {
      return true;
    }
  }

////
// Count how many products exist in a category
// TABLES: products, products_to_categories, categories
  function tep_products_in_category_count($categories_id, $include_deactivated = false) {
    $products_count = 0;

    if ($include_deactivated) {
      $products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2c.products_id and p2c.categories_id = '" . (int)$categories_id . "'");
    } else {
      $products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2c.products_id and p.products_status = '1' and p2c.categories_id = '" . (int)$categories_id . "'");
    }

    $products = tep_db_fetch_array($products_query);

    $products_count += $products['total'];

    $childs_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$categories_id . "'");
    if (tep_db_num_rows($childs_query)) {
      while ($childs = tep_db_fetch_array($childs_query)) {
        $products_count += tep_products_in_category_count($childs['categories_id'], $include_deactivated);
      }
    }

    return $products_count;
  }

////
// Count how many subcategories exist in a category
// TABLES: categories
  function tep_childs_in_category_count($categories_id) {
    $categories_count = 0;

    $categories_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$categories_id . "'");
    while ($categories = tep_db_fetch_array($categories_query)) {
      $categories_count++;
      $categories_count += tep_childs_in_category_count($categories['categories_id']);
    }

    return $categories_count;
  }

////
// Returns an array with countries
// TABLES: countries
  function tep_get_countries($default = '') {
    $countries_array = array();
    if ($default) {
      $countries_array[] = array('id' => '',
                                 'text' => $default);
    }
    $countries_query = tep_db_query("select countries_id, countries_name from " . TABLE_COUNTRIES . " order by countries_name");
    while ($countries = tep_db_fetch_array($countries_query)) {
      $countries_array[] = array('id' => $countries['countries_id'],
                                 'text' => $countries['countries_name']);
    }

    return $countries_array;
  }

////
// return an array with country zones
  function tep_get_country_zones($country_id) {
    $zones_array = array();
    $zones_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int)$country_id . "' order by zone_name");
    while ($zones = tep_db_fetch_array($zones_query)) {
      $zones_array[] = array('id' => $zones['zone_id'],
                             'text' => $zones['zone_name']);
    }

    return $zones_array;
  }

  function tep_prepare_country_zones_pull_down($country_id = '') {
// preset the width of the drop-down for Netscape
    $pre = '';
    if ( (!tep_browser_detect('MSIE')) && (tep_browser_detect('Mozilla/4')) ) {
      for ($i=0; $i<45; $i++) $pre .= '&nbsp;';
    }

    $zones = tep_get_country_zones($country_id);

    if (sizeof($zones) > 0) {
      $zones_select = array(array('id' => '', 'text' => PLEASE_SELECT));
      $zones = array_merge($zones_select, $zones);
    } else {
      $zones = array(array('id' => '', 'text' => TYPE_BELOW));
// create dummy options for Netscape to preset the height of the drop-down
      if ( (!tep_browser_detect('MSIE')) && (tep_browser_detect('Mozilla/4')) ) {
        for ($i=0; $i<9; $i++) {
          $zones[] = array('id' => '', 'text' => $pre);
        }
      }
    }

    return $zones;
  }

////
// Get list of address_format_id's
  function tep_get_address_formats() {
    $address_format_query = tep_db_query("select address_format_id from " . TABLE_ADDRESS_FORMAT . " order by address_format_id");
    $address_format_array = array();
    while ($address_format_values = tep_db_fetch_array($address_format_query)) {
      $address_format_array[] = array('id' => $address_format_values['address_format_id'],
                                      'text' => $address_format_values['address_format_id']);
    }
    return $address_format_array;
  }

////
// Alias function for Store configuration values in the Administration Tool
  function tep_cfg_pull_down_country_list($country_id) {
    return tep_draw_pull_down_menu('configuration_value', tep_get_countries(), $country_id);
  }

  function tep_cfg_pull_down_zone_list($zone_id) {
    return tep_draw_pull_down_menu('configuration_value', tep_get_country_zones(STORE_COUNTRY), $zone_id);
  }

  function tep_cfg_pull_down_tax_classes($tax_class_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $tax_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
      $tax_class_array[] = array('id' => $tax_class['tax_class_id'],
                                 'text' => $tax_class['tax_class_title']);
    }

    return tep_draw_pull_down_menu($name, $tax_class_array, $tax_class_id);
  }

////
// Function to read in text area in admin
 function tep_cfg_textarea($text) {
    return tep_draw_textarea_field('configuration_value', false, 35, 5, $text);
  }

  function tep_cfg_get_zone_name($zone_id) {
    $zone_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_id = '" . (int)$zone_id . "'");

    if (!tep_db_num_rows($zone_query)) {
      return $zone_id;
    } else {
      $zone = tep_db_fetch_array($zone_query);
      return $zone['zone_name'];
    }
  }

////
// Sets the status of a banner
  function tep_set_banner_status($banners_id, $status) {
    if ($status == '1') {
      return tep_db_query("update " . TABLE_BANNERS . " set status = '1', expires_impressions = NULL, expires_date = NULL, date_status_change = NULL where banners_id = '" . $banners_id . "'");
    } elseif ($status == '0') {
      return tep_db_query("update " . TABLE_BANNERS . " set status = '0', date_status_change = now() where banners_id = '" . $banners_id . "'");
    } else {
      return -1;
    }
  }

////
// Sets the status of a testimonial
  function tep_set_testimonial_status($testimonials_id, $status) {
    if ($status == '1') {
      return tep_db_query("update " . TABLE_TESTIMONIALS . " set status = '1' where testimonials_id = '" . $testimonials_id . "'");
    } elseif ($status == '0') {
      return tep_db_query("update " . TABLE_TESTIMONIALS . " set status = '0' where testimonials_id = '" . $testimonials_id . "'");
    } else {
      return -1;
    }
  }

////
// Sets the status of a testimonial
  function tep_set_testimonial_featured($testimonials_id, $featured) {
    if ($featured == '1') {
      return tep_db_query("update " . TABLE_TESTIMONIALS . " set featured = '1' where testimonials_id = '" . $testimonials_id . "'");
    } elseif ($featured == '0') {
      return tep_db_query("update " . TABLE_TESTIMONIALS . " set featured = '0' where testimonials_id = '" . $testimonials_id . "'");
    } else {
      return -1;
    }
  }

////
// Sets the status of a banner
  function tep_set_schedule_status($scheduler_id, $status) {
    if ($status == '1') {
      return tep_db_query("update " . TABLE_SCHEDULER . " set status = '1' where scheduler_id = '" . $scheduler_id . "'");
    } elseif ($status == '0') {
      return tep_db_query("update " . TABLE_SCHEDULER . " set status = '0' where scheduler_id = '" . $scheduler_id . "'");
    } else {
      return -1;
    }
  }

  function tep_send_sampler_email($product_type, $status, $bundle_id, $sub_product_id) {

    global $messageStack;

    $bundle_query = tep_db_query("select pd.products_name from " . TABLE_PRODUCTS_DESCRIPTION . " pd where pd.products_id = '" . (int)$bundle_id . "'");
    $bundle_info = tep_db_fetch_array($bundle_query);

  if($product_type == '1' && $status == '1'){
    // $product_type 1) Bundle has changed ...
    // Email 1 - $status 0,2,3,4) - Sampler is manually set active when not all are in stock
    $email_subject = 'Sampler Warning';
    $email_title = 'WARNING';
    $email_generic_body_text = 'The following sampler has been set to active when not all items are in stock / enabled ...<br /><br />';
    $email_generic_body_text .= '<a href="' . tep_catalog_href_link('product_info.php', 'products_id=' . $bundle_id) . '">' . $bundle_info['products_name'] . '</a>';
    
    if (isset($messageStack) && is_object($messageStack) && method_exists($messageStack, 'add_session')) {
     $messageStack->add_session('Not all items in this sampler are in stock / enabled!', 'warning');
    }

  } elseif($product_type == '2'){
    // $product_type 2) Sub Product has changed ...
   if($status == '1'){
    // Email 2 - $status 1) Sampler active because Subproduct is now active (and all other sub products) ...
    $email_subject = 'Sampler Reactivated';
    $email_title = 'Sampler reactivated';
    $email_generic_body_text = 'The following sampler has been automatically set to active as all items within the sampler are now active...<br /><br />';
    $email_generic_body_text .= '<a href="' . tep_catalog_href_link('product_info.php', 'products_id=' . $bundle_id) . '">' . $bundle_info['products_name'] . '</a>';

   } else {
    // Email 3 - $status 0,2,3,4) Sampler is disabled because Subproduct is now inactive ...
    $email_subject = 'Sampler Disabled';
    $email_title = 'Sampler disabled';
    $email_generic_body_text = 'The following sampler has been automatically disabled as an item within the sampler was disabled...<br /><br />';
    $email_generic_body_text .= '<a href="' . tep_catalog_href_link('product_info.php', 'products_id=' . $bundle_id) . '">' . $bundle_info['products_name'] . '</a>';

   }
  }

if($email_generic_body_text){

  $email_text  = EMAIL_GENERIC_TEMPLATE_START;
  $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;
  $email_text .= '<h4 style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 20px;margin-bottom: 8px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">' . $email_title . '</h4><p class="mbe" style="font-family: Helvetica, Arial, sans-serif;font-size: 16px;line-height: 23px;color: #616161;mso-line-height-rule: exactly;display: block;margin-top: 16px;margin-bottom: 0;"></p>' . $email_generic_body_text . '</p>';
  $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
  $email_text .= EMAIL_GENERIC_TEMPLATE_END;

  tep_mail('Out of stock - C.Gars Ltd', '<EMAIL>', $email_subject, $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

}

  }

  function tep_set_products_bundle_status($products_id, $status) {

   // Is it a product belonging to a bundle / cigar in sampler?
   $bundle_query = tep_db_query("select pb.bundle_id, p.products_status as bundle_status from " . TABLE_PRODUCTS_BUNDLES . " pb, " . TABLE_PRODUCTS . " p where pb.subproduct_id = '" . (int)$products_id . "' and pb.bundle_id = p.products_id");

 	 while ($bundle_info = tep_db_fetch_array($bundle_query)) { // Yes
    if($status == '1') { // sub_product status
     if($bundle_info['bundle_status'] == '2' || $bundle_info['bundle_status'] == '4'){ // if the bundle is currently out of stock (not disabled!)
      // are all products in the current bundle active now?..
      $stock_query = tep_db_query("select p.products_id from " . TABLE_PRODUCTS_BUNDLES . " pb, " . TABLE_PRODUCTS . " p where pb.bundle_id = '" . (int)$bundle_info['bundle_id'] . "' and pb.subproduct_id = p.products_id and p.products_status != '1'");

      if(!tep_db_num_rows($stock_query)) { // there are no inactive products in this bundle so set the bundle to active...
   	   tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '1', products_quantity = '9999', products_last_modified = now(), products_date_added = now() where products_id = '" . (int)$bundle_info['bundle_id'] . "'");
       tep_log_this('Sampler activated because product_id ' . (int)$products_id . ' and all other items are back in stock. ', (int)$bundle_info['bundle_id']);
       tep_send_sampler_email(2, $status, (int)$bundle_info['bundle_id'], (int)$products_id); // Email 2
      }
     }
    } else { // the product is now inactive and the sampler must be disabled
     if($bundle_info['bundle_status'] == '1'){ // if the bundle is currently active (dont change already disabled samplers!)
      tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '" . $status . "', products_last_modified = now() where products_id = '" . (int)$bundle_info['bundle_id'] . "'");
      tep_log_this('Sampler status changed to ' . $status . ' because product_id ' . (int)$products_id . ' status changed to this. ', (int)$bundle_info['bundle_id']);
      if($status < 1){ // cant use tep_set_product_visible_status
        tep_db_query("update " . TABLE_PRODUCTS . " set products_visible = '0' where products_id = '" . (int)$bundle_info['bundle_id'] . "'");
      }
      if($status == '2' || $status == '4'){ // OOS or Sold Out
        tep_db_query("delete from products_stock_date where products_id = '" . (int)$bundle_info['bundle_id'] . "'");
        tep_db_query("insert into products_stock_date (products_id, date_out_of_stock) values ('" . (int)$bundle_info['bundle_id'] . "', now())");
      }
      tep_send_sampler_email(2, $status, (int)$bundle_info['bundle_id'], (int)$products_id); // Email 3
     }
    }
   }

   if($status == '1') {
   // Is it a bundle / sampler?
   $bundle_query = tep_db_query("select pb.subproduct_id from " . TABLE_PRODUCTS_BUNDLES . " pb where pb.bundle_id = '" . (int)$products_id . "'");
 	 while ($bundle_details = tep_db_fetch_array($bundle_query)) { // Yes, so check subproducts...
    $sub_query = tep_db_query("select p.products_status, p.products_quantity from " . TABLE_PRODUCTS . " p where p.products_id = '" . (int)$bundle_details['subproduct_id'] . "'");
    $sub_info = tep_db_fetch_array($sub_query);
    if($sub_info['products_status'] != '1' || $sub_info['products_quantity'] < 1){ // its not active
      tep_send_sampler_email(1, $status, (int)$products_id, (int)$bundle_details['subproduct_id']); // Email 1
    }
   }
   }


  }



  ////
  // Sets the status of a product
    function tep_set_product_status($products_id, $status) {

      global $messageStack;

      if ($status == '1') { // Active
  	    tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '1', products_last_modified = now(), products_date_added = now() where products_id = '" . (int)$products_id . "'");

        // check stock level
        $stock_query = tep_db_query("select p.products_quantity from " . TABLE_PRODUCTS . " p where p.products_id = '" . (int)$products_id . "'");
        $stock_details = tep_db_fetch_array($stock_query);
        if($stock_details['products_quantity'] < 1){
         if (isset($messageStack) && is_object($messageStack) && method_exists($messageStack, 'add_session')) {
          $messageStack->add_session('The current stock level for the product just activated is zero!', 'warning');
         }
        }

      } elseif ($status == '0') { // Disabled
        tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '0', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
      } elseif ($status == '2') { // OOS
        tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '2', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
  	    tep_db_query("delete from products_stock_date where products_id = '" . (int)$products_id . "'");
  	    tep_db_query("insert into products_stock_date (products_id, date_out_of_stock) values ('" . (int)$products_id . "', now())");
      } elseif ($status == '3') { // Limited
        tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '3', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
      } elseif ($status == '4') { // Sold out
  	    tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '4', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
        tep_db_query("delete from products_stock_date where products_id = '" . (int)$products_id . "'");
  	    tep_db_query("insert into products_stock_date (products_id, date_out_of_stock) values ('" . (int)$products_id . "', now())");
      } elseif ($status == '5') { // Museum
        tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '5', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
      } elseif ($status == '6') { // Stashed
        tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '6', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
      } elseif ($status == '7') { // Showcase
        tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '7', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
  	  } else {
        return -1;
      }

        tep_set_products_bundle_status($products_id, $status);

        return true;
    }

////
// Sets a product to be tobacco / non tobacco
  function tep_set_product_cigar($products_id, $status) {

	if ($status == '1') {
      return tep_db_query("update " . TABLE_PRODUCTS . " set products_cigar = '1', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
	} elseif ($status == '2') {
      return tep_db_query("update " . TABLE_PRODUCTS . " set products_cigar = '2', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
	} elseif ($status == '0') {
      return tep_db_query("update " . TABLE_PRODUCTS . " set products_cigar = '0', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
    } else {
      return -1;
    }
  }

////
// Sets the status of a review
  function tep_set_review_status($reviews_id, $status) {
    if ($status == '1') {
      return tep_db_query("update " . TABLE_REVIEWS . " set reviews_status = '1', last_modified = now() where reviews_id = '" . (int)$reviews_id . "'");
    } elseif ($status == '0') {
      return tep_db_query("update " . TABLE_REVIEWS . " set reviews_status = '0', last_modified = now() where reviews_id = '" . (int)$reviews_id . "'");
    } else {
      return -1;
    }
  }

////
// Return a product's special price (returns nothing if there is no offer)
// TABLES: products
  function tep_get_products_special_price($product_id) {
    $product_query = tep_db_query("select specials_new_products_price from " . TABLE_SPECIALS . " where products_id = '" . (int)$product_id . "' and status = 1");
    $product = tep_db_fetch_array($product_query);

    return $product['specials_new_products_price'];
  }

////
// Sets the status of a product on special
  function tep_set_specials_status($specials_id, $status) {
    if ($status == '1') {
      return tep_db_query("update " . TABLE_SPECIALS . " set status = '1', expires_date = NULL, date_status_change = NULL where specials_id = '" . (int)$specials_id . "'");
    } elseif ($status == '0') {
      return tep_db_query("update " . TABLE_SPECIALS . " set status = '0', date_status_change = now() where specials_id = '" . (int)$specials_id . "'");
    } else {
      return -1;
    }
  }

////
// Sets timeout for the current script.
// Cant be used in safe mode.
  function tep_set_time_limit($limit) {
    if (!get_cfg_var('safe_mode')) {
      set_time_limit($limit);
    }
  }

////
// Alias function for Store configuration values in the Administration Tool
  function tep_cfg_select_option($select_array, $key_value, $key = '') {
    $string = '';

    for ($i=0, $n=sizeof($select_array); $i<$n; $i++) {
      $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');

      $string .= '<br /><input type="radio" name="' . $name . '" value="' . $select_array[$i] . '"';

      if ($key_value == $select_array[$i]) $string .= ' checked="checked"';

      $string .= ' /> ' . $select_array[$i];
    }

    return $string;
  }

////
// Alias function for module configuration keys
  function tep_mod_select_option($select_array, $key_name, $key_value) {
    reset($select_array);
    while (list($key, $value) = each($select_array)) {
      if (is_int($key)) $key = $value;
      $string .= '<br /><input type="radio" name="configuration[' . $key_name . ']" value="' . $key . '"';
      if ($key_value == $key) $string .= ' checked="checked"';
      $string .= ' /> ' . $value;
    }

    return $string;
  }

////
// Retreive server information
  function tep_get_system_information() {
    global $HTTP_SERVER_VARS;

    $db_query = tep_db_query("select now() as datetime");
    $db = tep_db_fetch_array($db_query);

    @list($system, $host, $kernel) = preg_split('/[\s,]+/', @exec('uname -a'), 5);

    $data = array();

    $data['oscommerce']  = array('version' => tep_get_version());

    $data['system'] = array('date' => date('Y-m-d H:i:s O T'),
                            'os' => PHP_OS,
                            'kernel' => $kernel,
                            'uptime' => @exec('uptime'),
                            'http_server' => $HTTP_SERVER_VARS['SERVER_SOFTWARE']);

    $data['mysql']  = array('version' => tep_db_get_server_info(),
                            'date' => $db['datetime']);

    $data['php']    = array('version' => PHP_VERSION,
                            'zend' => zend_version(),
                            'sapi' => PHP_SAPI,
                            'int_size'	=> defined('PHP_INT_SIZE') ? PHP_INT_SIZE : '',
                            'safe_mode'	=> (int) @ini_get('safe_mode'),
                            'open_basedir' => (int) @ini_get('open_basedir'),
                            'memory_limit' => @ini_get('memory_limit'),
                            'error_reporting' => error_reporting(),
                            'display_errors' => (int)@ini_get('display_errors'),
                            'allow_url_fopen' => (int) @ini_get('allow_url_fopen'),
                            'allow_url_include' => (int) @ini_get('allow_url_include'),
                            'file_uploads' => (int) @ini_get('file_uploads'),
                            'upload_max_filesize' => @ini_get('upload_max_filesize'),
                            'post_max_size' => @ini_get('post_max_size'),
                            'disable_functions' => @ini_get('disable_functions'),
                            'disable_classes' => @ini_get('disable_classes'),
                            'enable_dl'	=> (int) @ini_get('enable_dl'),
                            'magic_quotes_gpc' => (int) @ini_get('magic_quotes_gpc'),
                            'register_globals' => (int) @ini_get('register_globals'),
                            'filter.default'   => @ini_get('filter.default'),
                            'zend.ze1_compatibility_mode' => (int) @ini_get('zend.ze1_compatibility_mode'),
                            'unicode.semantics' => (int) @ini_get('unicode.semantics'),
                            'zend_thread_safty'	=> (int) function_exists('zend_thread_id'),
                            'extensions' => get_loaded_extensions());

    return $data;
  }

  function tep_generate_category_path($id, $from = 'category', $categories_array = '', $index = 0) {
    global $languages_id;

    if (!is_array($categories_array)) $categories_array = array();

    if ($from == 'product') {
      $categories_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int)$id . "'");
      while ($categories = tep_db_fetch_array($categories_query)) {
        if ($categories['categories_id'] == '0') {
          $categories_array[$index][] = array('id' => '0', 'text' => TEXT_TOP);
        } else {
          $category_query = tep_db_query("select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = '" . (int)$categories['categories_id'] . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "'");
          $category = tep_db_fetch_array($category_query);
          $categories_array[$index][] = array('id' => $categories['categories_id'], 'text' => $category['categories_name']);
          if ( (tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0') ) $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
          $categories_array[$index] = array_reverse($categories_array[$index]);
        }
        $index++;
      }
    } elseif ($from == 'category') {
      $category_query = tep_db_query("select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = '" . (int)$id . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "'");
      $category = tep_db_fetch_array($category_query);
      $categories_array[$index][] = array('id' => $id, 'text' => $category['categories_name']);
      if ( (tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0') ) $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
    }

    return $categories_array;
  }

	function tep_output_generated_category_path( $id, $from = 'category' )
{
	$calculated_category_path_string = '';
	$calculated_category_path = tep_generate_category_path( $id, $from );
	$n = sizeof( $calculated_category_path );

	for ( $i = 0; $i < $n; $i++ ) {
		$j = sizeof( $calculated_category_path[$i] ) - 1;
	 for (; $j >= 0; $j-- ) {
		 $calculated_category_path_string .= $calculated_category_path[$i][$j]['text'];
		 if($n <= $j) { $calculated_category_path_string .= ' > '; }
	 }
		$calculated_category_path_string = $calculated_category_path_string . '<br>';
	}

	$calculated_category_path_string = substr( $calculated_category_path_string, 0, -4 );

	if ( strlen( $calculated_category_path_string ) < 1 ) $calculated_category_path_string = TEXT_TOP;

	return $calculated_category_path_string;
    }

  function tep_get_generated_category_path_ids($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i=0, $n=sizeof($calculated_category_path); $i<$n; $i++) {
      for ($j=0, $k=sizeof($calculated_category_path[$i]); $j<$k; $j++) {
        $calculated_category_path_string .= $calculated_category_path[$i][$j]['id'] . '_';
      }
      $calculated_category_path_string = substr($calculated_category_path_string, 0, -1) . '<br />';
    }
    $calculated_category_path_string = substr($calculated_category_path_string, 0, -6);

    if (strlen($calculated_category_path_string) < 1) $calculated_category_path_string = TEXT_TOP;

    return $calculated_category_path_string;
  }

  function tep_p2c_history_check($product_id, $category_id){
    $check_query = tep_db_query("select count(*) as total from products_to_categories_history where products_id = '" . tep_db_input($product_id) . "' and categories_id = '" . tep_db_input($category_id) . "'");
    $check = tep_db_fetch_array($check_query);
    if ($check['total'] < '1') {
      return true;
    }
  }

  function tep_p2c_history_add_product($product_id){
    $insert_data_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . $product_id . "'");
    while($insert_data = tep_db_fetch_array($insert_data_query)){
      if(tep_p2c_history_check($product_id, $insert_data['categories_id'])){
        tep_db_query("insert into products_to_categories_history (products_id, categories_id) values ('" . tep_db_input($product_id) . "', '" . tep_db_input($insert_data['categories_id']) . "')");
      }
    }
  }

  function tep_p2c_history_add_category($category_id){
    $insert_data_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . $category_id . "'");
    while($insert_data = tep_db_fetch_array($insert_data_query)){
      if(tep_p2c_history_check($insert_data['products_id'], $category_id)){
      tep_db_query("insert into products_to_categories_history (products_id, categories_id) values ('" . tep_db_input($insert_data['products_id']) . "', '" . tep_db_input($category_id) . "')");
      }
    }
  }

  function tep_p2c_history_add_prod_cat($product_id, $category_id){
    if(is_numeric($product_id) && is_numeric($category_id)){
      if(tep_p2c_history_check($product_id, $category_id)){
      tep_db_query("insert into products_to_categories_history (products_id, categories_id) values ('" . tep_db_input($product_id) . "', '" . tep_db_input($category_id) . "')");
      }
    }
  }

  function tep_remove_category($category_id) {
    $category_image_query = tep_db_query("select categories_image from " . TABLE_CATEGORIES . " where categories_id = '" . (int)$category_id . "'");
    $category_image = tep_db_fetch_array($category_image_query);

    $duplicate_image_query = tep_db_query("select count(*) as total from " . TABLE_CATEGORIES . " where categories_image = '" . tep_db_input($category_image['categories_image']) . "'");
    $duplicate_image = tep_db_fetch_array($duplicate_image_query);

    if ($duplicate_image['total'] < 2) {
      if (file_exists(DIR_FS_CATALOG_IMAGES . $category_image['categories_image'])) {
        @unlink(DIR_FS_CATALOG_IMAGES . $category_image['categories_image']);
      }
    }

    // backup TABLE_PRODUCTS_TO_CATEGORIES data for reports first..
    tep_p2c_history_add_category((int)$category_id);    

    tep_db_query("delete from " . TABLE_CATEGORIES . " where categories_id = '" . (int)$category_id . "'");
    tep_db_query("delete from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$category_id . "'");
    tep_db_query("delete from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . (int)$category_id . "'");

    tep_db_query("delete from " . TABLE_CATEGORIES_TO_STORES . " where categories_id = '" . (int)$category_id . "'");
    tep_db_query("delete from " . TABLE_CATEGORIES_STORES_EXTRA . " where categories_id = '" . (int)$category_id . "'");

    if (USE_CACHE == 'true') {
      tep_reset_cache_block('categories');
      tep_reset_cache_block('cat_links');
      tep_reset_cache_block('also_purchased');
      tep_reset_cache_block('mega_menu');
      tep_reset_cache_block('mega_menu_mobile');
      tep_reset_cache_block('mega_menu_davidoff');
      tep_reset_cache_block('filter_menu');
    }
  }


  function tep_remove_product($product_id) {

    // begin Bundled Products
    global $messageStack, $languages_id;
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE bundle_id = " . (int)$product_id);
    $bundle_check = tep_db_query('select p.products_model, pd.products_name from ' . TABLE_PRODUCTS . ' p, ' . TABLE_PRODUCTS_DESCRIPTION . ' pd, ' . TABLE_PRODUCTS_BUNDLES . ' pb where p.products_id = pd.products_id and pd.language_id = ' . (int)$languages_id . ' and p.products_id = pb.bundle_id and pb.subproduct_id = ' . (int)$product_id);
    // if product being deleted is contained in any bundles warn the user
    while ($bundle = tep_db_fetch_array($bundle_check)) {
      $messageStack->add_session(WARNING_PRODUCT_IN_BUNDLE . '(' . $bundle['products_model'] . ') ' . $bundle['products_name'], 'warning');
    }
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE subproduct_id = " . (int)$product_id);
    // end Bundled Products

	$product_image_query = tep_db_query("select products_image from " . TABLE_PRODUCTS . " where products_id = '" . (int)$product_id . "'");
    $product_image = tep_db_fetch_array($product_image_query);

    $duplicate_image_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " where products_image = '" . tep_db_input($product_image['products_image']) . "'");
    $duplicate_image = tep_db_fetch_array($duplicate_image_query);

    if ($duplicate_image['total'] < 2) {
      if (file_exists(DIR_FS_CATALOG_IMAGES . $product_image['products_image'])) {
        @unlink(DIR_FS_CATALOG_IMAGES . $product_image['products_image']);
      }
    }

    $product_images_query = tep_db_query("select image from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int)$product_id . "'");
    if (tep_db_num_rows($product_images_query)) {
      while ($product_images = tep_db_fetch_array($product_images_query)) {
        $duplicate_image_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_IMAGES . " where image = '" . tep_db_input($product_images['image']) . "'");
        $duplicate_image = tep_db_fetch_array($duplicate_image_query);

        if ($duplicate_image['total'] < 2) {
          if (file_exists(DIR_FS_CATALOG_IMAGES . $product_images['image'])) {
            @unlink(DIR_FS_CATALOG_IMAGES . $product_images['image']);
          }
        }
      }

      tep_db_query("delete from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int)$product_id . "'");
    }

    // backup TABLE_PRODUCTS_TO_CATEGORIES data for reports first..
    tep_p2c_history_add_product((int)$product_id);

    tep_db_query("delete from " . TABLE_SPECIALS . " where products_id = '" . (int)$product_id . "'");
    tep_db_query("delete from " . TABLE_PRODUCTS . " where products_id = '" . (int)$product_id . "'");
    tep_db_query("delete from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int)$product_id . "'");
    tep_db_query("delete from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int)$product_id . "'");
    tep_db_query("delete from " . TABLE_PRODUCTS_ATTRIBUTES . " where products_id = '" . (int)$product_id . "'");
    tep_db_query("delete from " . TABLE_CUSTOMERS_BASKET . " where products_id = '" . (int)$product_id . "' or products_id like '" . (int)$product_id . "{%'");
    tep_db_query("delete from " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " where products_id = '" . (int)$product_id . "' or products_id like '" . (int)$product_id . "{%'");
    tep_db_query("delete from " . TABLE_WISHLIST . " where products_id = '" . (int)$product_id . "' or products_id like '" . (int)$product_id . "{%'");
    tep_db_query("delete from " . TABLE_WISHLIST_ATTRIBUTES . " where products_id = '" . (int)$product_id . "' or products_id like '" . (int)$product_id . "{%'");
	tep_db_query("delete from " . TABLE_PRODUCTS_TO_STORES . " where products_id = '" . (int)$product_id . "'");
	tep_db_query("delete from " . TABLE_PRODUCTS_STORES_EXTRA . " where products_id = '" . (int)$product_id . "'");
	tep_db_query("delete from " . TABLE_PRODUCTS_TO_STOCK_LOCATION . " where products_id = '" . (int)$product_id . "'");
	tep_db_query("delete from " . TABLE_PRODUCTS_ADD_ON_CATEGORIES . " where products_id = '" . (int)$product_id . "'");
	tep_db_query("delete from " . TABLE_PRICE_MATCH . " where products_id = '" . (int)$product_id . "'");


    $product_reviews_query = tep_db_query("select reviews_id from " . TABLE_REVIEWS . " where products_id = '" . (int)$product_id . "'");

    while ($product_reviews = tep_db_fetch_array($product_reviews_query)) {
      tep_db_query("delete from " . TABLE_REVIEWS_DESCRIPTION . " where reviews_id = '" . (int)$product_reviews['reviews_id'] . "'");
    }
    tep_db_query("delete from " . TABLE_REVIEWS . " where products_id = '" . (int)$product_id . "'");

    if (USE_CACHE == 'true') {
      tep_reset_cache_block('categories');
      tep_reset_cache_block('cat_links');
      tep_reset_cache_block('also_purchased');
      tep_reset_cache_block('mega_menu');
      tep_reset_cache_block('mega_menu_mobile');
      tep_reset_cache_block('mega_menu_davidoff');
      tep_reset_cache_block('filter_menu');
    }
  }

// begin Bundled Products
    function tep_restock_bundle($bundle_id, $restock_qty) {
      $bundle_query = tep_db_query("select pb.subproduct_id, pb.subproduct_qty, p.products_bundle, p.products_status, p.products_quantity as prod_qty from " . TABLE_PRODUCTS_BUNDLES . " pb, " . TABLE_PRODUCTS . " p where p.products_id = pb.subproduct_id and bundle_id = '" . (int)$bundle_id . "' and pb.subproduct_type != '1'");
      while ($bundle_info = tep_db_fetch_array($bundle_query)) {
        $qty_restocked = $bundle_info['subproduct_qty'] * $restock_qty;
        if ($bundle_info['products_bundle'] == 'yes') {
          tep_restock_bundle($bundle_info['subproduct_id'], $qty_restocked); // bunde in a bundle
        } else {
          // products in bundle...
          tep_db_query("update " . TABLE_PRODUCTS . " set products_quantity = products_quantity + " . (int)$qty_restocked . ", products_ordered = products_ordered - " . (int)$qty_restocked . " where products_id = " . (int)$bundle_info['subproduct_id']);

         if($bundle_info['products_status'] != '1') {
          if($bundle_info['prod_qty'] < 1){
           tep_set_product_status($bundle_info['subproduct_id'], 1); // set the status to active
           tep_set_product_visible_status($bundle_info['subproduct_id']);
           tep_log_this('tep_restock_order / tep_restock_bundle : Product in bundle set to active ', (int)$bundle_info['subproduct_id']);
          } else {
           $do_not_reactivate_bundle = true;
          }
         }

        }
      }
      // reduce number of bundle sold
      tep_db_query("update " . TABLE_PRODUCTS . " set products_ordered = products_ordered - " . (int)$restock_qty . " where products_id = " . (int)$bundle_id);

      if(!$do_not_reactivate_bundle){
       tep_set_product_status($bundle_id, 1); // set the status to active
       tep_set_product_visible_status($bundle_id);
       tep_log_this('tep_restock_order / tep_restock_bundle : Set bundle to active ', (int)$bundle_id);
      }

    } // end function restock_bundle

// end Bundled Products

   // begin bundled products
  function tep_bundle_avoid($bundle_id) { // returns an array of bundle_ids containing the specified bundle
    $avoid_list = array();
    $check_query = tep_db_query('select bundle_id from ' . TABLE_PRODUCTS_BUNDLES . ' where subproduct_id = ' . (int)$bundle_id);
    while ($check = tep_db_fetch_array($check_query)) {
      $avoid_list[] = $check['bundle_id'];
      $tmp = tep_bundle_avoid($check['bundle_id']);
      $avoid_list = array_merge($avoid_list, $tmp);
    }
    return $avoid_list;
  }
  // end bundled products

   function tep_calculate_profit_margin($sale_price, $trade_price_ex_vat, $trade_price_discount = 0) {
	   // products_profit_margin is only calculated for the sorting feature on quick updates & product exports
	   if ($sale_price > 0) {

	   	$sale_price_ex_vat = $sale_price / 1.2;

	   	if($trade_price_discount != 0) {
	    	$trade_price_discount_amount = $trade_price_ex_vat * ($trade_price_discount / 100);
			$trade_price_ex_vat_with_discount = $trade_price_ex_vat - $trade_price_discount_amount;
			$profit_in_sterling = $sale_price_ex_vat - $trade_price_ex_vat_with_discount;
	   	} else {
	    	$trade_price_ex_vat_with_discount = $trade_price_ex_vat;
			$profit_in_sterling = $sale_price_ex_vat - $trade_price_ex_vat;
	   	}

	   	if($sale_price_ex_vat) {
	    	$profit_margin = ($profit_in_sterling / $sale_price_ex_vat) * 100;
	   	} else {
	    	$profit_margin = 0;
	   	}

	   } else {
	    $profit_margin = 0;
	   }

	   return $profit_margin;

   }

    function tep_get_bundle_data_array($bundle_id){

      $bundle_array = array();

	  // this product is a bundle so get contents data
      $bundle_query = tep_db_query("SELECT pb.subproduct_id, pb.subproduct_qty, pb.subproduct_type FROM " . TABLE_PRODUCTS_BUNDLES . " pb WHERE pb.bundle_id = '" . (int)$bundle_id . "' order by pb.subproduct_type");

	  while ($bundle_contents = tep_db_fetch_array($bundle_query)) {

	   $bundle_product_id = $bundle_contents['subproduct_id'];

	 if($bundle_contents['subproduct_type'] == '1') {

	   $type_query = tep_db_query("select b.* from " . TABLE_PRODUCTS_BUNDLES_EXTRA . " b where b.products_bundles_extra_id = '" . $bundle_product_id . "'");
	   $type = tep_db_fetch_array($type_query);

	   if($type['show_in_description']) { $bundle_name = $type['extra_name'] . ' (visible)'; } else { $bundle_name = $type['extra_name']; }
	   $bundle_price = $type['extra_price'];
	   $bundle_cost_price = $type['extra_cost_price'];
	   $show_in_description = $type['show_in_description'];
	   $bundle_margin = tep_calculate_profit_margin($type['extra_price'], $type['extra_cost_price'], 0);

	 } else {

	   $type_query = tep_db_query("SELECT p.products_price, p.products_trade_price, p.products_trade_price_discount, pd.products_name FROM " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS . " p WHERE p.products_id = '" . $bundle_product_id . "' and p.products_id = pd.products_id");
	   $type = tep_db_fetch_array($type_query);

	 if($type['products_trade_price_discount'] > 0) {
	   $bundle_trade_price_discount_amount = $type['products_trade_price'] * ($type['products_trade_price_discount'] / 100);
	   $bundle_trade_price_ex_vat_with_discount = $type['products_trade_price'] - $bundle_trade_price_discount_amount;
	 } else {
	   $bundle_trade_price_ex_vat_with_discount = $type['products_trade_price'];
	 }

	   $bundle_name = $type['products_name'];
	   $bundle_price = $type['products_price'];
	   $bundle_cost_price = $bundle_trade_price_ex_vat_with_discount;
	   $show_in_description = '0';
	   $bundle_margin = tep_calculate_profit_margin($type['products_price'], $type['products_trade_price'], $type['products_trade_price_discount']);

	 }

		$bundle_array[] = array('id' => $bundle_product_id,
								'price' => $bundle_price,
								'cost_price' => $bundle_cost_price,
                                'qty' => $bundle_contents['subproduct_qty'],
                                'name' => $bundle_name,
								'desc' => $show_in_description,
                                'type' => $bundle_contents['subproduct_type'],
								'margin' => $bundle_margin
								);
      }

        return $bundle_array;

      }

  // update bundle cost price if an item's cost price changes
  function tep_check_bundle_cost_price($products_id) {

    // get bundles with this product included
	$bundle_query = tep_db_query("select pb.bundle_id from " . TABLE_PRODUCTS_BUNDLES . " pb where pb.subproduct_id = '" . (int)$products_id . "' group by pb.bundle_id ");

	  while ($bundle_info = tep_db_fetch_array($bundle_query)) {

	$bundle_array = array();
	$bundle_trade_price_total = 0;
	$bundle_count = 0;

	$bundle_array = tep_get_bundle_data_array($bundle_info['bundle_id']);	// get the data from the bundle
	$bundle_count = count($bundle_array);

	for ($i=0, $n = $bundle_count ? $bundle_count+1 : 3; $i<$n; $i++) {
	  if(isset($bundle_array[$i]['cost_price'])) {
	   $bundle_trade_price_total += ($bundle_array[$i]['cost_price'] * $bundle_array[$i]['qty']);  // collect the cost/trade prices
	  }
    }

	// update the bundle trade price
	tep_db_query("update " . TABLE_PRODUCTS . " set products_trade_price = " . number_format($bundle_trade_price_total, 4) . " where products_id = '" . (int)$bundle_info['bundle_id'] . "'");

	tep_log_this('products_trade_price for ' . $products_id . ' changed, so bundle products_trade_price updated to ' . number_format($bundle_trade_price_total, 4), (int)$bundle_info['bundle_id']);

      }

  }


  function tep_remove_order($order_id) {
    tep_db_query("delete from " . TABLE_ORDERS . " where orders_id = '" . (int)$order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_PRODUCTS . " where orders_id = '" . (int)$order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " where orders_id = '" . (int)$order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . (int)$order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$order_id . "'");
    tep_db_query("delete from " . TABLE_CUSTOMERS_POINTS_PENDING . " where orders_id = '" . (int)$order_id . "'");
    tep_db_query("delete from " . TABLE_CGARS_PLUS . " where orders_id = '" . (int)$order_id . "'");
  }

function tep_restock_order($order_id) {

    // Fetch ordered products and their current stock status
    $order_query = tep_db_query("
        SELECT o.products_id, o.products_quantity, p.products_bundle, p.products_status, p.products_quantity as prod_qty 
        FROM " . TABLE_ORDERS_PRODUCTS . " o 
        JOIN " . TABLE_PRODUCTS . " p ON o.products_id = p.products_id 
        WHERE o.orders_id = " . (int)$order_id
    );

    $product_string = '';

    while ($order = tep_db_fetch_array($order_query)) {

        $product_string .= (int)$order['products_id'] . ' : ' . (int)$order['products_quantity'] . ' : ' . (int)$order['prod_qty'] . ' - ';

        // Handle bundle products separately
        if ($order['products_bundle'] === 'yes') {
            tep_restock_bundle($order['products_id'], $order['products_quantity']);
        } else {
            // Restock the product
            tep_db_query("UPDATE " . TABLE_PRODUCTS . " 
                          SET products_quantity = products_quantity + " . (int)$order['products_quantity'] . ", 
                              products_ordered = products_ordered - " . (int)$order['products_quantity'] . " 
                          WHERE products_id = '" . (int)$order['products_id'] . "'");

            tep_log_this('tep_restock_order: products_quantity updated by + ' . (int)$order['products_quantity'], (int)$order['products_id']);

            // Check if the product should be reactivated
            if ((int)$order['products_status'] !== 1) {
                tep_set_product_status($order['products_id'], 1); // Set product to active
                tep_set_product_visible_status($order['products_id']);
                tep_log_this('tep_restock_order: Product set to active', (int)$order['products_id']);
            }
        }
    }

    // Log the entire request
    error_log(date("Y-m-d H:i:s") . " : Order ID: " . $order_id . "\n" . "Product Data: " . $product_string . "\n", 3, "/home/<USER>/public_html/tep/restock.log");

}

function tep_reset_cache_block($cache_block) {
  global $cache_blocks;

  for ($i = 0, $n = sizeof($cache_blocks); $i < $n; $i++) {
      if ($cache_blocks[$i]['code'] == $cache_block) {
          if ($cache_block == 'filter_menu') {
              // Track the oldest file creation time
              $oldest_file_time = PHP_INT_MAX;
              $files_to_delete = [];

              if ($dir = @opendir(DIR_FS_CACHE)) {
                  while ($cache_file = readdir($dir)) {
                      if (strpos($cache_file, 'filter_menu') !== false) {
                          $file_path = DIR_FS_CACHE . $cache_file;
                          $file_time = filemtime($file_path); // Get file last modified time

                          if ($file_time < $oldest_file_time) {
                              $oldest_file_time = $file_time;
                          }

                          $files_to_delete[] = $file_path;
                      }
                  }
                  closedir($dir);
              }

              // Check if the oldest file is older than 4 hours (14400 seconds)
              if (time() - $oldest_file_time > 14400) {
                  foreach ($files_to_delete as $file) {
                      @unlink($file);
                  }
              }

              break; // Exit loop early since we've handled 'filter_menu'
          }

          // Default behavior for other cache blocks
          if ($cache_blocks[$i]['multiple']) {
              if ($dir = @opendir(DIR_FS_CACHE)) {
                  while ($cache_file = readdir($dir)) {
                      $cached_file = $cache_blocks[$i]['file'];
                      $languages = tep_get_languages();
                      for ($j = 0, $k = sizeof($languages); $j < $k; $j++) {
                          $cached_file_unlink = preg_replace('/-language/', '-' . $languages[$j]['directory'], $cached_file);
                          if (preg_match('/^' . $cached_file_unlink . '/', $cache_file)) {
                              @unlink(DIR_FS_CACHE . $cache_file);
                          }
                      }
                  }
                  closedir($dir);
              }
          } else {
              $cached_file = $cache_blocks[$i]['file'];
              $languages = tep_get_languages();
              for ($j = 0, $k = sizeof($languages); $j < $k; $j++) {
                  $cached_file = preg_replace('/-language/', '-' . $languages[$j]['directory'], $cached_file);
                  @unlink(DIR_FS_CACHE . $cached_file);
              }
          }
          break;
      }
  }
}

  function tep_get_file_permissions($mode) {
// determine type
    if ( ($mode & 0xC000) == 0xC000) { // unix domain socket
      $type = 's';
    } elseif ( ($mode & 0x4000) == 0x4000) { // directory
      $type = 'd';
    } elseif ( ($mode & 0xA000) == 0xA000) { // symbolic link
      $type = 'l';
    } elseif ( ($mode & 0x8000) == 0x8000) { // regular file
      $type = '-';
    } elseif ( ($mode & 0x6000) == 0x6000) { //bBlock special file
      $type = 'b';
    } elseif ( ($mode & 0x2000) == 0x2000) { // character special file
      $type = 'c';
    } elseif ( ($mode & 0x1000) == 0x1000) { // named pipe
      $type = 'p';
    } else { // unknown
      $type = '?';
    }

// determine permissions
    $owner['read']    = ($mode & 00400) ? 'r' : '-';
    $owner['write']   = ($mode & 00200) ? 'w' : '-';
    $owner['execute'] = ($mode & 00100) ? 'x' : '-';
    $group['read']    = ($mode & 00040) ? 'r' : '-';
    $group['write']   = ($mode & 00020) ? 'w' : '-';
    $group['execute'] = ($mode & 00010) ? 'x' : '-';
    $world['read']    = ($mode & 00004) ? 'r' : '-';
    $world['write']   = ($mode & 00002) ? 'w' : '-';
    $world['execute'] = ($mode & 00001) ? 'x' : '-';

// adjust for SUID, SGID and sticky bit
    if ($mode & 0x800 ) $owner['execute'] = ($owner['execute'] == 'x') ? 's' : 'S';
    if ($mode & 0x400 ) $group['execute'] = ($group['execute'] == 'x') ? 's' : 'S';
    if ($mode & 0x200 ) $world['execute'] = ($world['execute'] == 'x') ? 't' : 'T';

    return $type .
           $owner['read'] . $owner['write'] . $owner['execute'] .
           $group['read'] . $group['write'] . $group['execute'] .
           $world['read'] . $world['write'] . $world['execute'];
  }

  function tep_remove($source) {
    global $messageStack, $tep_remove_error;

    if (isset($tep_remove_error)) $tep_remove_error = false;

    if (is_dir($source)) {
      $dir = dir($source);
      while ($file = $dir->read()) {
        if ( ($file != '.') && ($file != '..') ) {
          if (tep_is_writable($source . '/' . $file)) {
            tep_remove($source . '/' . $file);
          } else {
            $messageStack->add(sprintf(ERROR_FILE_NOT_REMOVEABLE, $source . '/' . $file), 'error');
            $tep_remove_error = true;
          }
        }
      }
      $dir->close();

      if (tep_is_writable($source)) {
        rmdir($source);
      } else {
        $messageStack->add(sprintf(ERROR_DIRECTORY_NOT_REMOVEABLE, $source), 'error');
        $tep_remove_error = true;
      }
    } else {
      if (tep_is_writable($source)) {
        unlink($source);
      } else {
        $messageStack->add(sprintf(ERROR_FILE_NOT_REMOVEABLE, $source), 'error');
        $tep_remove_error = true;
      }
    }
  }

////
// Output the tax percentage with optional padded decimals
  function tep_display_tax_value($value, $padding = TAX_DECIMAL_PLACES) {
    if (strpos($value, '.')) {
      $loop = true;
      while ($loop) {
        if (substr($value, -1) == '0') {
          $value = substr($value, 0, -1);
        } else {
          $loop = false;
          if (substr($value, -1) == '.') {
            $value = substr($value, 0, -1);
          }
        }
      }
    }

    if ($padding > 0) {
      if ($decimal_pos = strpos($value, '.')) {
        $decimals = strlen(substr($value, ($decimal_pos+1)));
        for ($i=$decimals; $i<$padding; $i++) {
          $value .= '0';
        }
      } else {
        $value .= '.';
        for ($i=0; $i<$padding; $i++) {
          $value .= '0';
        }
      }
    }

    return $value;
  }

  function tep_mail($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address, $headers = '', $mailer = '') {
    if (SEND_EMAILS != 'true') return false;

    // Instantiate a new mail object
    $message = new email(array('X-Mailer: osCommerce'));

    // Build the text version
    $text = strip_tags($email_text);
    if (EMAIL_USE_HTML == 'true') {
      $message->add_html($email_text, $text);
    } else {
      $message->add_text($text);
    }

    // Send message
    $message->build_message();
    $message->send($to_name, $to_email_address, $from_email_name, $from_email_address, $email_subject, $headers, $mailer);
  }

  function tep_get_tax_class_title($tax_class_id) {
    if ($tax_class_id == '0') {
      return TEXT_NONE;
    } else {
      $classes_query = tep_db_query("select tax_class_title from " . TABLE_TAX_CLASS . " where tax_class_id = '" . (int)$tax_class_id . "'");
      $classes = tep_db_fetch_array($classes_query);

      return $classes['tax_class_title'];
    }
  }

  function tep_banner_image_extension() {
    if (function_exists('imagetypes')) {
      if (imagetypes() & IMG_PNG) {
        return 'png';
      } elseif (imagetypes() & IMG_JPG) {
        return 'jpg';
      } elseif (imagetypes() & IMG_GIF) {
        return 'gif';
      }
    } elseif (function_exists('imagecreatefrompng') && function_exists('imagepng')) {
      return 'png';
    } elseif (function_exists('imagecreatefromjpeg') && function_exists('imagejpeg')) {
      return 'jpg';
    } elseif (function_exists('imagecreatefromgif') && function_exists('imagegif')) {
      return 'gif';
    }

    return false;
  }

////
// Wrapper function for round() for php3 compatibility
  function tep_round($value, $precision) {
    return round($value, $precision);
  }

////
// Add tax to a products price
  function tep_add_tax($price, $tax, $override = false) {
    if ( ( (DISPLAY_PRICE_WITH_TAX == 'true') || ($override == true) ) && ($tax > 0) ) {
      return $price + tep_calculate_tax($price, $tax);
    } else {
      return $price;
    }
  }

// Calculates Tax rounding the result
  function tep_calculate_tax($price, $tax) {
    return $price * $tax / 100;
  }

////
// Returns the tax rate for a zone / class
// TABLES: tax_rates, zones_to_geo_zones
  function tep_get_tax_rate($class_id, $country_id = -1, $zone_id = -1) {
    global $customer_zone_id, $customer_country_id;

    if ( ($country_id == -1) && ($zone_id == -1) ) {
      if (!tep_session_is_registered('customer_id')) {
        $country_id = STORE_COUNTRY;
        $zone_id = STORE_ZONE;
      } else {
        $country_id = $customer_country_id;
        $zone_id = $customer_zone_id;
      }
    }

    $tax_query = tep_db_query("select SUM(tax_rate) as tax_rate from " . TABLE_TAX_RATES . " tr left join " . TABLE_ZONES_TO_GEO_ZONES . " za ON tr.tax_zone_id = za.geo_zone_id left join " . TABLE_GEO_ZONES . " tz ON tz.geo_zone_id = tr.tax_zone_id WHERE (za.zone_country_id IS NULL OR za.zone_country_id = '0' OR za.zone_country_id = '" . (int)$country_id . "') AND (za.zone_id IS NULL OR za.zone_id = '0' OR za.zone_id = '" . (int)$zone_id . "') AND tr.tax_class_id = '" . (int)$class_id . "' GROUP BY tr.tax_priority");
    if (tep_db_num_rows($tax_query)) {
      $tax_multiplier = 0;
      while ($tax = tep_db_fetch_array($tax_query)) {
        $tax_multiplier += $tax['tax_rate'];
      }
      return $tax_multiplier;
    } else {
      return 0;
    }
  }

////
// Returns the tax rate for a tax class
// TABLES: tax_rates

  function tep_get_tax_rate_value($class_id) {
   return tep_get_tax_rate($class_id, -1, -1);
  }


  function tep_call_function($function, $parameter, $object = '') {
    if ($object == '') {
      return call_user_func($function, $parameter);
    } else {
      return call_user_func(array($object, $function), $parameter);
    }
  }

  function tep_get_zone_class_title($zone_class_id) {
    if ($zone_class_id == '0') {
      return TEXT_NONE;
    } else {
      $classes_query = tep_db_query("select geo_zone_name from " . TABLE_GEO_ZONES . " where geo_zone_id = '" . (int)$zone_class_id . "'");
      $classes = tep_db_fetch_array($classes_query);

      return $classes['geo_zone_name'];
    }
  }

  function tep_cfg_pull_down_zone_classes($zone_class_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $zone_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $zone_class_query = tep_db_query("select geo_zone_id, geo_zone_name from " . TABLE_GEO_ZONES . " order by geo_zone_name");
    while ($zone_class = tep_db_fetch_array($zone_class_query)) {
      $zone_class_array[] = array('id' => $zone_class['geo_zone_id'],
                                  'text' => $zone_class['geo_zone_name']);
    }

    return tep_draw_pull_down_menu($name, $zone_class_array, $zone_class_id);
  }

  function tep_cfg_pull_down_order_statuses($order_status_id, $key = '') {
    global $languages_id;

    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $statuses_array = array(array('id' => '0', 'text' => TEXT_DEFAULT));
    $statuses_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int)$languages_id . "' order by orders_status_name");
    while ($statuses = tep_db_fetch_array($statuses_query)) {
      $statuses_array[] = array('id' => $statuses['orders_status_id'],
                                'text' => $statuses['orders_status_name']);
    }

    return tep_draw_pull_down_menu($name, $statuses_array, $order_status_id);
  }

  function tep_get_order_status_name($order_status_id, $language_id = '') {
    global $languages_id;

    if ($order_status_id < 1) return TEXT_DEFAULT;

    if (!is_numeric($language_id)) $language_id = $languages_id;

    $status_query = tep_db_query("select orders_status_name from " . TABLE_ORDERS_STATUS . " where orders_status_id = '" . (int)$order_status_id . "' and language_id = '" . (int)$language_id . "'");
    $status = tep_db_fetch_array($status_query);

    return $status['orders_status_name'];
  }

////
// Return a random value
  function tep_rand($min = null, $max = null) {
    static $seeded;

    if (!isset($seeded)) {
      $seeded = true;

      if ( (PHP_VERSION < '4.2.0') ) {
        mt_srand((double)microtime()*1000000);
      }
    }

    if (isset($min) && isset($max)) {
      if ($min >= $max) {
        return $min;
      } else {
        return mt_rand($min, $max);
      }
    } else {
      return mt_rand();
    }
  }

// nl2br() prior PHP 4.2.0 did not convert linefeeds on all OSs (it only converted \n)
  function tep_convert_linefeeds($from, $to, $string) {
    if ((PHP_VERSION < "4.0.5") && is_array($from)) {
      return preg_replace('/(' . implode('|', $from) . ')/', $to, $string);
    } else {
      return str_replace($from, $to, $string);
    }
  }

  function tep_string_to_int($string) {
    return (int)$string;
  }

////
// Parse and secure the cPath parameter values
  function tep_parse_category_path($cPath) {
// make sure the category IDs are integers
    $cPath_array = array_map('tep_string_to_int', explode('_', $cPath));

// make sure no duplicate category IDs exist which could lock the server in a loop
    $tmp_array = array();
    $n = sizeof($cPath_array);
    for ($i=0; $i<$n; $i++) {
      if (!in_array($cPath_array[$i], $tmp_array)) {
        $tmp_array[] = $cPath_array[$i];
      }
    }

    return $tmp_array;
  }

  function tep_validate_ip_address($ip_address) {
    if (function_exists('filter_var') && defined('FILTER_VALIDATE_IP')) {
      return filter_var($ip_address, FILTER_VALIDATE_IP, array('flags' => FILTER_FLAG_IPV4));
    }

    if (preg_match('/^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/', $ip_address)) {
      $parts = explode('.', $ip_address);

      foreach ($parts as $ip_parts) {
        if ( (intval($ip_parts) > 255) || (intval($ip_parts) < 0) ) {
          return false; // number is not within 0-255
        }
      }

      return true;
    }

    return false;
  }

  function tep_get_ip_address() {
    global $HTTP_SERVER_VARS;

    $ip_address = null;
    $ip_addresses = array();

    if (isset($HTTP_SERVER_VARS['HTTP_X_FORWARDED_FOR']) && !empty($HTTP_SERVER_VARS['HTTP_X_FORWARDED_FOR'])) {
      foreach ( array_reverse(explode(',', $HTTP_SERVER_VARS['HTTP_X_FORWARDED_FOR'])) as $x_ip ) {
        $x_ip = trim($x_ip);

        if (tep_validate_ip_address($x_ip)) {
          $ip_addresses[] = $x_ip;
        }
      }
    }

    if (isset($HTTP_SERVER_VARS['HTTP_CLIENT_IP']) && !empty($HTTP_SERVER_VARS['HTTP_CLIENT_IP'])) {
      $ip_addresses[] = $HTTP_SERVER_VARS['HTTP_CLIENT_IP'];
    }

    if (isset($HTTP_SERVER_VARS['HTTP_X_CLUSTER_CLIENT_IP']) && !empty($HTTP_SERVER_VARS['HTTP_X_CLUSTER_CLIENT_IP'])) {
      $ip_addresses[] = $HTTP_SERVER_VARS['HTTP_X_CLUSTER_CLIENT_IP'];
    }

    if (isset($HTTP_SERVER_VARS['HTTP_PROXY_USER']) && !empty($HTTP_SERVER_VARS['HTTP_PROXY_USER'])) {
      $ip_addresses[] = $HTTP_SERVER_VARS['HTTP_PROXY_USER'];
    }

    $ip_addresses[] = $HTTP_SERVER_VARS['REMOTE_ADDR'];

    foreach ( $ip_addresses as $ip ) {
      if (!empty($ip) && tep_validate_ip_address($ip)) {
        $ip_address = $ip;
        break;
      }
    }

    return $ip_address;
  }

////
// Wrapper function for is_writable() for Windows compatibility
  function tep_is_writable($file) {
    if (strtolower(substr(PHP_OS, 0, 3)) === 'win') {
      if (file_exists($file)) {
        $file = realpath($file);
        if (is_dir($file)) {
          $result = @tempnam($file, 'osc');
          if (is_string($result) && file_exists($result)) {
            unlink($result);
            return (strpos($result, $file) === 0) ? true : false;
          }
        } else {
          $handle = @fopen($file, 'r+');
          if (is_resource($handle)) {
            fclose($handle);
            return true;
          }
        }
      } else{
        $dir = dirname($file);
        if (file_exists($dir) && is_dir($dir) && tep_is_writable($dir)) {
          return true;
        }
      }
      return false;
    } else {
      return is_writable($file);
    }
  }

/**
  * ULTIMATE Seo Urls 5 PRO by FWR Media
  * Reset the various cache systems
  * @param string $action
  */
  function tep_reset_cache_data_usu5( $action = false ) {
    if ( $action == 'reset' ) {
      $usu5_path = realpath( dirname( __FILE__ ) . '/../../../' ) . '/' . DIR_WS_MODULES . 'ultimate_seo_urls5/';
      switch( USU5_CACHE_SYSTEM ) {
        case 'file':
          $path_to_cache = $usu5_path . 'cache_system/cache/';
          $it = new DirectoryIterator( $path_to_cache );
          while( $it->valid() ) {
            if ( !$it->isDot() && is_readable( $path_to_cache . $it->getFilename() ) && ( substr( $it->getFilename(), -6 ) == '.cache' ) ) {
              @unlink( $path_to_cache . $it->getFilename() );
            }
            $it->next();
          }
          break;
        case 'mysql':
          tep_db_query( 'TRUNCATE TABLE `usu_cache`' );
          break;
        case 'memcache':
          if ( class_exists('Memcache') ){
            include $usu5_path . 'interfaces/cache_interface.php';
            include $usu5_path . 'cache_system/memcache.php';
            Memcache_Cache_Module::iAdmin()->initiate()
                                           ->flushOut();
          }
          break;
        case 'sqlite':
          include $usu5_path . 'interfaces/cache_interface.php';
          include $usu5_path . 'cache_system/sqlite.php';
          Sqlite_Cache_Module::admini()->gc();
          break;
      }
      tep_db_query( "UPDATE " . TABLE_CONFIGURATION . " SET configuration_value='false' WHERE configuration_key='USU5_RESET_CACHE'" );
    }
  } // end function

  function tep_array_merge($array1, $array2, $array3 = '') {
    if ($array3 == '') $array3 = array();
    if (function_exists('array_merge')) {
      $array_merged = array_merge($array1, $array2, $array3);
    } else {
      while (list($key, $val) = each($array1)) $array_merged[$key] = $val;
      while (list($key, $val) = each($array2)) $array_merged[$key] = $val;
      if (sizeof($array3) > 0) while (list($key, $val) = each($array3)) $array_merged[$key] = $val;
    }

    return (array) $array_merged;
  }

  // BOF DEFAULT_SHIPPING_METHOD
	function tep_get_available_shipping_method () {
	global $PHP_SELF, $language;
    $module_type = 'shipping';
    $module_directory = DIR_FS_CATALOG_MODULES . 'shipping/';
    $module_key = 'MODULE_SHIPPING_INSTALLED';
		$file_extension = substr($PHP_SELF, strrpos($PHP_SELF, '.'));
  	$directory_array = array();
  	if ($dir = @dir($module_directory)) {
    	while ($file = $dir->read()) {
      	if (!is_dir($module_directory . $file)) {
       		if (substr($file, strrpos($file, '.')) == $file_extension) {
          	$directory_array[] = $file;
        	}
      	}
    	}
    	sort($directory_array);
    	$dir->close();
  	}

		$installed_modules = array(array('id' => 'false', 'text' => 'false'));
		for ($i=0, $n=sizeof($directory_array); $i<$n; $i++) {
			$file = $directory_array[$i];

			include(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/' . $module_type . '/' . $file);
			include($module_directory . $file);

			$class = substr($file, 0, strrpos($file, '.'));
			if (tep_class_exists($class)) {
      	$module = new $class;
      	if ($module->check() > 0) {
          $installed_modules[] = array('id' => $module->code,
                                				'text' => $module->title);
      	}
			}

		}
	return $installed_modules;
	}

  function tep_cfg_pull_down_available_shipping_method() {

    return tep_draw_pull_down_menu('configuration_value', tep_get_available_shipping_method (), DEFAULT_SHIPPING_METHOD);
  }
// EOF DEFAULT_SHIPPING_METHOD

//////create a pull down for all payment installed payment methods for Order Editor configuration

  // Get list of all payment modules available
  function tep_cfg_pull_down_payment_methods() {
  global $language;
  $enabled_payment = array();
  $module_directory = DIR_FS_CATALOG_MODULES . 'payment/';
  $file_extension = '.php';

  if ($dir = @dir($module_directory)) {
    while ($file = $dir->read()) {
      if (!is_dir( $module_directory . $file)) {
        if (substr($file, strrpos($file, '.')) == $file_extension) {
          $directory_array[] = $file;
        }
      }
    }
    sort($directory_array);
    $dir->close();
  }

  // For each available payment module, check if enabled
  for ($i=0, $n=sizeof($directory_array); $i<$n; $i++) {
    $file = $directory_array[$i];

    include(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/payment/' . $file);
    include($module_directory . $file);

    $class = substr($file, 0, strrpos($file, '.'));
    if (tep_class_exists($class)) {
      $module = new $class;
      if ($module->check() > 0) {
        // If module enabled create array of titles
        $enabled_payment[] = array('id' => $module->title, 'text' => $module->title);

      }
   }
 }

    $enabled_payment[] = array('id' => 'Other', 'text' => 'Other');

                //draw the dropdown menu for payment methods and default to the order value
          return tep_draw_pull_down_menu('configuration_value', $enabled_payment, '', '');
                }


/////end payment method dropdown

// rmh referral
  function tep_get_sources_name($source_id, $customers_id) {

    if ($source_id == '9999') {
      $sources_query = tep_db_query("select sources_other_name as sources_name from " . TABLE_SOURCES_OTHER . " where customers_id = '" . (int)$customers_id . "'");
    } else {
      $sources_query = tep_db_query("select sources_name from " . TABLE_SOURCES . " where sources_id = '" . (int)$source_id . "'");
    }

    if (!tep_db_num_rows($sources_query)) {
      if ($source_id == '9999') {
        return TEXT_OTHER;
      } else {
        return TEXT_NONE;
      }
    } else {
      $sources = tep_db_fetch_array($sources_query);
      return $sources['sources_name'];
    }
  }

    function tep_get_express_sources_name($source_id, $order_id) {

      $sources_express_query = tep_db_query("select sources_name from " . TABLE_SOURCES . " where sources_id = '" . (int)$source_id . "'");

    if (!tep_db_num_rows($sources_express_query)) {
      if ($source_id == '9999') {
        return TEXT_OTHER;
      } else {
        return TEXT_NONE;
      }
    } else {
      $sources = tep_db_fetch_array($sources_express_query);
      return $sources['sources_name'];
    }
  }

  function tep_get_sources($sources_id = '') {
    $sources_array = array();
    if (tep_not_null($sources_id)) {
        $sources = tep_db_query("select sources_name from " . TABLE_SOURCES . " where sources_id = '" . (int)$sources_id . "'");
        $sources_values = tep_db_fetch_array($sources);
        $sources_array = array('sources_name' => $sources_values['sources_name']);
    } else {
      $sources = tep_db_query("select sources_id, sources_name from " . TABLE_SOURCES . " order by sources_name");
      while ($sources_values = tep_db_fetch_array($sources)) {
        $sources_array[] = array('sources_id' => $sources_values['sources_id'],
                                 'sources_name' => $sources_values['sources_name']);
      }
    }

    return $sources_array;
  }

  //// sort order ////
// Sets the sort order of a product
  function tep_set_product_sort_order($products_id, $sort_order) {
    return tep_db_query("update " . TABLE_PRODUCTS . " set products_sort_order = '" . $sort_order . "', products_last_modified = now() where products_id = '" . (int)$products_id . "'");
  }


  function tep_get_source_list($name, $show_other = false, $selected = '', $parameters = '') {
    $sources_array = array(array('id' => '', 'text' => 'All Sources'));
    $sources = tep_get_sources();

    for ($i=0, $n=sizeof($sources); $i<$n; $i++) {
      $sources_array[] = array('id' => $sources[$i]['sources_id'], 'text' => $sources[$i]['sources_name']);
    }

    if ($show_other == 'true') {
      $sources_array[] = array('id' => '9999', 'text' => PULL_DOWN_OTHER);
    }

    return tep_draw_pull_down_menu($name, $sources_array, $selected, $parameters);
  }

  function tep_get_category_description($category_id, $language_id) {
    $category_query = tep_db_query("select categories_description from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . $category_id . "' and language_id = '" . $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_description'];
  }

  function tep_get_category_read_more($category_id, $language_id) {
    $category_query = tep_db_query("select categories_read_more from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . $category_id . "' and language_id = '" . $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_read_more'];
  }  

  function tep_get_category_name_highlight($category_id, $language_id) {
    $category_query = tep_db_query("select categories_name_highlight from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . $category_id . "' and language_id = '" . $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_name_highlight'];
  }

  function tep_get_category_name_highlight_black($category_id, $language_id) {
    $category_query = tep_db_query("select categories_name_highlight_black from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . $category_id . "' and language_id = '" . $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_name_highlight_black'];
  }

  function tep_get_category_name_gap($category_id, $language_id) {
    $category_query = tep_db_query("select categories_name_gap from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . $category_id . "' and language_id = '" . $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_name_gap'];
  }

  function tep_get_category_desc_switch($category_id, $language_id) {
    $category_query = tep_db_query("select categories_desc_switch from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . $category_id . "' and language_id = '" . $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_desc_switch'];
  }

  function tep_html_noquote($string) {

	$string=str_replace('&#39;', '', $string);
    $string=str_replace("'", "", $string);
    $string=str_replace('"', '', $string);
    $string=preg_replace("/\\r\\n|\\n|\\r/", "<BR>", $string);

  return $string;

  }

//// sort order ////
// Sets the sort order of a product
  function tep_set_product_site_select_cg($products_id, $site_visability) {
    return tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_cg = '" . $site_visability . "' where products_id = '" . (int)$products_id . "'");
  }

  function tep_set_product_site_select_ho($products_id, $site_visability) {
    return tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_ho = '" . $site_visability . "' where products_id = '" . (int)$products_id . "'");
  }

  function tep_set_product_site_select_hs($products_id, $site_visability) {
    return tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_hs = '" . $site_visability . "' where products_id = '" . (int)$products_id . "'");
  }

  function tep_set_product_site_select_wm($products_id, $site_visability) {
    return tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_wm = '" . $site_visability . "' where products_id = '" . (int)$products_id . "'");
  }

  function tep_set_product_site_select_dc($products_id, $site_visability) {
    return tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_dc = '" . $site_visability . "' where products_id = '" . (int)$products_id . "'");
  }

  function tep_set_product_site_select_tt($products_id, $site_visability) {
    return tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_tt = '" . $site_visability . "' where products_id = '" . (int)$products_id . "'");
  }

// master site set categories

  function tep_set_category_site_select_cg($categories_id, $site_visability) {
	  $tree = tep_get_category_tree($categories_id, '', '', '', true);
	  $tree[] = (int)$categories_id;

	  for ($i=1; $i<sizeof($tree); $i++) {
      tep_db_query("update " . TABLE_CATEGORIES_TO_STORES . " set store_cg = '" . $site_visability . "' where categories_id = '" . $tree[$i]['id'] . "'");
	  //$products_in_category_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . $tree[$i]['id'] . "'");
	   //while ($products_in_category = tep_db_fetch_array($products_in_category_query)) {
	   //  tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_cg = '" . $site_visability . "' where products_id = '" . $products_in_category['products_id'] . "'");
	   //}
	  }
  }

  function tep_set_category_site_select_ho($categories_id, $site_visability) {
	  $tree = tep_get_category_tree($categories_id, '', '', '', true);
	  $tree[] = (int)$categories_id;

	  for ($i=1; $i<sizeof($tree); $i++) {
      tep_db_query("update " . TABLE_CATEGORIES_TO_STORES . " set store_ho = '" . $site_visability . "' where categories_id = '" . $tree[$i]['id'] . "'");
	  //$products_in_category_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . $tree[$i]['id'] . "'");
	  // while ($products_in_category = tep_db_fetch_array($products_in_category_query)) {
	  //   tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_ho = '" . $site_visability . "' where products_id = '" . $products_in_category['products_id'] . "'");
	  // }
	  }
  }

  function tep_set_category_site_select_hs($categories_id, $site_visability) {
	  $tree = tep_get_category_tree($categories_id, '', '', '', true);
	  $tree[] = (int)$categories_id;

	  for ($i=1; $i<sizeof($tree); $i++) {
      tep_db_query("update " . TABLE_CATEGORIES_TO_STORES . " set store_hs = '" . $site_visability . "' where categories_id = '" . $tree[$i]['id'] . "'");
	  //$products_in_category_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . $tree[$i]['id'] . "'");
	  // while ($products_in_category = tep_db_fetch_array($products_in_category_query)) {
	  //   tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_hs = '" . $site_visability . "' where products_id = '" . $products_in_category['products_id'] . "'");
	  // }
	  }
  }

  function tep_set_category_site_select_wm($categories_id, $site_visability) {
	  $tree = tep_get_category_tree($categories_id, '', '', '', true);
	  $tree[] = (int)$categories_id;

	  for ($i=1; $i<sizeof($tree); $i++) {
      tep_db_query("update " . TABLE_CATEGORIES_TO_STORES . " set store_wm = '" . $site_visability . "' where categories_id = '" . $tree[$i]['id'] . "'");
	  //$products_in_category_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . $tree[$i]['id'] . "'");
	  // while ($products_in_category = tep_db_fetch_array($products_in_category_query)) {
	  //   tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_wm = '" . $site_visability . "' where products_id = '" . $products_in_category['products_id'] . "'");
	  // }
	  }
  }

  function tep_set_category_site_select_dc($categories_id, $site_visability) {

	  $tree = tep_get_category_tree($categories_id, '', '', '', true);
	  $tree[] = (int)$categories_id;

	  for ($i=1; $i<sizeof($tree); $i++) {
      tep_db_query("update " . TABLE_CATEGORIES_TO_STORES . " set store_dc = '" . $site_visability . "' where categories_id = '" . $tree[$i]['id'] . "'");
	  //$products_in_category_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . $tree[$i]['id'] . "'");
	  // while ($products_in_category = tep_db_fetch_array($products_in_category_query)) {
	  //   tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_dc = '" . $site_visability . "' where products_id = '" . $products_in_category['products_id'] . "'");
	  // }
	  }


  }

  function tep_set_category_site_select_tt($categories_id, $site_visability) {

	  $tree = tep_get_category_tree($categories_id, '', '', '', true);
	  $tree[] = (int)$categories_id;

	  for ($i=1; $i<sizeof($tree); $i++) {
      tep_db_query("update " . TABLE_CATEGORIES_TO_STORES . " set store_tt = '" . $site_visability . "' where categories_id = '" . $tree[$i]['id'] . "'");
	  //$products_in_category_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . $tree[$i]['id'] . "'");
	  // while ($products_in_category = tep_db_fetch_array($products_in_category_query)) {
	  //   tep_db_query("update " . TABLE_PRODUCTS_TO_STORES . " set store_tt = '" . $site_visability . "' where products_id = '" . $products_in_category['products_id'] . "'");
	  // }
	  }


  }


   function tep_count_active_site_products_in_category($category_id, $store_column_name) {

    $products_count = 0;

    // checks product is ticked and status is enabled - does not check if parent cat is ticked/enabled
	 $products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_STORES . " p2s, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2s.products_id and p2s." . $store_column_name . " = '1' and p.products_status = '1' and p.products_id = p2c.products_id and p2c.categories_id = '" . (int)$category_id . "'");

    $products = tep_db_fetch_array($products_query);
    $products_count += $products['total'];

    $child_categories_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$category_id . "'");

    if (tep_db_num_rows($child_categories_query)) {
      while ($child_categories = tep_db_fetch_array($child_categories_query)) {
        $products_count += tep_count_active_site_products_in_category($child_categories['categories_id'], $store_column_name);
      }
    }

    return $products_count;

   }

function tep_draw_selected_cat_pull_down($name, $parameters = '', $exclude = '', $cat_id = '') {
    global $currencies, $languages_id;

	if ($cat_id != '') {
      $cat_id_string = 'and p2c.categories_id = ' . $cat_id . ' ';
    } else {
	  $cat_id_string = '';
	}


    if ($exclude == '') {
      $exclude = array();
    }

    $select_string = '<select name="' . $name . '"';

    if ($parameters) {
      $select_string .= ' ' . $parameters;
    }

    $select_string .= '>';

    $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_price from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = pd.products_id and p.products_id = p2c.products_id " . $cat_id_string . " and pd.language_id = '" . (int)$languages_id . "' order by products_sort_order, products_name");
    while ($products = tep_db_fetch_array($products_query)) {
      if (!in_array($products['products_id'], $exclude)) {
        $select_string .= '<option value="' . $products['products_id'] . '">' . $prod_lot_id . $products['products_name'] . ' (' . $currencies->format($products['products_price']) . ')</option>';
      }
    }

    $select_string .= '</select>';

    return $select_string;
  }

      //Optional Related Products
        function tep_version_readonly($value){
          $version_text = '<br>Version ' . $value;
          return $version_text;
        }

 ////BEGIN: CCGV
 /// Creates a Coupon Code. lenght of the coupon set in admin
  function create_coupon_code($salt="somemumbojumbo", $length=CCGV_SECURITY_CODE_LENGTH) {
    $ccid = md5(uniqid("","salt"));
    $ccid .= md5(uniqid("","salt"));
    $ccid .= md5(uniqid("","salt"));
    $ccid .= md5(uniqid("","salt"));
    srand((double)microtime()*1000000); // seed the random number generator
    $random_start = @rand(0, (128-$length));
    $good_result = 0;
    while ($good_result == 0) {
      $id1 = substr($ccid, $random_start,$length);
      $query = tep_db_query("select coupon_code from " . TABLE_COUPONS . " where coupon_code = '" . $id1 . "'");
      if (tep_db_num_rows($query) == 0) $good_result = 1;
    }
    return $id1;
  }

  function create_voucher_code($salt="somemumbojumbo", $length=CCGV_SECURITY_CODE_LENGTH) {
    $ccid = md5(uniqid("","salt"));
    $ccid .= md5(uniqid("","salt"));
    $ccid .= md5(uniqid("","salt"));
    $ccid .= md5(uniqid("","salt"));
    srand((double)microtime()*1000000); // seed the random number generator
    $random_start = @rand(0, (128-$length));
    $good_result = 0;
    while ($good_result == 0) {
      $id1 = substr($ccid, $random_start,$length);
      $query = tep_db_query("select voucher_code from " . TABLE_VOUCHERS . " where voucher_code = '" . $id1 . "'");
      if (tep_db_num_rows($query) == 0) $good_result = 1;
    }
    return $id1;
  }
////
// Update the Customers GV account
  function tep_gv_account_update($customer_id, $gv_id) {
    $customer_gv_query = tep_db_query("select amount from " . TABLE_COUPON_GV_CUSTOMER . " where customer_id = '" . (int)$customer_id . "'");
    $coupon_gv_query = tep_db_query("select coupon_amount from " . TABLE_COUPONS . " where coupon_id = '" . $gv_id . "'");
    $coupon_gv = tep_db_fetch_array($coupon_gv_query);
    if (tep_db_num_rows($customer_gv_query) > 0) {
      $customer_gv = tep_db_fetch_array($customer_gv_query);
      $new_gv_amount = $customer_gv['amount'] + $coupon_gv['coupon_amount'];
 $gv_query = tep_db_query("update " . TABLE_COUPON_GV_CUSTOMER . " set amount = '" . $new_gv_amount . "' where customer_id = '" . (int)$customer_id . "'");
    } else {
      $gv_query = tep_db_query("insert into " . TABLE_COUPON_GV_CUSTOMER . " (customer_id, amount) values ('" . $customer_id . "', '" . $coupon_gv['coupon_amount'] . "')");
    }
  }
// END: CCGV

   function tep_set_product_visible_status($products_id) {

   // IF THIS SINGLE PRODUCT IS VISIBLE ON CG
   $products_query = tep_db_query("select p.products_id, p.products_status from " . TABLE_PRODUCTS . " p,  " . TABLE_PRODUCTS_TO_STORES . " p2s, " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_TO_STORES . " c2s, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int)$products_id . "' and c.categories_id = c2s.categories_id and c2s.store_cg = '1' and p.products_id = p2s.products_id and p2s.store_cg = '1' and c.categories_status != '0' and p.products_status not in(0,5,6) and p.products_id = p2c.products_id and p2c.categories_id = c.categories_id");

    if (tep_db_num_rows($products_query)) { // MAKE IT VISIBLE
		 tep_db_query("update " . TABLE_PRODUCTS . " set products_visible = '1' where products_id = '" . (int)$products_id . "'");
	} else {
		 tep_db_query("update " . TABLE_PRODUCTS . " set products_visible = '0' where products_id = '" . (int)$products_id . "'");
	}

   }

   function tep_set_category_visible_status($categories_id) {

	$cats[] = $categories_id; // catID as starting value
	// put cat-IDs of all cats nested in current branch into $cats array, go through all subbranches
	for($z=0;$z<count($cats);$z++) {
		$categorie_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$cats[$z] . "'");
		while ($categorie = tep_db_fetch_array($categorie_query)) {
			$cats[] = $categorie['categories_id'];
		}
		$cats=array_unique($cats); // sort out doubles
	}

	$products_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id in (".implode(', ', $cats).")");

	while ($products = tep_db_fetch_array($products_query)) {
	 tep_set_product_visible_status($products['products_id']);
	}

   }

   function tep_log_this($log_description, $products_id = 0) {
     tep_db_query("insert into logging (products_id, log_date_added, log_url, log_description) values ('" . $products_id . "', now(), '" . basename($_SERVER['REQUEST_URI']) . "', '" . $log_description . "')");
   }

   function tep_log_price($new_price = 0, $old_price = 0, $products_id = 0, $spec_new_price = 0, $spec_old_price = 0) {
     tep_db_query("insert into logging_prices (products_id, log_date_added, new_price, old_price, spec_new_price, spec_old_price) values ('" . $products_id . "', now(), '" . tep_db_input($new_price) . "', '" . tep_db_input($old_price) . "', '" . tep_db_input($spec_new_price) . "', '" . tep_db_input($spec_old_price) . "')");
   }

 //BOF Multiple dropdown
  function tep_cfg_pull_down_multiple_order_statuses($order_status_id, $key = '') {
    global $languages_id;

    $name = 'configuration_value[]';

    $statuses_array = array(array('id' => '0', 'text' => TEXT_DEFAULT));
    $statuses_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int)$languages_id . "' order by orders_status_name");
    while ($statuses = tep_db_fetch_array($statuses_query)) {
      $statuses_array[] = array('id' => $statuses['orders_status_id'],
                                'text' => $statuses['orders_status_name']);
    }

    return tep_draw_pull_down_menu($name, $statuses_array, $order_status_id, "multiple");
  }

  function tep_get_multiple_order_status_names($values, $language_id = '') {
    global $languages_id;
    $order_status_names = "";

    $order_statuses = unserialize($values);
    if(is_array($order_statuses) && 0 != count($order_statuses)) {
      if (!is_numeric($language_id)) $language_id = $languages_id;

      $status_query = tep_db_query("select orders_status_name from " . TABLE_ORDERS_STATUS . " where orders_status_id IN (" . implode($order_statuses, ',') . ") and language_id = '" . (int)$language_id . "'");
      while($status = tep_db_fetch_array($status_query)) {
        $order_status_names .= $status['orders_status_name'] . ', ';
      }
    }

    return trim($order_status_names, ', ');
  }


//EOF Multiple dropdown

  function tep_display_attribute_details($attribute_id, $attribute_option, $attribute_option_value, $attribute_prefix, $attribute_price, $attribute_qty, $attribute_currency, $attribute_currency_value) {
  global $currencies;

  $atribute_string = '';

  $show_all = true;
  $show_option = true;
  $show_option_value = true;
  $show_price = true;

  if($attribute_id == 0 || strpos($attribute_option, 'Text') !== false) {
   //$show_all = false;
  }

  if(strpos($attribute_option, 'Tobacco') !== false) {
   $show_option = false;
   $show_price = false;
  }

  if ($attribute_price == '0') {
   $show_price = false;
  }

  if($show_all) {

    $atribute_string .= '<i>';

   if($show_option) {
    $atribute_string .= ' - ' . $attribute_option;
   }

  if($show_option_value) {
   if($show_option) {
    $atribute_string .= ': ';
   }
    $atribute_string .= $attribute_option_value;
  }

   if ($show_price) {
    $atribute_string .= ' (' . $attribute_prefix . $currencies->format($attribute_price * $attribute_qty, true, $attribute_currency, $attribute_currency_value) . ')';
   }

    $atribute_string .= '</i>';
  }


  if($atribute_string) {
   return $atribute_string;
  } else {
   return false;
  }

  }


  function tep_set_review_average($products_id) {

	    $rp_query = tep_db_query("SELECT pop_products_id_slave FROM " . TABLE_PRODUCTS_RELATED_PRODUCTS . " WHERE pop_products_id_master = '" . $products_id . "'");

		$rp_ids[] = $products_id; // get current product id

		while ($rp_list = tep_db_fetch_array($rp_query)) {
			$rp_ids[] = $rp_list['pop_products_id_slave']; // get slaves
		}

		$rp_ids = array_unique($rp_ids); // sort out doubles

		$jp_ids = $rp_ids;

	  $reviews_query_average = tep_db_query("select (avg(reviews_rating)) as average_rating, count(*) as count from " . TABLE_REVIEWS . " where products_id in (".implode(', ', $rp_ids).") and reviews_status = '1'");
      $reviews_average = tep_db_fetch_array($reviews_query_average);

	  if($reviews_average['average_rating']) {
		$reveiws_stars = $reviews_average['average_rating'];
	    $reveiws_rating = number_format($reveiws_stars,0);
	    $final_rating = (($reveiws_rating * 1000) + $reviews_average['count']);
	  }

	$how_many = count($jp_ids);

	for ($i=0; $i<$how_many; $i++) {
	 if($final_rating) {
	   tep_db_query("update " . TABLE_PRODUCTS . " set review_average = '" . $final_rating . "' where products_id = '" . $jp_ids[$i] . "'");
	 } else {
	   tep_db_query("update " . TABLE_PRODUCTS . " set review_average = NULL where products_id = '" . $jp_ids[$i] . "'");
     }
	}



  }


  function tep_customer_free_gift($customers_id) {

	$time = strtotime("-6 month", time());
	$date1 = date("Y-m-d", $time);
	$date2 = date('Y-m-d');

	// $tobacco_query = tep_db_query("select c.customers_free_gift, count(DISTINCT o.orders_id) as ordertotal, sum(op.products_quantity * op.final_price) as ordersum from " . TABLE_CUSTOMERS . " c, " . TABLE_ORDERS_PRODUCTS . " op, " . TABLE_ORDERS . " o where c.customers_id = o.customers_id and o.orders_id = op.orders_id and DATE(date_purchased)>=DATE(\"$date1%\") AND DATE(date_purchased)<=DATE(\"$date2%\") and c.customers_id = '" . (int)$customers_id . "' group by c.customers_id having ordertotal > 4 order by ordersum DESC");

	$tobacco_query = tep_db_query("select c.customers_free_gift, count(DISTINCT o.orders_id) as ordertotal, sum(ot.value) as ordersum from " . TABLE_CUSTOMERS . " c, " . TABLE_ORDERS_TOTAL . " ot, " . TABLE_ORDERS . " o where c.customers_id = o.customers_id and o.orders_id = ot.orders_id and DATE(date_purchased)>=DATE(\"$date1%\") AND DATE(date_purchased)<=DATE(\"$date2%\") and c.customers_id = '" . (int)$customers_id . "' and ot.class = 'ot_total' group by c.customers_id having ordertotal > 4 order by ordersum DESC");

    $tobacco_total = tep_db_fetch_array($tobacco_query);

	if($tobacco_total['ordersum'] > 499) {
	 if($tobacco_total['customers_free_gift'] > 0) {
	  $customer_string = 'sent';
	 } else {
	  $customer_string = 'yes';
	 }
	} else {
	  $customer_string = 'no';
	}

	return $customer_string;

  }

      function tep_product_additional_info_array($products_id = 0, $options_group = 0, $output_blanks = true) {

	   // Additional Info
       $column_query = tep_db_query("select ai_column from " . TABLE_ADDITIONAL_INFO_OPTIONS . " where ai_options_group = '" . $options_group . "'");
       while ($column = tep_db_fetch_array($column_query)) {
	    $column_array[] = array('ai_column' => $column['ai_column']);
       }

	   if($options_group == 1) {
	    $option_table = TABLE_ADDITIONAL_INFO_CIGAR;
	   }
	   if($options_group == 2) {
	    $option_table = TABLE_ADDITIONAL_INFO_WHISKY;
	   }

	   if($products_id > 0 && $options_group > 0) {
	    $additional_info_query = tep_db_query("select * from " . $option_table . " where ai_products_id = '" . (int)$products_id . "'");
        $additional_info = tep_db_fetch_array($additional_info_query);
	   }

	   if($products_id > 0 && $additional_info['ai_products_id'] > 0) {
	    for ($i=0, $n=sizeof($column_array); $i<$n; $i++) {
	     if(tep_not_null($additional_info[$column_array[$i]['ai_column']])) {
		  $column_values_array[$column_array[$i]['ai_column']] = $additional_info[$column_array[$i]['ai_column']];
         } else {
		  $column_values_array[$column_array[$i]['ai_column']] = 'null';
		 }
		}
	   } else {
	    if($output_blanks) {
		 for ($i=0, $n=sizeof($column_array); $i<$n; $i++) {
		  $column_values_array[$column_array[$i]['ai_column']] = '';
		 }
	    } else {
		  $column_values_array = array();
		}
	   }

	   return $column_values_array;

	 }

  function tep_display_additional_info($products_id, $options_group = 0) {

	$additional_info_array = tep_product_additional_info_array($products_id, $options_group);

	$option_name_query = tep_db_query("select ai_options_id, ai_options_name, ai_column, ai_options_type, ai_options_measure from " . TABLE_ADDITIONAL_INFO_OPTIONS . " where ai_options_group = '" . $options_group . "' order by ai_options_name");
    while ($option_name = tep_db_fetch_array($option_name_query)) {

	if($option_name['ai_options_type'] > '1') {

	   if($option_name['ai_options_type'] == '3') {
	    $add_class = 'class="additional-info-text-field num-only"';
	   } elseif($option_name['ai_options_type'] == '4') {
	    $add_class = 'class="additional-info-text-field decimal-only"';
	   } else {
	    $add_class = 'class="additional-info-text-field"';
	   }

	   if(!tep_not_null($additional_info_array[$option_name['ai_column']]) || $additional_info_array[$option_name['ai_column']] == 'null') {
	    $text_field_value = '';
	   } else {
	    $text_field_value = $additional_info_array[$option_name['ai_column']];
	   }

	   $output_string .= '<tr>';
	   $output_string .=  '<td class="main">' . $option_name['ai_options_name'] . ': </td><td class="main">' . tep_draw_input_field($option_name['ai_column'], $text_field_value, $add_class) . ' </td><td class="main"> ' . $option_name['ai_options_measure'] . '</td>';
	   $output_string .=  '</tr>';

	} else {

	$value_name_query = tep_db_query("select ai_values_id, ai_options_id, ai_values_name from " . TABLE_ADDITIONAL_INFO_VALUES . " where ai_options_id = '" . $option_name['ai_options_id'] . "' order by ai_values_name");

	$value_array = array();
	$first_value = true;

	while ($value_name = tep_db_fetch_array($value_name_query)) {
      if($first_value) { $value_array[] = array('id' => '0', 'text' => 'Select'); $first_value = false; }
	  $value_array[] = array('id' => $value_name['ai_values_id'], 'text' => $value_name['ai_values_name']);
    }

	 if(!empty($value_array)) {
	  $output_string .=  '<tr>';
	  $output_string .=  '<td class="main">' . $option_name['ai_options_name'] . ': </td><td class="main">' . tep_draw_pull_down_menu($option_name['ai_column'], $value_array, $additional_info_array[$option_name['ai_column']], 'class="js-search-select" style="width: 100%;"') . '</td><td class="main"></td>';
	  $output_string .=  '</tr>';
	 }

	 }

	}

	 return $output_string;

  }

  function tep_check_additional_info_input($value_submitted, $ai_options_type) {

	if($ai_options_type == '1' || $ai_options_type == '3') { // INT
     if(is_numeric($value_submitted)) {
      if ($value_submitted == round($value_submitted)) {
       $output_value_submitted = $value_submitted;
      } else {
       $output_value_submitted = 'null'; // Number has decimals
      }
	 } else {
	  $output_value_submitted = 'null'; // Not a number
	 }
    }

	if($ai_options_type == '2') { // VARCHAR
	 $output_value_submitted = $value_submitted;
    }

	if($ai_options_type == '4') { // DECIMAL
	 if(is_numeric($value_submitted)) {
	  $output_value_submitted = $value_submitted;
	 } else {
	  $output_value_submitted = 'null'; // Its not a number
	 }
    }

	return $output_value_submitted;

  }

  function tep_decToFraction($float) {
    // 1/2, 1/4, 1/8, 1/16, 1/3 ,2/3, 3/4, 3/8, 5/8, 7/8, 3/16, 5/16, 7/16,
    // 9/16, 11/16, 13/16, 15/16
    $whole = floor ( $float );
    $decimal = $float - $whole;
    $leastCommonDenom = 48; // 16 * 3;
    $denominators = array (2, 3, 4, 8, 16, 24, 48 );
    $roundedDecimal = round ( $decimal * $leastCommonDenom ) / $leastCommonDenom;
    if ($roundedDecimal == 0)
        return $whole;
    if ($roundedDecimal == 1)
        return $whole + 1;
    foreach ( $denominators as $d ) {
        if ($roundedDecimal * $d == floor ( $roundedDecimal * $d )) {
            $denom = $d;
            break;
        }
    }
    return ($whole == 0 ? '' : $whole) . " " . ($roundedDecimal * $denom) . "/" . $denom;
  }

  function tep_bundle_string($products_id, $csv_export = false) {

	  // this product is a bundle so get contents data
      $bundle_query = tep_db_query("SELECT pb.* FROM " . TABLE_PRODUCTS_BUNDLES . " pb WHERE pb.bundle_id = '" . (int)$products_id . "' order by pb.subproduct_type");

    if($csv_export !== true){
	   $display_bundle_string = '<div style="padding: 5px;">';
    }

	  while ($bundle_contents = tep_db_fetch_array($bundle_query)) {

	 if($bundle_contents['subproduct_type'] == '1') {

	   $type_query = tep_db_query("select b.* from " . TABLE_PRODUCTS_BUNDLES_EXTRA . " b where b.products_bundles_extra_id = '" . $bundle_contents['subproduct_id'] . "'");
	   $type = tep_db_fetch_array($type_query);
     
     if($csv_export !== true){
	     $display_bundle_string .= '' . $bundle_contents['subproduct_qty'] . '&nbsp;x&nbsp;'  . $type['extra_name'] . '';
     } else {
       $display_bundle_string .= '' . $bundle_contents['subproduct_qty'] . ' x '  . $type['extra_name'] . '';
     }

	 } else {

	   $type_query = tep_db_query("SELECT p.products_price, p.products_cigar_length, p.products_cigar_ring_gauge, pd.products_name FROM " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS . " p WHERE p.products_id = '" . $bundle_contents['subproduct_id'] . "' and p.products_id = pd.products_id");
	   $type = tep_db_fetch_array($type_query);

     if($csv_export !== true){
	     $display_bundle_string .= '' . $bundle_contents['subproduct_qty'] . '&nbsp;x&nbsp;'  . $type['products_name'] . '';
     } else {
       $display_bundle_string .= '' . $bundle_contents['subproduct_qty'] . ' x '  . $type['products_name'] . '';
     }

	   if($bundle_contents['products_cigar_length'] > 0) {
		 $display_bundle_string .= ' (' . $type['products_cigar_ring_gauge'] . ' x ' . tep_decToFraction($type['products_cigar_length']) . '")';
	   }

	 }

   if($csv_export !== true){
	   $display_bundle_string .= '<br />';
   } else {
     $display_bundle_string .= "\n";
   }

      }

      if($csv_export !== true){
		   $display_bundle_string .= '</div>';
      }
	    

      return $display_bundle_string;

	   }

		function tep_image_check_delete($filename){

			// check main image
            $main_duplicate_image_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " where products_image = '" . tep_db_input($filename) . "'");
            $main_duplicate_image = tep_db_fetch_array($main_duplicate_image_query);

			$sub_duplicate_image_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_IMAGES . " where image = '" . tep_db_input($filename) . "'");
            $sub_duplicate_image = tep_db_fetch_array($sub_duplicate_image_query);

            $image_count_grand_total = ($main_duplicate_image['total'] + $sub_duplicate_image['total']);

		    if ($image_count_grand_total < 2) {
              if (file_exists(DIR_FS_CATALOG_IMAGES . $filename)) {
                @unlink(DIR_FS_CATALOG_IMAGES . $filename);
              }
			}

		}

    // Check log to see if a magazine should be sent to customer
function tep_magazine_check($customers_id, $orders_id, $orders_date, $express) {

  // temp disable 22/05/2025
  return false; // Don't send magazine

  // Validate customers_id and orders_id as integers
  if (!is_numeric($customers_id) || intval($customers_id) != $customers_id || $customers_id <= 0) {
      return false; // Invalid customers_id
  }
  
  if (!is_numeric($orders_id) || intval($orders_id) != $orders_id || $orders_id <= 0) {
      return false; // Invalid orders_id
  }

  // Validate orders_date as a valid date and time format
  if (!DateTime::createFromFormat('Y-m-d H:i:s', $orders_date)) {
      return false; // Invalid date format
  }

  if($express > 0){
    return false; // express checkout customers dont get a magazine
  }

  // Check if the customer is opted in
  $magazine_query = tep_db_query("
      SELECT customers_magazine 
      FROM " . TABLE_CUSTOMERS . " 
      WHERE customers_id = '" . (int)$customers_id . "'
  ");
  $magazine = tep_db_fetch_array($magazine_query);

  if($magazine['customers_magazine'] == '3'){
    return false;
  }

  // Calculate quarter start and end dates
  $currentYear = date('Y');
  $currentMonth = date('n');
  $quarterStartMonth = 3 * (floor(($currentMonth - 1) / 3)) + 1;
  $quarterEndMonth = $quarterStartMonth + 2;
  $quarterStartDate = "$currentYear-$quarterStartMonth-01";
  $quarterEndDate = date('Y-m-t', strtotime("$currentYear-$quarterEndMonth-01"));

  // temp override with fixed dates
  // $quarterStartDate = '2024-10-01'; // YYYY-MM-DD
  // $quarterEndDate = '2025-03-01'; // YYYY-MM-DD

  // Check if there is an entry for this customer_id in this quarter
  $mag_log_query = tep_db_query("
      SELECT customer_id, orders_id, orders_date_added 
      FROM magazine_log 
      WHERE customer_id = '" . (int)$customers_id . "' 
      AND orders_date_added BETWEEN '" . tep_db_input($quarterStartDate) . "' AND '" . tep_db_input($quarterEndDate) . "'
      LIMIT 1
  ");

  $mag_log = tep_db_fetch_array($mag_log_query);

  if ($mag_log) {
      // Check if the existing order ID matches
      if ($mag_log['orders_id'] == $orders_id) {
          return true; // Send magazine
      } else {
          return false; // Don't send magazine as it was previously sent this quarter
      }
  } else {
      // Insert a new log entry
      tep_db_query("
          INSERT INTO magazine_log (customer_id, orders_id, orders_date_added) 
          VALUES ('" . (int)$customers_id . "', '" . (int)$orders_id . "', '" . tep_db_input($orders_date) . "')
      ");
      return true; // Send magazine
  }
}

?>
