<?php
/*
  $Id: orders.php,v 1.112 2003/06/29 22:50:52 hpdl Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License

*/

require('includes/application_top.php');

require(DIR_WS_FUNCTIONS . 'reporting.php');

// OTF contrib begins
// Decode string encoded with htmlspecialchars()
function tep_decode_specialchars($string)
{
  $string = str_replace('&gt;', '>', $string);
  $string = str_replace('&lt;', '<', $string);
  $string = str_replace('&#039;', "'", $string);
  $string = str_replace('&quot;', "\"", $string);
  $string = str_replace('&amp;', '&', $string);

  return $string;
}

function tep_email_display_attribute_details($attribute_option, $attribute_option_value)
{

  $atribute_string = '';

  $show_all = true;
  $show_option = true;
  $show_option_value = true;
  $show_price = true;

  if (strpos($attribute_option, 'Text') !== false) {
    // $show_all = false;
  }

  if (strpos($attribute_option, 'Tobacco') !== false) {
    $show_option = false;
  }

  if ($show_all) {

    $atribute_string .= '<small><i>';

    if ($show_option) {
      $atribute_string .= ' - ' . $attribute_option;
    }

    if ($show_option_value) {
      if ($show_option) {
        $atribute_string .= ': ';
      }
      $atribute_string .= $attribute_option_value;
    }

    $atribute_string .= '</i></small>';
  }


  if ($atribute_string) {
    return $atribute_string;
  } else {
    return false;
  }
}
// OTF contrib ends

include('includes/modules/text_magic/TextMagicAPI.php');

require(DIR_FS_CATALOG . 'includes/classes/' . FILENAME_TRACKING_MODULE);
include(DIR_FS_CATALOG_LANGUAGES . $language . '/' . FILENAME_TRACKING_MODULE);

$tracking = new tracking_module();

define('MAX_DISPLAY_ORDER_SEARCH_RESULTS', 50);

if (isset($_GET['user'])) {
  if (!tep_session_is_registered('user_access')) {
    tep_session_register('user_access');
  }

  $user_access = $_GET['user'];
}

if (isset($_GET['label']) && $_GET['label'] == "success") { 
 $messageStack->add('Royal Mail Label Created Successfully', 'success');
}

// search query (oid, customer or company)
$search_query = null;
if (isset($_GET['q']) && $_GET['q'] != "") { // query is set in address

  $search_query = $_GET['q'];

  if (preg_match("/^\d+$/", $search_query)) { // oid

    // show given order
    tep_redirect(tep_href_link(FILENAME_ORDERS, 'oID=' . $search_query .
      '&action=edit'));
    exit;
  } else { // name (customer or company)

    $q_array = explode(' ', ($search_query));
    $q_customer = '(o.customers_name LIKE \'%' . $q_array[0] . '%\'';
    $q_email = '(o.customers_email_address LIKE \'%' . $q_array[0] . '%\'';
    $q_customer_postcode = '(o.customers_postcode LIKE \'%' . $q_array[0] . '%\'';
    $q_shipping_postcode = '(o.delivery_postcode LIKE \'%' . $q_array[0] . '%\'';
    $q_billing_postcode = '(o.billing_postcode LIKE \'%' . $q_array[0] . '%\'';

    // more than one search term

    for ($i = 1; $i < sizeof($q_array); $i++) {
      $q_customer .= ' AND o.customers_name LIKE \'%' . $q_array[$i] . '%\'';
      $q_email .= ' AND o.customers_email_address LIKE \'%' . $q_array[$i] . '%\'';
      $q_customer_postcode  .= ' AND o.customers_postcode LIKE \'%' . $q_array[$i] . '%\'';
      $q_shipping_postcode .= ' AND o.delivery_postcode LIKE \'%' . $q_array[$i] . '%\'';
      $q_billing_postcode .= ' AND o.billing_postcode LIKE \'%' . $q_array[$i] . '%\'';
    }
    $q_customer .= ')';
    $q_email .= ')';
    $q_customer_postcode .= ')';
    $q_shipping_postcode .= ')';
    $q_billing_postcode .= ')';

    $search_query = ' AND (' . $q_customer . ' OR ' . $q_email . ' OR ' . $q_customer_postcode . ' OR ' . $q_shipping_postcode . ' OR ' . $q_billing_postcode . ')';
  }
} // ends if ($search_query = $_GET['q'])

// ----------------- search query ends -----------------

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

include('../html/order_emails.php');

$type_statuses = array(array('id' => '1', 'text' => 'London Orders'), array('id' => '2', 'text' => 'Norfolk / Alcohol Orders'), array('id' => '99', 'text' => 'Norfolk (no subs)'), array('id' => '3', 'text' => 'Alcohol Only'), array('id' => '4', 'text' => 'C&amp;C Orders'), array('id' => '5', 'text' => 'Locker'), array('id' => '6', 'text' => 'Chester Orders'), array('id' => '7', 'text' => 'Liverpool Orders'), array('id' => '8', 'text' => 'Chester & Liverpool Orders'), array('id' => '66', 'text' => 'Lighters (Past 6 months)'));

$old_delivery_statuses = array(array('id' => '1', 'text' => 'Special Delivery'), array('id' => '2', 'text' => 'Special Saturday'), array('id' => '3', 'text' => 'Courier'));

$delivery_statuses = array(array('id' => '1', 'text' => 'Standard & Economy'), array('id' => '2', 'text' => 'Priority & Express'), array('id' => '3', 'text' => 'Next Day & Courier'), array('id' => '4', 'text' => 'Saturday Delivery'));

$payment_statuses = array(array('id' => '1', 'text' => 'Sage Pay'), array('id' => '2', 'text' => 'PayPal'), array('id' => '3', 'text' => 'Bank Transfer'), array('id' => '4', 'text' => 'Phone Order'));

$orders_statuses = array();
$orders_status_array = array();
$orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where select_flag = '1' and language_id = '" . (int)$languages_id . "'");
while ($orders_status = tep_db_fetch_array($orders_status_query)) {
  $orders_statuses[] = array(
    'id' => $orders_status['orders_status_id'],
    'text' => $orders_status['orders_status_name']
  );
  $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
}


$action = (isset($_GET['action']) ? $_GET['action'] : '');

// Start Batch Update Status v1.1 (with Comment buttons & e-mail notification)
if (isset($_POST['submit']) && !$restrict_user_order_updates) {
  if (($_POST['submit'] == BUS_SUBMIT) && (isset($_POST['new_status']))) { // Fair enough, let's update ;)
    $status = tep_db_prepare_input($_POST['new_status']);
    if ($status == '') { // New status not selected
      tep_redirect(tep_href_link(FILENAME_ORDERS), tep_get_all_get_params());
    }

    $email_generic_heading = 'Order Status Update';
    $email_generic_sub_heading = 'Your order has been updated, details below.';

    // load email templates
    include('../' . DIR_WS_LANGUAGES . 'english/email_generic_template.php');

    foreach ($_POST['update_oID'] as $this_orderID) {
      $order_updated = false;
      $check_status_query = tep_db_query("select customers_id, customers_name, customers_email_address, orders_status, date_purchased, payment_method from " . TABLE_ORDERS . " where orders_id = '" . (int)$this_orderID . "'");
      $check_status = tep_db_fetch_array($check_status_query);
      $comments = tep_db_prepare_input($_POST['comments']);
      if ($check_status['orders_status'] != $status) {

        tep_db_query("update " . TABLE_ORDERS . " set orders_status = '" . tep_db_input($status) . "', last_modified = now() where orders_id = '" . (int)$this_orderID . "'");

        // 8 courtesy email sent
        // 22 customer courtesy called
        // 24 customer courtesy done
        // 35 order review

        if ($status == 8 or $status == 22 or $status == 24 or $status == 35) {
          tep_db_query("update " . TABLE_CUSTOMERS_INFO . " set date_last_courtesy_email_order_id = '" . tep_db_input($status) . "', date_last_courtesy_email_sent = now() where customers_info_id = '" . (int)$check_status['customers_id'] . "'");
        }

        if ($status == 8) {
          $email_sender_name = 'C.Gars Ltd Customer Care';
          $email_sender_address = '<EMAIL>';
        } else {
          $email_sender_name = STORE_OWNER;
          $email_sender_address = STORE_OWNER_EMAIL_ADDRESS;
        }

        if (isset($_POST['free_gift'])) {
          $free_gift = tep_customer_free_gift((int)$check_status['customers_id']);
          if ($free_gift == 'yes') {
            tep_db_query("update " . TABLE_CUSTOMERS . " set customers_free_gift = '1' where customers_id = '" . (int)$check_status['customers_id'] . "'");
          }
        }

        $customer_notified = '0';
        if (isset($_POST['notify'])) {

          if ($status == 8) {

            $email_generic_body_text = '<h4 class="mb_xxs mte" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 16px;margin-bottom: 4px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Order #' . $this_orderID . '</h4><p class="small tm" style="font-family: Helvetica, Arial, sans-serif;font-size: 14px;line-height: 20px;color: #a7b1b6;mso-line-height-rule: exactly;display: block;margin-top: 0;margin-bottom: 0;">' . tep_date_long($check_status['date_purchased']) . '</p><h5 class="mt_xs mb_0" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 26px;margin-bottom: 0;padding: 0;font-weight: bold;font-size: 19px;line-height: 21px;">Dear ' . $check_status['customers_name'] . ',</h5><br/>';
          } else {

            if ($status == '16') {
              $order_status_label = 'Processing';
            } else {
              $order_status_label = $orders_status_array[$status];
            }

            $email_generic_body_text = '<h4 class="mb_xxs mte" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 16px;margin-bottom: 4px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Order #' . $this_orderID . '</h4><p style="font-family: Helvetica, Arial, sans-serif;font-size: 14px;line-height: 20px;color: #a7b1b6;mso-line-height-rule: exactly;display: block;margin-top: 0;margin-bottom: 0;">' . tep_date_long($check_status['date_purchased']) . '</p><br /><span style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 26px;font-weight: bold;font-size: 19px;margin-bottom: 0;padding: 0;line-height: 21px;">New status <span class="tp" style="font-weight:700;color: #35bec5;line-height: inherit;">' . $order_status_label . '</span></span><br/><br/><a href="' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $this_orderID, 'SSL') . '">' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $this_orderID, 'SSL') . '</a><br/><br/>';
          }

          $email_generic_body_text .= $comments . '';

          if ($status == 8) {
            $email_generic_body_text .= '<br/><br/>Best wishes<br/>The C.Gars Sales Team<br/>';
          }

          $email_text  = EMAIL_GENERIC_TEMPLATE_START;
          $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;

          if ($status != 8) {
           $email_text .= '<div style="text-align:center;">';
           $email_text .= '<div style="padding:10px;">'; // pad
          }

          $email_text .= $email_generic_body_text;  

          if ($status == 8) {
            $email_text .= EMAIL_GENERIC_TEMPLATE_TRUSTPILOT;
          } else {
            $email_text .= '</div>'; // pad
            $email_text .= EMAIL_GENERIC_TEMPLATE_CUSTOMERCARE_SIGNATURE;
          }

          if ($status != 8) {
            $email_text .= '</div>';
          }          

          $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
          $email_text .= EMAIL_GENERIC_TEMPLATE_END;

          tep_mail($check_status['customers_name'], $check_status['customers_email_address'], 'Order Review - C.Gars Ltd Order Number: ' . (int)$this_orderID, $email_text, $email_sender_name, $email_sender_address);

          $customer_notified = '1';
        }
        tep_db_query("insert into " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments) values ('" . (int)$this_orderID . "', '" . tep_db_input($status) . "', now(), '" . tep_db_input($customer_notified) . "', '" . tep_db_input($comments)  . "')");
        
        $dup_orders_status_history_id = tep_db_insert_id();
        
        tep_db_query("insert into orders_user_log (orders_status_history_id, orders_id, orders_status_id, admin_id, date_added) values ('" . tep_db_input($dup_orders_status_history_id) . "', '" . (int)$this_orderID . "', '" . tep_db_input($status) . "', '" . tep_db_input($admin['id']) . "', now() ) ");
        
        $order_updated = true;
      }
      if ($order_updated == true) {
        $messageStack->add("Order $this_orderID updated.", 'success');
      } else {
        $messageStack->add("Order $this_orderID not updated.", 'warning');
      }
    } // End foreach ID loop
  }
}

// End Batch Update Status v1.1 (with Comment buttons & e-mail notification)

include(DIR_WS_CLASSES . 'order.php');

if (tep_not_null($action) && !$restrict_user_order_updates) {
  switch ($action) {
    case 'update_delivery_notes':

      $oID = tep_db_prepare_input($_GET['oID']);
      $order_updated = false;

      if (isset($_POST['delivery_comments'])) {
        $delivery_comments = tep_db_prepare_input($_POST['delivery_comments']);
        tep_db_query("insert into orders_delivery_comments (orders_id, date_added, comments) values ('" . (int)$oID . "', now(), '" . tep_db_input($delivery_comments)  . "')");
        $order_updated = true;
      }

      if ($order_updated == true) {
        $messageStack->add_session(SUCCESS_ORDER_UPDATED, 'success');
      } else {
        $messageStack->add_session(WARNING_ORDER_NOT_UPDATED, 'warning');
      }

      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=edit'));

      break;
    case 'update_order':

      $oID = tep_db_prepare_input($_GET['oID']);
      $status = tep_db_prepare_input($_POST['status']);
      $comments = tep_db_prepare_input($_POST['comments']);
      $location_id = tep_db_prepare_input($_POST['location_id']);

      $track_id_failed = $tracking->validate_input(); // added tracking module
      $new_tracking_id = $tracking->insert_tracking_id();

      if ($status == 8) {
        $email_sender_name = 'C.Gars Ltd Customer Care';
        $email_sender_address = '<EMAIL>';
      } else {
        $email_sender_name = STORE_OWNER;
        $email_sender_address = STORE_OWNER_EMAIL_ADDRESS;
      }

      $check_status_query = tep_db_query("select customers_id, customers_name, customers_telephone, customers_email_address, orders_status, date_purchased, payment_method, delivery_country_id from " . TABLE_ORDERS . " where orders_id = '" . (int)$oID . "'");
      $check_status = tep_db_fetch_array($check_status_query);

      if ($check_status['orders_status'] != $status || tep_not_null($comments) || $location_id > 0 || $new_tracking_id) {

        // Order Status changes
        if ($check_status['orders_status'] != $status) { // 8 courtesy email sent // 22 customer courtesy called // 24 customer courtesy done // 35 order review

          tep_db_query("update " . TABLE_ORDERS . " set orders_status = '" . tep_db_input($status) . "', last_modified = now() where orders_id = '" . (int)$oID . "'");

          if ($status == 8 or $status == 22 or $status == 24 or $status == 35) {
            tep_db_query("update " . TABLE_CUSTOMERS_INFO . " set date_last_courtesy_email_order_id = '" . tep_db_input($status) . "', date_last_courtesy_email_sent = now() where customers_info_id = '" . (int)$check_status['customers_id'] . "'");
          }
        }

        // Email Customer
        if (isset($_POST['notify']) && ($_POST['notify'] == 'on')) { // Notify Customer via email
          $customer_notified = '1';
        } else {
          $customer_notified = '0';
        }

        // Update Order History

        tep_db_query("insert into " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, tracking_id, location_id) values ('" . (int)$oID . "', '" . tep_db_input($status) . "', now(), '" . tep_db_input($customer_notified) . "', '" . tep_db_input($comments)  . "', '" . tep_db_input($new_tracking_id) . "', '" . tep_db_input($location_id) . "')");

        $dup_orders_status_history_id = tep_db_insert_id();

        tep_db_query("insert into orders_user_log (orders_status_history_id, orders_id, orders_status_id, admin_id, date_added) values ('" . tep_db_input($dup_orders_status_history_id) . "', '" . (int)$oID . "', '" . tep_db_input($status) . "', '" . tep_db_input($admin['id']) . "', now() ) ");

        // Start Building email for customer

        if (isset($_POST['notify']) && ($_POST['notify'] == 'on')) { // Notify Customer via email

          $email_generic_heading = 'Order Status Update';
          $email_generic_sub_heading = 'Your order has been updated, details below.';

          // load email templates
          include('../' . DIR_WS_LANGUAGES . 'english/email_generic_template.php');
          include('../' . DIR_WS_LANGUAGES . 'english/email_order_template.php');

          if ($status == 8) {

            $email_generic_body_text = '<h4 class="mb_xxs mte" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 16px;margin-bottom: 4px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Order #' . $oID . '</h4><p style="font-family: Helvetica, Arial, sans-serif;font-size: 14px;line-height: 20px;color: #a7b1b6;mso-line-height-rule: exactly;display: block;margin-top: 0;margin-bottom: 0;">' . tep_date_long($check_status['date_purchased']) . '</p><h5 class="mt_xs mb_0" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 26px;margin-bottom: 0;padding: 0;font-weight: bold;font-size: 19px;line-height: 21px;">Dear ' . $check_status['customers_name'] . ',</h5>';
          } else {

            $email_generic_body_text = '<h4 class="mb_xxs mte" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 16px;margin-bottom: 4px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Order #' . $oID . '</h4><p class="small tm" style="font-family: Helvetica, Arial, sans-serif;font-size: 14px;line-height: 20px;color: #a7b1b6;mso-line-height-rule: exactly;display: block;margin-top: 0;margin-bottom: 0;">' . tep_date_long($check_status['date_purchased']) . '</p>';

            if ($status == '16') {
              $order_status_label = 'Processing';
            } else {
              $order_status_label = $orders_status_array[$status];
            }

            $email_generic_body_text .= '<h5 class="mt_xs mb_0" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 26px;margin-bottom: 0;padding: 0;font-weight: bold;font-size: 19px;line-height: 21px;">New status <span class="tp" style="color: #35bec5;line-height: inherit;">' . $order_status_label . '</span></h5>';

            $email_generic_body_text .= '<br/><a href="' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $oID, 'SSL') . '">' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $oID, 'SSL') . '</a><br/>';
          }

          //// Start Add Tracking Info + Product Details if available...

          if ((isset($_POST['tracking_per_product']) && ($_POST['tracking_per_product'] == 'on')) || ($status == 8)) {

            $order = new order($oID);

            // Loop through tracking codes
            $orders_courier_query = tep_db_query("select location_id from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . (int)$oID . "' and tracking_id != '' order by location_id asc");

            $tracking_location_id = 999;

            while ($orders_courier = tep_db_fetch_array($orders_courier_query)) {

              $tracking_string = '';

              if ($orders_courier['location_id'] != $tracking_location_id) {

                // Get ALL tracking IDs for this group / location...
                $group_track_query = tep_db_query("select tracking_id from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . (int)$oID . "' and tracking_id != '' and location_id = '" . $orders_courier['location_id'] . "' group by tracking_id");
                $track_num = 0;
                
                while ($group_track = tep_db_fetch_array($group_track_query)) {
                  if ($track_num > 0) {
                    $tracking_string .= ', ';
                  }
                  $tracking_string .= $tracking->display_tracking_link($group_track['tracking_id']);
                  $track_num++;
                }

                // Start Output Tracking code(s)

                $tracking_product_info .= '';

                $tracking_product_info .= '<div class="email_row" style="box-sizing: border-box;font-size: 0;display: block;width: 100%;vertical-align: top;margin: 0 auto;text-align: center;clear: both;line-height: inherit;min-width: 0 !important;max-width: 600px !important;">';

                $tracking_product_info .= '<div class="col_6" style="box-sizing: border-box;font-size: 0;display: inline-block;width: 100%;vertical-align: top;max-width: 600px;line-height: inherit;min-width: 0 !important;"><table class="column" width="100%" border="0" cellspacing="0" cellpadding="0" style="box-sizing: border-box;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;width: 100%;min-width: 100%;"><tbody><tr><td class="column_cell px pt hdr_menu tr" style="box-sizing: border-box;vertical-align: top;width: 100%;min-width: 100%;font-family: Helvetica, Arial, sans-serif;font-size: 16px;line-height: 23px;color: #333;mso-line-height-rule: exactly;">Tracking Number: ' . $tracking_string . '</td></tr></tbody></table></div>';

                $tracking_product_info .= '</div>';

                $tracking_product_info .= '';

                // End Output Tracking code(s)

              }  // end if($orders_courier['location_id'] != $tracking_location_id) {

              $tracking_location_id = $orders_courier['location_id'];
            } // end tacking id loop

          } // end if (isset($_POST['tracking_per_product']) && ($_POST['tracking_per_product'] == 'on')) {


          //// End Add Tracking Info + Product Details if available...


          if (isset($_POST['notify_comments']) && ($_POST['notify_comments'] == 'on') && tep_not_null($comments)) {
            $email_generic_body_text .= '<br/>' . $comments . '<br/>';
          }

          if ($tracking_product_info && $status != 8) {
            $email_generic_body_text .= '<br/>If you would like to track your parcel online, you can do this by either logging in via our website and viewing your order history or alternatively follow the link(s) below:<br/>';
          }

          if ($status == 8) {
            $email_generic_body_text .= '<br/>Best wishes<br/>The C.Gars Sales Team<br/>';
          }

          $email_text  = EMAIL_GENERIC_TEMPLATE_START;
          $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;

          if ($status != 8) {
            $email_text .= '<div style="text-align:center;">';
            $email_text .= '<div style="padding:10px;">'; // padding
          }

          $email_text .= $email_generic_body_text;

          if ($tracking_product_info && $status != 8) {
            $email_text .= '</div>'; // padding
            $email_text .= EMAIL_ORDER_TEMPLATE_GREY_NOTICE_ONE;
            $email_text .= $tracking_product_info;
            $email_text .= EMAIL_ORDER_TEMPLATE_GREY_NOTICE_TWO;
            $email_text .= '<br />';
          }

          if ($status == 8) {
            $email_text .= EMAIL_GENERIC_TEMPLATE_TRUSTPILOT;
          } else {
            $email_text .= EMAIL_GENERIC_TEMPLATE_CUSTOMERCARE_SIGNATURE;
            $email_text .= '</div>'; // align left
          }

          $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
          $email_text .= EMAIL_GENERIC_TEMPLATE_END;

          // Email Customer

          tep_mail($check_status['customers_name'], $check_status['customers_email_address'], 'Order Review - C.Gars Ltd Order Number: ' . $oID, $email_text, $email_sender_name, $email_sender_address);

        }

        // End Build email for customer


        $order_update_message .= SUCCESS_ORDER_UPDATED;
        $messageStack->add_session($order_update_message, 'success');
      } else {
        $order_update_message .= WARNING_ORDER_NOT_UPDATED;
        $messageStack->add_session($order_update_message, 'warning');
      }

      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=edit'));
      break;
    case 'locker':

      if (isset($_GET['oID']) && isset($_GET['pID']) && isset($_GET['cID']) && isset($_GET['qID'])) {

        $oID_locker = tep_db_prepare_input($_GET['oID']);
        $pID_locker = tep_db_prepare_input($_GET['pID']);
        $cID_locker = tep_db_prepare_input($_GET['cID']);
        $qID_locker = tep_db_prepare_input($_GET['qID']);

        $product_query = tep_db_query("select products_quantity, products_model, products_image, products_subimage1, products_subimage2, products_subimage3, products_subimage4, products_subimage5, products_subimage6, products_subimage7, products_price, products_date_available, products_weight, products_mpn, products_tax_class_id, manufacturers_id, products_geo_zones, products_free_shipping, products_cigar from " . TABLE_PRODUCTS . " where products_id = '" . tep_db_input((int)$pID_locker) . "'");

        $product = tep_db_fetch_array($product_query);

        tep_db_query("insert into " . TABLE_PRODUCTS . " (products_quantity, products_model, products_image, products_subimage1, products_subimage2, products_subimage3, products_subimage4, products_subimage5, products_subimage6, products_subimage7, products_price, products_date_added, products_date_available, products_weight, products_mpn, products_status, products_tax_class_id, manufacturers_id, products_geo_zones, products_free_shipping, products_cigar) values ('" . tep_db_input($product['products_quantity']) . "', '" . tep_db_input($product['products_model']) . "', '" . tep_db_input($product['products_image']) . "', '" . tep_db_input($product['products_subimage1']) . "', '" . tep_db_input($product['products_subimage2']) . "', '" . tep_db_input($product['products_subimage3']) . "', '" . tep_db_input($product['products_subimage4']) . "', '" . tep_db_input($product['products_subimage5']) . "', '" . tep_db_input($product['products_subimage6']) . "', '" . tep_db_input($product['products_subimage7']) . "', '0.0000',  now(), '" . tep_db_input($product['products_date_available']) . "', '" . tep_db_input($product['products_weight']) . "', '" . tep_db_input($product['products_mpn']) . "', '0', '" . (int)$product['products_tax_class_id'] . "', '" . (int)$product['manufacturers_id'] . "', '" . (int)$product['products_geo_zones'] . "', '" . (int)$product['products_free_shipping'] . "', '" . (int)$product['products_cigar'] . "')");

        $dup_products_id = tep_db_insert_id();

        $description_query = tep_db_query("select language_id, products_name, products_description, products_tasting_notes, products_taste_test, products_head_title_tag, products_head_desc_tag, products_head_keywords_tag, products_url from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . tep_db_input((int)$pID_locker) . "'");

        while ($description = tep_db_fetch_array($description_query)) {

          tep_db_query("insert into " . TABLE_PRODUCTS_DESCRIPTION . " (products_id, language_id, products_name, products_description, products_tasting_notes, products_taste_test, products_head_title_tag, products_head_desc_tag, products_head_keywords_tag, products_url, products_viewed) values ('" . (int)$dup_products_id . "', '" . (int)$description['language_id'] . "', '" . tep_db_input($description['products_name']) . "', '" . tep_db_input($description['products_description']) . "', '" . tep_db_input($description['products_tasting_notes']) . "', '" . tep_db_input($description['products_taste_test']) . "', '" . tep_db_input($description['products_head_title_tag']) . "', '" . tep_db_input($description['products_head_desc_tag']) . "', '" . tep_db_input($description['products_head_keywords_tag']) . "', '" . tep_db_input($description['products_url']) . "', '0')");
        }

        tep_db_query("insert into " . TABLE_PRODUCTS_TO_CUSTOMER_LOCKER . " (products_id, customers_id, add_orders_id, original_products_id, final_price_per_unit, unit_quantity) values ('" . $dup_products_id . "', '" . tep_db_input($cID_locker) . "', '" . $oID_locker . "', '" . tep_db_input((int)$pID_locker) . "', '" . tep_db_input($product['products_price']) . "', '" . tep_db_input($qID_locker) . "')");

        tep_db_query("insert into " . TABLE_PRODUCTS_TO_STORES . " (products_id, store_cg) values ('" . (int)$dup_products_id . "', '0')"); // override CG default with zero
        tep_db_query("insert into " . TABLE_PRODUCTS_TO_CATEGORIES . " (products_id, categories_id) values ('" . $dup_products_id . "', '" . tep_db_input(1) . "')");

        $messageStack->add_session('Item added to locker', 'success');
      }

      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=edit'));

      break;
    case 'id_update':

      if ($_POST['verify_tick']) {
        foreach ($_POST['verify_tick'] as $tick_id => $verify_tick_value) {
          tep_db_query("update uploads set verified = '" . $verify_tick_value . "' where id = '" . $tick_id . "'");
        }
      }

      if ($_POST['delete_tick']) {
        foreach ($_POST['delete_tick'] as $tick_id => $delete_tick_value) {

          tep_db_query("update uploads set deleted = '" . $delete_tick_value . "' where id = '" . $tick_id . "'");

          $verified_query = tep_db_query("select name from uploads where id = '" . $tick_id . "'");
          $verified = tep_db_fetch_array($verified_query);

          unlink(DIR_FS_CATALOG . 'uploads/images/' . $verified['name']);
        }
      }

      $messageStack->add_session(SUCCESS_ORDER_UPDATED, 'success');

      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=edit'));

      break;
    case 'order_points_update':

      if (isset($_GET['oID']) && is_numeric($_POST['add_points_amount']) && ($_POST['add_points_customer_id'] > 0)) {
        tep_add_pending_points($_POST['add_points_customer_id'], $_GET['oID'], $_POST['add_points_amount']);
        $messageStack->add_session(SUCCESS_ORDER_UPDATED, 'success');
      }

      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=edit'));

      break;
    case 'order_cost_update':

      if ($_POST['products_cost_value']) {
        foreach ($_POST['products_cost_value'] as $id => $new_products_cost_value) {

          if ($_POST['products_cost_value_tick']) {
            foreach ($_POST['products_cost_value_tick'] as $tick_id => $new_products_cost_value_tick) {
              if (($tick_id == $id) && (tep_not_null($new_products_cost_value))) {
                $new_products_cost_value = str_replace(',', '', $new_products_cost_value);
                tep_db_query("update " . TABLE_PRODUCTS . " set products_trade_price = '" . tep_db_input($new_products_cost_value) . "' where products_id = '" . $id . "'");
              }
            }
          } // end tick

          if (($_GET['oID']) && (tep_not_null($new_products_cost_value))) {
            $oID = tep_db_prepare_input($_GET['oID']);
            $new_products_cost_value = str_replace(',', '', $new_products_cost_value);
            tep_db_query("update " . TABLE_ORDERS_PRODUCTS . " set orders_products_cg_cost = '" . tep_db_input($new_products_cost_value) . "' where orders_id = '" . $oID . "' and products_id = '" . $id . "'");
          }
        }
      }


      if ($_POST['products_attribute_cost_prefix']) {
        foreach ($_POST['products_attribute_cost_prefix'] as $id => $new_products_attribute_cost_prefix) {

          if ($_POST['products_attribute_cost_value_tick']) {
            foreach ($_POST['products_attribute_cost_value_tick'] as $tick_id => $new_products_attribute_cost_value_tick) {
              if (($tick_id == $id) && (tep_not_null($new_products_attribute_cost_value))) {
                tep_db_query("update " . TABLE_PRODUCTS_ATTRIBUTES . " set price_cost_prefix = '" . tep_db_input($new_products_attribute_cost_prefix) . "' where products_attributes_id = '" . $id . "'");
              }
            }
          } // end tick

          if (($_GET['oID']) && (tep_not_null($new_products_attribute_cost_prefix))) {
            $oID = tep_db_prepare_input($_GET['oID']);
            tep_db_query("update " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " set price_cost_prefix = '" . tep_db_input($new_products_attribute_cost_prefix) . "' where orders_id = '" . $oID . "' and products_attributes_id = '" . $id . "'");
          }
        }
      }

      if ($_POST['products_attribute_cost_value']) {
        foreach ($_POST['products_attribute_cost_value'] as $id => $new_products_attribute_cost_value) {

          if ($_POST['products_attribute_cost_value_tick']) {
            foreach ($_POST['products_attribute_cost_value_tick'] as $tick_id => $new_products_attribute_cost_value_tick) {
              if (($tick_id == $id) && (tep_not_null($new_products_attribute_cost_value))) {
                tep_db_query("update " . TABLE_PRODUCTS_ATTRIBUTES . " set options_values_price_cost = '" . tep_db_input($new_products_attribute_cost_value) . "' where products_attributes_id = '" . $id . "'");
              }
            }
          } // end tick

          if (($_GET['oID']) && (tep_not_null($new_products_attribute_cost_value))) {
            $oID = tep_db_prepare_input($_GET['oID']);
            tep_db_query("update " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " set options_values_price_cost = '" . tep_db_input($new_products_attribute_cost_value) . "' where orders_id = '" . $oID . "' and products_attributes_id = '" . $id . "'");
          }
        }
      }

      if ($_POST['orders_products_trade_price_discount']) {
        foreach ($_POST['orders_products_trade_price_discount'] as $id => $new_orders_products_trade_price_discount) {

          if (($_GET['oID']) && (tep_not_null($new_orders_products_trade_price_discount))) {
            $oID = tep_db_prepare_input($_GET['oID']);
            $new_orders_products_trade_price_discount = str_replace(',', '', $new_orders_products_trade_price_discount);
            tep_db_query("update " . TABLE_ORDERS_PRODUCTS . " set orders_products_trade_price_discount = '" . tep_db_input($new_orders_products_trade_price_discount) . "' where orders_id = '" . $oID . "' and products_id = '" . $id . "'");
          }
        }
      }


      $messageStack->add_session(SUCCESS_ORDER_UPDATED, 'success');

      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=edit'));
      break;
    case 'restockconfirm':
      $oID = tep_db_prepare_input($_GET['oID']);
      if(is_numeric($oID)){
        tep_restock_order($oID);
        $messageStack->add_session('Order Restocked Successfully', 'success');
      } else {
        $messageStack->add_session('Order NOT Restocked Successfully', 'warning');
      }
      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action'))));
      break;
    case 'deleteconfirm':
      $oID = tep_db_prepare_input($_GET['oID']);
      tep_remove_order($oID); // restock removed
      tep_redirect(tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action'))));
      break;
  }
}

if (($action == 'edit') && isset($_GET['oID'])) {
  $oID = tep_db_prepare_input($_GET['oID']);

  $orders_query = tep_db_query("select orders_id from " . TABLE_ORDERS . " where orders_id = '" . (int)$oID . "'");
  $order_exists = true;
  if (!tep_db_num_rows($orders_query)) {
    $order_exists = false;
    $messageStack->add(sprintf(ERROR_ORDER_DOES_NOT_EXIST, $oID), 'error');
  }
}

if ($_GET["date1"] == "" or $_GET["date2"] == "") {
  //$date1 = date('Y-m-d'); #2003-09-07%
  //$date2 = date('Y-m-d'); #2003-09-07%
} else {
  if (preg_match("/([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})/", $_GET['date1'])) {
    $date1 = $_GET["date1"];
  } else {
    //$date1 = date('Y-m-d'); #2003-09-07%
  }
  if (preg_match("/([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})/", $_GET['date2'])) {
    $date2 = $_GET["date2"];
  } else {
    //$date2 = date('Y-m-d'); #2003-09-07%
  }
}

$cal1maxdate = date("Y") . "," . date("m") . "," . date("d");

$show_calendar = true;

require(DIR_WS_INCLUDES . 'template_top.php');

?>
<div id="dhtmltooltip"></div>
<script type="text/javascript">
  /***********************************************
   * Cool DHTML tooltip script- � Dynamic Drive DHTML code library (www.dynamicdrive.com)
   * This notice MUST stay intact for legal use
   * Visit Dynamic Drive at http://www.dynamicdrive.com/ for full source code
   ***********************************************/

  var offsetxpoint = -60 //Customize x offset of tooltip
  var offsetypoint = 20 //Customize y offset of tooltip
  var ie = document.all
  var ns6 = document.getElementById && !document.all
  var enabletip = false
  if (ie || ns6)
    var tipobj = document.all ? document.all["dhtmltooltip"] : document.getElementById ? document.getElementById("dhtmltooltip") : ""

  function ietruebody() {
    return (document.compatMode && document.compatMode != "BackCompat") ? document.documentElement : document.body
  }

  function ddrivetip(thetext, thecolor, thewidth) {
    if (ns6 || ie) {
      if (typeof thewidth != "undefined") tipobj.style.width = thewidth + "px"
      if (typeof thecolor != "undefined" && thecolor != "") tipobj.style.backgroundColor = thecolor
      tipobj.innerHTML = thetext
      enabletip = true
      return false
    }
  }

  function positiontip(e) {
    if (enabletip) {
      var curX = (ns6) ? e.pageX : event.clientX + ietruebody().scrollLeft;
      var curY = (ns6) ? e.pageY : event.clientY + ietruebody().scrollTop;
      //Find out how close the mouse is to the corner of the window
      var rightedge = ie && !window.opera ? ietruebody().clientWidth - event.clientX - offsetxpoint : window.innerWidth - e.clientX - offsetxpoint - 20
      var bottomedge = ie && !window.opera ? ietruebody().clientHeight - event.clientY - offsetypoint : window.innerHeight - e.clientY - offsetypoint - 20

      var leftedge = (offsetxpoint < 0) ? offsetxpoint * (-1) : -1000

      //if the horizontal distance isn't enough to accomodate the width of the context menu
      if (rightedge < tipobj.offsetWidth)
        //move the horizontal position of the menu to the left by it's width
        tipobj.style.left = ie ? ietruebody().scrollLeft + event.clientX - tipobj.offsetWidth + "px" : window.pageXOffset + e.clientX - tipobj.offsetWidth + "px"
      else if (curX < leftedge)
        tipobj.style.left = "5px"
      else
        //position the horizontal position of the menu where the mouse is positioned
        tipobj.style.left = curX + offsetxpoint + "px"

      //same concept with the vertical position
      if (bottomedge < tipobj.offsetHeight)
        tipobj.style.top = ie ? ietruebody().scrollTop + event.clientY - tipobj.offsetHeight - offsetypoint + "px" : window.pageYOffset + e.clientY - tipobj.offsetHeight - offsetypoint + "px"
      else
        tipobj.style.top = curY + offsetypoint + "px"
      tipobj.style.visibility = "visible"
    }
  }

  function hideddrivetip() {
    if (ns6 || ie) {
      enabletip = false
      tipobj.style.visibility = "hidden"
      tipobj.style.left = "-1000px"
      tipobj.style.backgroundColor = ''
      tipobj.style.width = ''
    }
  }

  document.onmousemove = positiontip



  $(document).ready(function() {
    $('form[name=status]').submit(function() {
      $('#tdb6').attr('disabled', 'disabled');
    });
  });
</script>
<script language="javascript">
  <!--
  $.fn.sumValues = function() {
    var sum = 0;
    this.each(function() {
      if ($(this).is(':input')) {
        var val = $(this).val();
      } else {
        var val = $(this).text();
      }

      var quantity = $(this).attr('qty');

      // sum += parseFloat( ('0' + val).replace(/[^0-9-\.]/g, '')) * quantity;
      sum += val * quantity;
    });
    return '&pound;' + sum.toFixed(2);
  };

  // base price
  function update_totalcostpricespan(prod_count) {

    var tradepriceqty = $('input.costpriceinput' + prod_count).attr('qty');
    var tradepricediscount = $('input.tradepricediscount' + prod_count).val();
    var costpriceinput = $('input.costpriceinput' + prod_count).val();
    var trade_price_discount_amount = costpriceinput * (tradepricediscount / 100);
    var trade_price_ex_vat_with_discount = tradepriceqty * (costpriceinput - trade_price_discount_amount);

    $('input.totalcostpricespan' + prod_count).val(trade_price_ex_vat_with_discount.toFixed(2));

  }

  // attributes
  function update_att_totalcostpricespan(attribute_id, prod_count) {

    var attpriceqty = $('input.costpriceinputattribute' + attribute_id).attr('qty');
    var attcostpriceinput = $('input.costpriceinputattribute' + attribute_id).val();
    var attcostprefixinput = $('input.costprefixinputattribute' + attribute_id).val();
    var tradepricediscount = $('input.tradepricediscount' + prod_count).val();

    if (!attpriceqty) {
      attpriceqty = '0';
    }
    if (!attcostpriceinput) {
      attcostpriceinput = '0';
    }
    if (!attcostprefixinput) {
      attcostprefixinput = '0';
    }
    if (!tradepricediscount) {
      tradepricediscount = '0';
    }

    var att_trade_price_discount_amount = attcostpriceinput * (tradepricediscount / 100);

    if (attcostprefixinput == '-') {
      var att_trade_price_ex_vat_with_discount = parseFloat(attcostpriceinput) + parseFloat(att_trade_price_discount_amount); // add the discount amount if its negative
      var attcostpriceinputtotal = 0 - (parseFloat(attpriceqty) * parseFloat(att_trade_price_ex_vat_with_discount));
    } else {
      var att_trade_price_ex_vat_with_discount = parseFloat(attcostpriceinput) - parseFloat(att_trade_price_discount_amount);
      var attcostpriceinputtotal = parseFloat(attpriceqty) * parseFloat(att_trade_price_ex_vat_with_discount);
    }

    $('input.totalcostpricespan' + attribute_id).val(attcostpriceinputtotal.toFixed(2));

  }

  // attribute = + / - value
  function update_att_costpriceinputattribute(attribute_id, prod_count) {

    var costpriceinput = $('input.costpriceinput' + prod_count).val();
    var attcostpricetotalinput = $('input.costpricetotalinputattribute' + attribute_id).val();

    if (!costpriceinput) {
      costpriceinput = '0.0000';
    }

    if (attcostpricetotalinput == 0 || attcostpricetotalinput == '') {
      $('input.costprefixinputattribute' + attribute_id).val('+'); // add prefix
      $('input.costpriceinputattribute' + attribute_id).val('0.0000');
    } else {
      if (parseInt(attcostpricetotalinput) < parseInt(costpriceinput)) {
        var dec_sum = (parseFloat(costpriceinput) - parseFloat(attcostpricetotalinput));
        var dec_sum = Math.abs(dec_sum); // remove from value -
        $('input.costprefixinputattribute' + attribute_id).val('-'); // add prefix
        $('input.costpriceinputattribute' + attribute_id).val(dec_sum.toFixed(2));
      } else {
        var dec_sum = (parseFloat(attcostpricetotalinput) - parseFloat(costpriceinput));
        $('input.costprefixinputattribute' + attribute_id).val('+'); // add prefix
        $('input.costpriceinputattribute' + attribute_id).val(dec_sum.toFixed(2));
      }
    }

  }

  function update_att_costpricetotalinputattribute(attribute_id, prod_count) {

    var costpriceinput = $('input.costpriceinput' + prod_count).val();
    var attcostpriceinput = $('input.costpriceinputattribute' + attribute_id).val();
    var attcostprefixinput = $('input.costprefixinputattribute' + attribute_id).val();

    if (attcostprefixinput == '-') {
      var dec_sum = (parseFloat(costpriceinput) - parseFloat(attcostpriceinput));
    } else {
      var dec_sum = (parseFloat(costpriceinput) + parseFloat(attcostpriceinput));
    }

    $('input.costpricetotalinputattribute' + attribute_id).val(dec_sum.toFixed(2));

  }

  <?php if (($action == 'edit') && ($order_exists == true)) { ?>
    $(document).ready(function() {
      $('.courier').hide();
      $('#courier-select').change(function() {
        $('.courier').hide();
        $('#' + $(this).val()).show();
      });
    });
    $(document).on('paste', '#tracking_id', function(e) {
      e.preventDefault();
      // prevent copying action
      // alert(e.originalEvent.clipboardData.getData('Text'));
      var withoutSpaces = e.originalEvent.clipboardData.getData('Text');
      withoutSpaces = withoutSpaces.replace(/\s+/g, '');
      $(this).val(withoutSpaces);
      // you need to use val() not text()
    });
    s
  <?php } ?>
  //
  -->
</script>
<style>
  .free-gift-check-off {
    display: none;
  }

  .free-gift-check-on {
    color: #FFF;
    background-color: #009966;
    width: 100%;
    text-align: center;
  }

  .cg_plus_membership {
    color: #222;
    padding: 5px 10px;
    margin: 0 10px 0 0;
    border-radius: 3px;
    display: inline;
    font-size: 10px;
    font-weight: 700;
  }

</style>
<table border="0" width="100%" cellspacing="0" cellpadding="2">
  <?php
  if (($action == 'edit') && ($order_exists == true)) {
    $order = new order($oID);

    // Get staff list
    $staff_query = tep_db_query("SELECT staff_id, staff_name FROM staff_list ORDER BY staff_name");
    $staff_array = array();
    while ($staff = tep_db_fetch_array($staff_query)) {
        $staff_array[$staff['staff_id']] = $staff['staff_name'];
    }

    // Get Order Info
    $order_info_query = tep_db_query("SELECT * FROM orders_info WHERE order_id = '" . (int)$oID . "'");
    $order_info = tep_db_fetch_array($order_info_query);

    $order_status_name_query = tep_db_query("SELECT o.orders_location, o.cgars_plus_active from " . TABLE_ORDERS . " o where o.orders_id = '" . $oID . "' ");
    $order_status_name = tep_db_fetch_array($order_status_name_query);

  if($order_status_name['cgars_plus_active'] == '1'){
      $points_icon = '<div class="cg_plus_membership" style="background-color:#D49A6A;" title="Bronze Member">Bronze</div>';
  } elseif($order_status_name['cgars_plus_active'] == '2'){
    $points_icon = '<div class="cg_plus_membership" style="background-color:#D9D9D9;" title="Silver Member">Silver</div>';
  } elseif($order_status_name['cgars_plus_active'] == '3'){
    $points_icon = '<div class="cg_plus_membership" style="background-color:#FFD700;" title="Gold Member">Gold</div>';
  } else {
    $points_icon = '';
  }

  ?>
    <tr>
      <td width="100%">
        <table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" align="left">Order Number: <?php echo $oID;

                                                                // 0 Online Order - London
                                                                // 1 Norfolk Shop Order - London
                                                                // 2 Online Order - Norfolk
                                                                // 3 Norfolk Shop Order - Norfolk
                                                                // 4 Online Order - Alcohol
                                                                // 5 Norfolk Shop Order - Alcohol

                                                                // 8 Incomplete Online Order
                                                                // 9 Incomplete Norfolk Shop Order

                                                                if ($order_status_name['orders_location'] == '0') {
                                                                  echo '&nbsp;&nbsp;&nbsp;London Order (Online) ' . tep_image(DIR_WS_ICONS . 'order_london.png', 'London Order (Online)');
                                                                  $current_orders_location = 0;
                                                                }
                                                                if ($order_status_name['orders_location'] == '1') {
                                                                  echo '&nbsp;&nbsp;&nbsp;London Order (Norfolk Shop) ' . tep_image(DIR_WS_ICONS . 'order_london.png', 'London Order (Norfolk Shop)');
                                                                  $current_orders_location = 0;
                                                                }
                                                                if ($order_status_name['orders_location'] == '2') {
                                                                  echo '&nbsp;&nbsp;&nbsp;Norfolk / Alcohol Order (Online) ' . tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Online)') . ')';
                                                                  $norfolk_order = true;
                                                                  $current_orders_location = 2;
                                                                }
                                                                if ($order_status_name['orders_location'] == '3') {
                                                                  echo '&nbsp;&nbsp;&nbsp;Norfolk / Alcohol Order (Norfolk Shop) ' . tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Norfolk Shop)');
                                                                  $norfolk_order = true;
                                                                  $current_orders_location = 2;
                                                                }
                                                                if ($order_status_name['orders_location'] == '4') {
                                                                  echo '&nbsp;&nbsp;&nbsp;Norfolk / Alcohol Order (Online) ' . tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Online)');
                                                                  $norfolk_order = true;
                                                                  $current_orders_location = 2;
                                                                }
                                                                if ($order_status_name['orders_location'] == '5') {
                                                                  echo '&nbsp;&nbsp;&nbsp;Norfolk / Alcohol Order (Norfolk Shop) ' . tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Norfolk Shop)');
                                                                  $norfolk_order = true;
                                                                  $current_orders_location = 2;
                                                                }
                                                                if ($order_status_name['orders_location'] == '6') {
                                                                  echo '&nbsp;&nbsp;&nbsp;Chester Order' . tep_image(DIR_WS_ICONS . 'order_chester.png', 'Chester Order');
                                                                  $current_orders_location = 6;
                                                                }
                                                                if ($order_status_name['orders_location'] == '7') {
                                                                  echo '&nbsp;&nbsp;&nbsp;Liverpool Order' . tep_image(DIR_WS_ICONS . 'order_liverpool.png', 'Liverpool Order');
                                                                  $current_orders_location = 7;
                                                                }

                                                                if ($norfolk_order) {
                                                                  if ($admin['id'] == 28 || $admin['id'] == 11 || $admin['id'] == 5 || $admin['id'] == 8 || $admin['id'] == 30 || $admin['id'] == 23 || $admin['id'] == 2) {
                                                                    $show_product_tracking = true;
                                                                  }
                                                                }

                                                                ?></td>
            <td class="pageHeading" align="right"><a href="javascript:window.print()"><?php echo tep_image_button('button_print.gif', 'Print'); ?></a>
              <?php echo tep_draw_button(IMAGE_EDIT, 'document', tep_href_link(FILENAME_ORDERS_EDIT, 'oID=' . $_GET['oID']), null) . tep_draw_button(IMAGE_ORDERS_INVOICE, 'document', tep_href_link(FILENAME_ORDERS_INVOICE, 'oID=' . $_GET['oID']), null, array('newwindow' => true)) . tep_draw_button('VAT Invoice', 'document', tep_href_link(FILENAME_ORDERS_INVOICE, 'vat=1&oID=' . $_GET['oID']), null, array('newwindow' => true)) . tep_draw_button(IMAGE_ORDERS_PACKINGSLIP, 'document', tep_href_link(FILENAME_ORDERS_PACKINGSLIP, 'oID=' . $_GET['oID']), null, array('newwindow' => true)) . tep_draw_button(IMAGE_BACK, 'triangle-1-w', tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')))); ?>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <table width="100%" border="0" cellspacing="0" cellpadding="2">
          <tr>
            <td colspan="3"><?php echo tep_draw_separator(); ?></td>
          </tr>
          <tr>
            <td valign="top">
              <table width="100%" border="0" cellspacing="0" cellpadding="2">
                <tr>
                  <td class="main" valign="top"><b><?php echo ENTRY_CUSTOMER; ?></b><br /><br /><?php echo $points_icon; ?></td>
                  <td class="main"><?php echo tep_address_format($order->customer['format_id'], $order->customer, 1, '', '<br>'); ?></td>
                </tr>
                <tr>
                  <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '5'); ?></td>
                </tr>
                <tr>
                  <td class="main"><b><?php echo ENTRY_TELEPHONE_NUMBER; ?></b></td>
                  <td class="main"><?php echo $order->customer['telephone']; ?></td>
                </tr>
                <tr>
                  <td class="main"><b><?php echo ENTRY_EMAIL_ADDRESS; ?></b></td>
                  <td class="main"><?php echo '<a href="mailto:' . $order->customer['email_address'] . '"><u>' . $order->customer['email_address'] . '</u></a>'; ?></td>
                </tr>
              </table>
            </td>
            <td valign="top">
              <table width="100%" border="0" cellspacing="0" cellpadding="2">
                <tr>
                  <td class="main" valign="top"><b><?php echo ENTRY_SHIPPING_ADDRESS; ?></b></td>
                  <td class="main"><?php echo tep_address_format($order->delivery['format_id'], $order->delivery, 1, '', '<br>'); ?></td>
                </tr>
                <tr>
                  <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '5'); ?></td>
                </tr>
                <tr>
                  <td class="main"><b>Delivery Telephone:</b></td>
                  <td class="main"><?php echo $order->delivery['delivery_telephone']; ?></td>
                </tr>
              </table>
            </td>
            <td valign="top">
              <table width="100%" border="0" cellspacing="0" cellpadding="2">
                <tr>
                  <td class="main" valign="top"><b><?php echo ENTRY_BILLING_ADDRESS; ?></b></td>
                  <td class="main"><?php echo tep_address_format($order->billing['format_id'], $order->billing, 1, '', '<br>'); ?></td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
    </tr>
    <tr>
      <td>

        <table border="0" cellspacing="5" cellpadding="5" width="100%">
          <tr>
            <td width="50%" valign="top">


              <table border="0" cellspacing="5" cellpadding="5">
                <tr>
                  <td class="main"><b><?php echo ENTRY_PAYMENT_METHOD; ?></b></td>
                  <td class="main"><?php echo $order->info['payment_method']; ?></td>
                </tr>
                <tr>
                  <td class="main"><b><?php echo ENTRY_CUSTOMER_ORDERS; ?></b></td>
                  <td class="main" bgcolor="#C0C0C0"><?php //START HOW MANY ORDERS 

                                                      // get customer id
                                                      $cust_tilaukset_query2 = tep_db_query("select customers_id, delivery_country_id, date_purchased, customers_dummy_account, third_man, agree_customs, payment_method, subscription_status from " . TABLE_ORDERS . " where orders_id = '" . (int)$oID . "'");
                                                      $cust_tilaukset2 = tep_db_fetch_array($cust_tilaukset_query2);
                                                      // get previous orders where not a dummy account
                                                      $cust_tilaukset_query = tep_db_query("select count(*) from " . TABLE_ORDERS . " where customers_id = '" . $cust_tilaukset2['customers_id'] . "' and customers_dummy_account != 1");

                                                      $cust_tilaukset = tep_db_fetch_array($cust_tilaukset_query);
                                                      // Express
                                                      if ($cust_tilaukset['count(*)'] == "0" or $cust_tilaukset2['customers_id'] == "0" or $order->customer['is_dummy_account']) {
                                                        echo tep_image(DIR_WS_ICONS . 'flag_red.png', 'First Order / Express Checkout') . ' <b>Express Checkout</b>';
                                                        $express_checkout = true;
                                                        // First
                                                      } elseif ($cust_tilaukset['count(*)'] == "1") {
                                                        echo tep_image(DIR_WS_ICONS . 'flag_red.png', 'First Order / Express Checkout') . ' This is the first order';
                                                        // Many
                                                      } elseif ($cust_tilaukset['count(*)'] == "2") {
                                                        echo tep_image(DIR_WS_ICONS . 'flag_red.png', 'First Order / Express Checkout') . ' This is the second order';
                                                        // Many
                                                      } else {
                                                        echo $cust_tilaukset['count(*)'] . '&nbsp;' . 'orders';
                                                      }


                                                      if ($cust_tilaukset2['customers_id'] != 0) {
                                                        $customer_notes_query = tep_db_query("SELECT customers_info_notes FROM " . TABLE_CUSTOMERS_INFO . " WHERE customers_info_id = '" . $cust_tilaukset2['customers_id'] . "' ");
                                                        $customer_notes = tep_db_fetch_array($customer_notes_query);
                                                        if (tep_not_null($customer_notes['customers_info_notes'])) {
                                                          echo '<div style="background-color: #990000; color: #FFF; padding: 10px;" ><strong>WARNING</strong> ' . $customer_notes['customers_info_notes'] . '</div>';
                                                        }
                                                      }

                                                      ?></td>
                </tr>
                <?php
                // END HOW MANY ORDERS
                ?>
                <?php if (strpos($order->info['payment_method'], 'Transfer') !== false && !$express_checkout) { ?>
                <tr>
                  <td class="main"><b>Loyalty Points:</b></td>
                  <td class="main">
                <div  style="background-color: #CEEFEF; padding: 10px;">    
                <table>  
                <tr>
                  <td class="main"><?php 
          
          // Get Loyalty Settings
          $loyalty_settings = tep_loyalty_scheme_logic($cust_tilaukset2['customers_id']);

          $loyalty_order_history = array(
            'editor_cg_plus_was_earned' => 0,
            'editor_cg_plus_was_redeemed' => 0,
            'editor_lp_legacy_was_earned' => 0,
            'editor_lp_legacy_was_redeemed' => 0
          ); 

// EARN
if ($loyalty_settings['process_cg_plus_earn']) { #### CGars Plus ####
  
  $cp_check_query = tep_db_query("select points_pending, points_comment, date_added, points_status from " . TABLE_CGARS_PLUS . " where orders_id = '" . (int)$oID . "' order by date_added desc");
  while($cp_check = tep_db_fetch_array($cp_check_query)){
    if($cp_check['points_status'] == '2'){ // earn
      $loyalty_order_history['editor_cg_plus_was_earned'] += $cp_check['points_pending']; // a) Were cgars plus points earned in this order
    } else { // spend or lose
      $loyalty_order_history['editor_cg_plus_was_redeemed'] += $cp_check['points_pending']; // b) Were cgars plus points redeemed in this order
    }
  }

} 

if ($loyalty_settings['process_lp_legacy_earn'] && $loyalty_order_history['editor_cg_plus_was_earned'] == 0) { #### Points/Rewards Module ####
  
  $lp_check_query = tep_db_query("select points_pending, points_comment, date_added, points_status from " . TABLE_CUSTOMERS_POINTS_PENDING . " where orders_id = '" . (int)$oID . "' order by date_added desc");
  while($lp_check = tep_db_fetch_array($lp_check_query)){
    if($lp_check['points_status'] == '2'){ // earn
      $loyalty_order_history['editor_lp_legacy_was_earned'] += $lp_check['points_pending']; // c) Were lp legacy points earned in this order
    } else { // spend or lose
      $loyalty_order_history['editor_lp_legacy_was_redeemed'] += $lp_check['points_pending']; // d) Were lp legacy points redeemed in this order 
    }
  }

}

if($loyalty_order_history['editor_cg_plus_was_earned'] > 0){ 
  echo $loyalty_order_history['editor_cg_plus_was_earned'] . ' CGars Plus points have been awarded for this order.';
} elseif($loyalty_order_history['editor_lp_legacy_was_earned'] > 0){
  echo $loyalty_order_history['editor_lp_legacy_was_earned'] . ' loyalty points have been awarded for this order.';
} else {
  echo '<h2>No points have been earned for this order</h2>';
  echo 'Once payment has been made please click the Add Points button below and add<br />the appropriate amount of points in the highlighted orange box on the order editor.<br /><br />';
  echo tep_draw_button('Add Points', 'document', tep_href_link(FILENAME_ORDERS_EDIT, 'oID=' . $_GET['oID'] . '&hl=earn'), null);
}
              
                  ?></td>
                </tr>
          </table>
          </div>
                 </td>
                </tr>
                <?php } ?>
                <tr>
                  <td class="main"></td>
                  <td class="main">
                    <?php

                    $free_gift = tep_customer_free_gift($cust_tilaukset2['customers_id']);

                    if ($free_gift == 'yes') {
                      echo '<button type="button" id="free-gift-check" name="' . $cust_tilaukset2['customers_id'] . '" value="yes" class="free-gift-check-on pure-button">Send Free Gift</button>';
                    } elseif ($free_gift == 'sent') {
                      echo '<button type="button" id="free-gift-check" name="' . $cust_tilaukset2['customers_id'] . '" value="sent" class="free-gift-check-off pure-button">Free Gift Sent</button>';
                    }

                    ?>
                  </td>
                </tr>



                <tr>
                  <td class="main"><b>Source:</b></td>
                  <td>
                    <?php
                    if ($order->customer['id'] != 0) {

                      $sources_id_query = tep_db_query("SELECT customers_info_source_id FROM " . TABLE_CUSTOMERS_INFO . " WHERE customers_info_id = '" . $order->customer['id'] . "' ");
                      $sources_id = tep_db_fetch_array($sources_id_query);
                      echo tep_get_sources_name($sources_id['customers_info_source_id'], $order->customer['id']);
                    } else {

                      $sources_express_id_query = tep_db_query("SELECT express_info_source_id FROM " . TABLE_SOURCES_EXPRESS . " WHERE express_orders_id = '" . (int)$oID . "' ");
                      $sources_express_id = tep_db_fetch_array($sources_express_id_query);
                      echo tep_get_express_sources_name($sources_express_id['express_info_source_id'], (int)$oID);
                    }
                    ?>
                  </td>
                </tr>
                <?php
                $sagepay_comments_query = tep_db_query("select sagepay_report from orders_sagepay_comments where orders_id = '" . tep_db_input($oID) . "'");
                if (tep_db_num_rows($sagepay_comments_query)) {
                  $sagepay_comments = tep_db_fetch_array($sagepay_comments_query)
                ?>

                  <tr>
                    <td class="main"><strong><?php echo $order->info['payment_method']; ?> Report:</strong> </td>
                    <td class="main">
                      <?php 
                      
  echo nl2br(html_entity_decode($sagepay_comments['sagepay_report']));

if (strpos($cust_tilaukset2['payment_method'], 'PayPal') !== false) {
if ((strpos($sagepay_comments['sagepay_report'], 'Pending') !== false) || (strpos($sagepay_comments['sagepay_report'], 'Ineligible') !== false) || ($cust_tilaukset2['third_man'] == '3')) { ?>
<div class="main" style="position: absolute; top: 225px; right: 10px; font-weight: 700; background-color: #990000; color: #FFF; padding: 7px; font-size: 16px; text-align: center;">
<?php 

if($cust_tilaukset2['third_man'] == '3'){
  echo 'Warning: PayPal Duplicate Transaction ID.';
} elseif (strpos($sagepay_comments['sagepay_report'], 'Ineligible') !== false) {
  echo 'Warning: PayPal Seller Protection Ineligible.';
} else {
  echo 'Warning: PayPal Payment Pending';
}                                                                                                                                                                                
                                                                                                                                                                                                            
?>
</div>
<?php } } 

if(strpos($sagepay_comments['sagepay_report'], 'ATTEMPTONLY')){
  echo '<div class="main" style="position: absolute; top: 225px; right: 10px; font-weight: 700; background-color: #990000; color: #FFF; padding: 7px; font-size: 16px; text-align: center;">Warning: Do NOT ship. Please refund, ring customer and ask them to try a different card.</div>';
}

?>

                    </td>
                  </tr>
                  <tr>
                    <td class="main"><b>3rd Man</b></td>
                    <td class="main">
                      <?php

                      if ($cust_tilaukset2['third_man'] == '0') {
                        $third_man_icon = 'icon-shield-outline.png';
                      } elseif ($cust_tilaukset2['third_man'] == '1') {
                        $third_man_icon = 'icon-shield-check.png';
                      } elseif ($cust_tilaukset2['third_man'] == '2') {
                        $third_man_icon = 'icon-shield-question.png';
                      } elseif ($cust_tilaukset2['third_man'] == '3') {
                        $third_man_icon = 'icon-shield-cross.png';
                      } elseif ($cust_tilaukset2['third_man'] == '4') {
                        $third_man_icon = 'icon-shield-zebra.png';
                      } else {
                        $third_man_icon = '';
                      }

                      ?>
                      <a class="sagepay-report" href="<?php echo tep_href_link('sagepay_reporting.php', 'orders_id=' . $oID); ?>"><?php echo tep_image(DIR_WS_ICONS . $third_man_icon, 'Click for Sage Info'); ?></a>&nbsp;&nbsp;&nbsp;
<?php

if($cust_tilaukset2['third_man'] >= 1 && $cust_tilaukset2['third_man'] <= 3){

  $order_total_query = tep_db_query("select ot.text as order_total, ot.value as order_total_value, ot.class from " . TABLE_ORDERS_TOTAL . " ot where ot.orders_id = '" . tep_db_input($oID) . "' and (ot.class = 'ot_total' or ot.class = 'ot_discount_coupon') ");
                  
  while($order_total = tep_db_fetch_array($order_total_query)){  
    if($order_total['class'] == 'ot_total'){
      $order_total_value = number_format((float)$order_total['order_total_value'], 2);
    }
  }

 $sagepay_amount_query = tep_db_query("SELECT s.sage_3d_amount from sagepay_server_log s where s.orders_id = '" . tep_db_input($oID) . "' limit 1");
 $sagepay_amount = tep_db_fetch_array($sagepay_amount_query);
 if (is_numeric($sagepay_amount['sage_3d_amount'])) { 
  $sagepay_amount_output = number_format((float)$sagepay_amount['sage_3d_amount'], 2);
  if($sagepay_amount_output != $order_total_value){
    echo '<strong style="color:#CC0000; font-size:22px;">' . $sagepay_amount_output . '</strong>'; 
  } else {
    echo $sagepay_amount_output;
  }
 }
}

?>
                    </td>
                  </tr>
                <?php } ?>
                <?php
                $check_geo_zone_array = array();
                $find_geo_zone_query = tep_db_query("select geo_zone_id from " . TABLE_ZONES_TO_GEO_ZONES . " where zone_country_id = '" . (int)$cust_tilaukset2['delivery_country_id'] . "'");
                while ($find_geo_zone = tep_db_fetch_array($find_geo_zone_query)) {
                  $check_geo_zone_array[] = $find_geo_zone['geo_zone_id'];
                }
                if (!in_array("3", $check_geo_zone_array)) {
                ?>
                  <tr>
                    <td class="main"><b>Accepted Customs Risk:</b></td>
                    <td class="main"> <?php if ($cust_tilaukset2['agree_customs'] != '1') { ?>No<?php $customs_warning = true;
                                                                                              } else { ?>Yes<?php } ?></td>
                  </tr>
                <?php } ?>
                <?php
                $gift_comments_query = tep_db_query("select gift_message, gift_invoice from orders_gift_comments where orders_id = '" . tep_db_input($oID) . "'");
                if (tep_db_num_rows($gift_comments_query)) {
                  $gift_comments = tep_db_fetch_array($gift_comments_query)
                ?>
                  <tr>
                    <td class="main"><strong>Gift Message:</strong> </td>
                    <td class="main"><?php echo $gift_comments['gift_message']; ?></td>
                  </tr>
                  <?php if ($gift_comments['gift_invoice'] == '1') { ?>
                    <tr>
                      <td class="main"></td>
                      <td class="main">Include an invoice in shipment.</td>
                    </tr>
                  <?php } else { ?>
                    <tr>
                      <td class="main"></td>
                      <td class="main"><strong>Do not</strong> include an invoice in shipment.</td>
                    </tr>
                  <?php } ?>
                <?php } ?>
              </table>

            </td>
            <td width="50%" valign="top">


              <?php

              echo '<strong class="main">Upload I.D link</strong><br /><br />';

              if ($express_checkout) {
                echo '<a href="https://www.cgarsltd.co.uk/upload_id.php?oID=' . (int)$oID . '&cID=' . $cust_tilaukset2['customers_id'] . '">https://www.cgarsltd.co.uk/upload_id.php?oID=' . (int)$oID . '&cID=' . $cust_tilaukset2['customers_id'] . '</a>';
              } else {
                echo '<a href="https://www.cgarsltd.co.uk/upload_id.php">https://www.cgarsltd.co.uk/upload_id.php</a>';
              }

              echo '<br /><br />';

              // check if id has already been approved...
              $verified_query = tep_db_query("select id, original_name, verified, deleted from uploads where customers_id = '" . $cust_tilaukset2['customers_id'] . "'");

              while ($num_verified = tep_db_fetch_array($verified_query)) {

                if (!$header_count) {
                  echo tep_draw_form('id_update', FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=id_update');
                  echo '<table border="1" cellspacing="0" cellpadding="5" align="left"><tr class="main" style="font-weight: 700;"><td>Uploaded I.D. files</td><td align="center">Verify</td><td align="center">Delete</td></tr>';
                  $header_count = true;
                }

                echo '<tr class="main">';
                echo '<td align="left"><a href="id_upload.php?id=' . $num_verified['id'] . '" target="_blank">' . $num_verified['original_name'] . '</a></td>';

                if ($num_verified['verified'] == 1) {
                  echo '<td align="center">Verified</td>';
                } else {
                  echo '<td align="center">' . tep_draw_checkbox_field('verify_tick[' . $num_verified['id'] . ']', '1', true) . '</td>';
                }

                if ($num_verified['deleted'] == 1) {
                  echo '<td align="center">Deleted</td>';
                } else {
                  echo '<td align="center">' . tep_draw_checkbox_field('delete_tick[' . $num_verified['id'] . ']', '1', true) . '</td>';
                }

                echo '</tr>';
              }

              if ($header_count) {
                echo '<tr><td></td><td colspan="2" align="center">' . tep_image_submit('button_update.gif', IMAGE_UPDATE) . '</td></tr></table></form>';
              }

              ?>
<?php
$cadogan_query = tep_db_query("select ot.title from " . TABLE_ORDERS_TOTAL . " ot where ot.orders_id = '" . tep_db_input($oID) . "' and ot.class = 'ot_shipping' ");       
while($cadogan = tep_db_fetch_array($cadogan_query)){   
  if (strpos($cadogan['title'], 'SOUTHEND-ON-SEA') !== false) {
    $cadogan_warning = true;
  }
}
          if ($cust_tilaukset2['delivery_country_id'] == '222') { // $cust_tilaukset2['subscription_status'] < 1 && 
            if(tep_magazine_check($cust_tilaukset2['customers_id'], $oID, $cust_tilaukset2['date_purchased'], $cust_tilaukset2['customers_dummy_account'])){
            
          ?>
<div class="main" style="padding: 30px 10px 10px 0; color: #006699; font-size: 18px; clear: both;"><strong>Send Magazine</strong> <img src="images/magazine.png" width="30px"></div>
    <?php } } ?>

          </td>
          </tr>
        </table>

      </td>
    </tr>
    <tr>
      <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
    </tr>
    <tr>
      <td>
        <div align="right" class="main" style="margin-bottom: 5px;">(All cost prices are ex vat)</div>
        <?php echo tep_draw_form('order_cost_update', FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=order_cost_update'); ?>
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
          <tr class="dataTableHeadingRow">
            <td class="dataTableHeadingContent"></td>
            <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_PRODUCTS; ?></td>
            <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_PRODUCTS_MODEL; ?></td>
            <td class="dataTableHeadingContent" align="right"></td>
            <td class="dataTableHeadingContent" align="center">Supplier</td>
            <td class="dataTableHeadingContent" align="center">Stock Location</td>
            <td class="dataTableHeadingContent" align="right"></td>
            <td class="dataTableHeadingContent" align="right"></td>
            <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_TOTAL_INCLUDING_TAX; ?></td>
            <td style="background-color: #FFF;"></td>
            <td class="dataTableHeadingContent" style="background-color:#00CC99;" width="230">Saved Cost Price (each)</td>
            <td class="dataTableHeadingContent" style="background-color:#00CC99;"></td>
            <td class="dataTableHeadingContent" style="background-color:#00CC99;" width="200" align="right">Cost Price (each)</td>
            <td class="dataTableHeadingContent" style="background-color:#00CC99;" width="80" align="center">S.D%</td>
            <td class="dataTableHeadingContent" style="background-color:#00CC99;" width="80" align="right">Total</td>
          </tr>
          <?php

          $all_order_details_saved = true;
          $prod_count = 1;

          for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {

            echo '          <tr class="dataTableRow">' . "\n" .
              '            <td class="dataTableContent" valign="top" align="right">' . $order->products[$i]['qty'] . '&nbsp;x</td>' . "\n" .
              '            <td class="dataTableContent" valign="top">' . $order->products[$i]['name'];

            if ($order->products[$i]['locker_item']) {
              echo '&nbsp;&nbsp;&nbsp;<strong> !!! REMOVE FROM LOCKER !!! </strong>';
            }

            $stock_location_query = tep_db_query("select psl.stock_liverpool, psl.stock_liverpool_stashed, psl.stock_chester, psl.stock_chester_stashed, psl.stock_leeds, psl.stock_leeds_stashed, psl.stock_mashtun, psl.stock_mashtun_stashed, psl.stock_lime_street, psl.stock_edinburgh, psl.stock_london, psl.stock_london_cg, psl.stock_mayfair, psl.stock_norfolk, psl.stock_knutsford, psl.stock_supplier from " . TABLE_PRODUCTS_TO_STOCK_LOCATION . " psl where psl.products_id = '" . $order->products[$i]['id'] . "'");

            $stock_location = '';
            $main_location = 0;

            if (tep_db_num_rows($stock_location_query)) {
              while ($stock_location_result = tep_db_fetch_array($stock_location_query)) {
                if ($stock_location_result['stock_norfolk'] != 0) {
                  $stock_location .= 'Norfolk, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_liverpool'] != 0) {
                  $stock_location .= 'Liverpool, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_liverpool_stashed'] != 0) {
                  $stock_location .= 'L. Stashed, ';
                }
                if ($stock_location_result['stock_chester'] != 0) {
                  $stock_location .= 'Chester, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_chester_stashed'] != 0) {
                  $stock_location .= 'C. Stashed, ';
                }
                if ($stock_location_result['stock_leeds'] != 0) {
                  $stock_location .= 'Leeds, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_leeds_stashed'] != 0) {
                  $stock_location .= 'Lds. Stashed, ';
                }
                if ($stock_location_result['stock_mashtun'] != 0) {
                  $stock_location .= 'Mash Tun, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_mashtun_stashed'] != 0) {
                  $stock_location .= 'MT. Stashed, ';
                }
                if ($stock_location_result['stock_lime_street'] != 0) {
                  $stock_location .= 'Lime Street, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_edinburgh'] != 0) {
                  $stock_location .= 'Edinburgh, ';
                  $main_location++;
                }                                
                if ($stock_location_result['stock_london'] != 0) {
                  $stock_location .= 'London, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_london_cg'] != 0) {
                  $stock_location .= 'St James, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_mayfair'] != 0) {
                  $stock_location .= 'Mayfair, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_knutsford'] != 0) {
                  $stock_location .= 'Knutsford, ';
                  $main_location++;
                }
                if ($stock_location_result['stock_supplier'] != 0) {
                  $stock_location .= 'Drop Ship';
                }
              }
            }

            if($main_location == 0){ // no main stock locations have stock so get last known place(s) with stock
              $stock_location_history_query = tep_db_query("select pslh.stock_liverpool, pslh.stock_chester, pslh.stock_leeds, pslh.stock_mashtun, pslh.stock_lime_street, pslh.stock_edinburgh, pslh.stock_london, pslh.stock_london_cg, pslh.stock_mayfair, pslh.stock_norfolk, pslh.stock_knutsford from " . TABLE_PRODUCTS_TO_STOCK_LOCATION_HISTORY . " pslh where pslh.products_id = '" . $order->products[$i]['id'] . "' and ((pslh.stock_liverpool > 0) or (pslh.stock_chester > 0) or (pslh.stock_leeds > 0) or (pslh.stock_mashtun > 0) or (pslh.stock_lime_street > 0) or (pslh.stock_edinburgh > 0) or (pslh.stock_london > 0) or (pslh.stock_london_cg > 0) or (pslh.stock_mayfair > 0) or (pslh.stock_norfolk > 0) or (pslh.stock_knutsford > 0)) order by pslh.products_to_stock_location_history_id desc limit 1");
  
              if (tep_db_num_rows($stock_location_history_query)) {
                while ($stock_location_history_result = tep_db_fetch_array($stock_location_history_query)) {
                  if ($stock_location_history_result['stock_norfolk'] != 0) {
                    $stock_location .= 'Norfolk*, ';
                  }
                  if ($stock_location_history_result['stock_liverpool'] != 0) {
                    $stock_location .= 'Liverpool*, ';
                  }
                  if ($stock_location_history_result['stock_chester'] != 0) {
                    $stock_location .= 'Chester*, ';
                  }
                  if ($stock_location_history_result['stock_leeds'] != 0) {
                    $stock_location .= 'Leeds*, ';
                  }
                  if ($stock_location_history_result['stock_mashtun'] != 0) {
                    $stock_location .= 'Mash Tun*, ';
                  }
                  if ($stock_location_history_result['stock_lime_street'] != 0) {
                    $stock_location .= 'Lime Street*, ';
                  }
                  if ($stock_location_history_result['stock_edinburgh'] != 0) {
                    $stock_location .= 'Edinburgh*, ';
                  }                                    
                  if ($stock_location_history_result['stock_london'] != 0) {
                    $stock_location .= 'London*, ';
                  }
                  if ($stock_location_history_result['stock_london_cg'] != 0) {
                    $stock_location .= 'St James*, ';
                  }
                  if ($stock_location_history_result['stock_mayfair'] != 0) {
                    $stock_location .= 'Mayfair*, ';
                  }
                  if ($stock_location_history_result['stock_knutsford'] != 0) {
                    $stock_location .= 'Knutsford*, ';
                  }
                }
              }
            }

            $shipping_type_query = tep_db_query("select products_free_shipping from " . TABLE_PRODUCTS . " where products_id = '" . $order->products[$i]['id'] . "' ");
            $shipping_type = tep_db_fetch_array($shipping_type_query);

            if ($shipping_type['products_free_shipping'] == '4') {
              $butane_gas_item = true;
            }
            if ($shipping_type['products_free_shipping'] == '5') {
              $lighter_item = true;
            }


            $suppliers_query = tep_db_query("select suppliers_name from " . TABLE_SUPPLIERS . " where suppliers_id = '" . $order->products[$i]['suppliers_id'] . "' ");
            $suppliers = tep_db_fetch_array($suppliers_query);
            $supplier = $suppliers['suppliers_name'];

            echo '            </td>' . "\n" .
              '            <td class="dataTableContent" valign="top">' . $order->products[$i]['model'] . '</td>' . "\n" .
              '            <td class="dataTableContent" align="right" valign="top"></td>' . "\n" .
              '            <td class="dataTableContent" align="center" valign="top">' . $supplier . '</td>' . "\n" .
              '            <td class="dataTableContent" align="center" valign="top">' . $stock_location . '</td>' . "\n";

            if ($user_access == 'laura') {

              $show_locker_link_query = tep_db_query("select original_products_id from " . TABLE_PRODUCTS_TO_CUSTOMER_LOCKER . " where customers_id = '" . $cust_tilaukset2['customers_id'] . "' and original_products_id = '" . $order->products[$i]['id'] . "' and add_orders_id = '" . (int)$oID . "'");
              $show_locker_link = tep_db_fetch_array($show_locker_link_query);

              if ($show_locker_link['original_products_id']) {

                echo '            <td class="dataTableContent" align="right" valign="top">(In Locker)</td>' . "\n";
              } else {

                echo '            <td class="dataTableContent" align="right" valign="top"><u><a href="' . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'pID', 'cID', 'qID', 'action')) . 'oID=' . (int)$oID . '&pID=' . $order->products[$i]['id'] . '&cID=' . $cust_tilaukset2['customers_id'] . '&qID=' . $order->products[$i]['qty'] . '&action=locker') . '">Add to Locker</a></u></td>' . "\n";
              }
            } else {

              echo '            <td class="dataTableContent" align="right" valign="top"></td>' . "\n";
            }

            echo '            <td class="dataTableContent" align="right" valign="top"></td>' . "\n" .
              '            <td class="dataTableContent" align="right" valign="top"><b>' . $currencies->format(tep_add_tax($order->products[$i]['final_price'], $order->products[$i]['tax']) * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";

            $cost_price_text = 'Save Product Cost Price?';
            $cost_price = '';
            $cost_price_tick = true;

            if (tep_not_null($order->products[$i]['products_trade_price'])) {
              $cost_price_text = 'Update Product Cost Price?';
              $cost_price = number_format((float)$order->products[$i]['products_trade_price'], 2);
              $cost_price_tick = false;
            }

            if (tep_not_null($order->products[$i]['orders_products_cg_cost'])) {
              $cost_price_text = 'Update Product Cost Price?';
              $cost_price = number_format((float)$order->products[$i]['orders_products_cg_cost'], 2);
              $cost_price_tick = false;
            }

            $orders_products_trade_price_discount = number_format((float)$order->products[$i]['orders_products_trade_price_discount'], 2);

            list($prod_total_not_calculated, $prod_product_retail_total, $prod_product_cost_total_ex_vat) = tep_get_product_figures($order->products[$i]['id'], $order->products[$i]['final_price'], $order->products[$i]['qty'], $cost_price, $orders_products_trade_price_discount);

            if ($prod_total_not_calculated) {
              $individual_product_margin = 'Incomplete';
            } else {
              $prod_product_retail_total_ex_vat = ($prod_product_retail_total / 1.2);
              if ($prod_product_retail_total_ex_vat > 0) {
                $individual_product_margin = tep_round((((($prod_product_retail_total_ex_vat) - ($prod_product_cost_total_ex_vat)) / ($prod_product_retail_total_ex_vat)) * 100), 0) . '%';
              } else {
                $individual_product_margin = 0;
              }
            }

            echo '          <td style="background-color: #FFF;" align="center"><img src="images/icon_status_museum_light.gif" border="0" title="' . $individual_product_margin . '"></td>' . "\n";

            echo '          <td class="dataTableContent" valign="top" style="background-color: #E0F9F3;"><span>&nbsp;&pound;' . number_format((float)$order->products[$i]['products_trade_price'], 2) . '</span>' . "\n";
            echo '          &nbsp;<span class="cost_price_text' . $prod_count . '">' . tep_draw_checkbox_field('products_cost_value_tick[' . $order->products[$i]['id'] . ']', '', $cost_price_tick) . ' ' . $cost_price_text . '</span></td>' . "\n";

            echo '          <td class="dataTableContent" align="center" valign="top" style="background-color: #E0F9F3;" ></td>' . "\n";

            echo '          <td class="dataTableContent" align="right" valign="top" style="background-color: #E0F9F3;" >' . tep_draw_input_field('products_cost_value[' . $order->products[$i]['id'] . ']', $cost_price, 'size="10" qty="' . $order->products[$i]['qty'] . '" class="costpriceinput' . $prod_count . '" style="text-align: right;"') . '</td>' . "\n";

            echo '          <td class="dataTableContent" align="center" valign="top" style="background-color: #E0F9F3;" >' . tep_draw_input_field('orders_products_trade_price_discount[' . $order->products[$i]['id'] . ']', $orders_products_trade_price_discount, 'size="2" class="tradepricediscount' . $prod_count . '" style="text-align: right;"') . '%</td>' . "\n";

            echo '          <td class="dataTableContent" align="right" valign="top" style="background-color: #E0F9F3;" >
	  ' . tep_draw_input_field('totalcostpricespan[' . $order->products[$i]['id'] . ']', '', 'size="2" qty="1" class="totalcostpricespan' . $prod_count . '" readonly style="text-align: right; border: none; background-color: transparent;"') . '</td>' . "\n";
            echo '          </tr>' . "\n";

            // Attribute Rows Start
            //$normal_cost_price_update_is_void = 0;
            $order_total_reduction = 0;

            if (isset($order->products[$i]['attributes']) && (sizeof($order->products[$i]['attributes']) > 0)) {
              for ($j = 0, $k = sizeof($order->products[$i]['attributes']); $j < $k; $j++) {

                $attribute_cost_price_text = 'Save Attribute Cost Price?';
                $attribute_cost_price = '';
                $attribute_cost_prefix = '';
                $attribute_cost_price_tick = true;
                $attribute_cost_price_total = '';
                $attribute_cost_price_product_total = '';
                $allow_stored_attribute_cost_price_to_be_changed = false;

                if ($order->products[$i]['attributes'][$j]['product_attribute_id'] != 0) { // orders before this mod was added = 0
                  // do stuff to allow you to change the product attribute cost price
                  $allow_stored_attribute_cost_price_to_be_changed = true;

                  if (tep_not_null($order->products[$i]['attributes'][$j]['product_cost'])) {  // attributes table
                    $attribute_cost_price_text = 'Update Attribute Cost Price?';
                    $attribute_cost_price = number_format((float)$order->products[$i]['attributes'][$j]['product_cost'], 2);
                    $attribute_cost_prefix = $order->products[$i]['attributes'][$j]['product_cost_prefix'];
                    $attribute_cost_price_tick = false;
                    if ($attribute_cost_prefix == '-') {
                      $attribute_cost_price_total = ($cost_price - $order->products[$i]['attributes'][$j]['product_cost']);
                      $attribute_cost_price_product_total = ($cost_price - $order->products[$i]['attributes'][$j]['product_cost']);
                    } else {
                      $attribute_cost_price_total = ($cost_price + $order->products[$i]['attributes'][$j]['product_cost']);
                      $attribute_cost_price_product_total = ($cost_price + $order->products[$i]['attributes'][$j]['product_cost']);
                    }
                  }
                }

                // the order cost price overides the stored product cost price (if available)
                if (tep_not_null($order->products[$i]['attributes'][$j]['order_cost'])) { // orders_attributes_products table
                  $attribute_cost_price_text = 'Update Attribute Cost Price?';
                  $attribute_cost_price = number_format((float)$order->products[$i]['attributes'][$j]['order_cost'], 2);
                  $attribute_cost_prefix = $order->products[$i]['attributes'][$j]['order_cost_prefix'];
                  $attribute_cost_price_tick = false;
                  if ($attribute_cost_prefix == '-') {
                    $attribute_cost_price_total = ($cost_price - $order->products[$i]['attributes'][$j]['order_cost']);
                  } else {
                    $attribute_cost_price_total = ($cost_price + $order->products[$i]['attributes'][$j]['order_cost']);
                  }
                }

                //if (!in_array($order->products[$i]['attributes'][$j]['option_id'], array(22,23,24,26,4,35,36), true)) {
                if (tep_display_attribute_details($order->products[$i]['attributes'][$j]['product_attribute_id'], $order->products[$i]['attributes'][$j]['option'], $order->products[$i]['attributes'][$j]['value'], $order->products[$i]['attributes'][$j]['prefix'], $order->products[$i]['attributes'][$j]['price'], $order->products[$i]['qty'], $order->info['currency'], $order->info['currency_value'])) {


                  echo '<tr><td style="background-color: #F7F8F8;"></td>';

                  echo '<td class="dataTableContent" style="background-color: #F7F8F8;">' . tep_display_attribute_details($order->products[$i]['attributes'][$j]['product_attribute_id'], $order->products[$i]['attributes'][$j]['option'], $order->products[$i]['attributes'][$j]['value'], $order->products[$i]['attributes'][$j]['prefix'], $order->products[$i]['attributes'][$j]['price'], $order->products[$i]['qty'], $order->info['currency'], $order->info['currency_value']) . '</td>';

                  echo '<td style="background-color: #F7F8F8;"></td>
		  <td style="background-color: #F7F8F8;"></td>
		  <td style="background-color: #F7F8F8;"></td>
		  <td style="background-color: #F7F8F8;"></td>
		  <td style="background-color: #F7F8F8;"></td>
		  <td style="background-color: #F7F8F8;"></td>
		  <td style="background-color: #F7F8F8;"></td>
		  <td></td>';

                  // is there an attribute price?
                  if ($order->products[$i]['attributes'][$j]['price']) { // we need to think about attribute cost prices...

                    //$normal_cost_price_update_is_void = 1;

                    echo '<td class="dataTableContent" valign="top" style="background-color: #EFFCF9;">';

                    if ($allow_stored_attribute_cost_price_to_be_changed) {

                      echo '&nbsp;&pound;' . number_format((float)$attribute_cost_price_product_total, 2) . ' &nbsp;' . tep_draw_checkbox_field('products_attribute_cost_value_tick[' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . ']', '', $attribute_cost_price_tick) . ' ' . $attribute_cost_price_text;
                    }

                    echo '</td>
		  <td class="dataTableContent" valign="top" align="right" style="background-color: #EFFCF9;">';

                    if ($allow_stored_attribute_cost_price_to_be_changed) {

                      echo  tep_draw_input_field('products_attribute_cost_prefix[' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . ']', $attribute_cost_prefix, 'size="1" qty="' . $order->products[$i]['qty'] . '" readonly class="costprefixinputattribute' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . '" style="text-align: right; background: none; border: none;"') .

                        tep_draw_input_field('products_attribute_cost_value[' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . ']', $attribute_cost_price, 'size="3" qty="' . $order->products[$i]['qty'] . '" readonly class="costpriceinputattribute' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . '" style="text-align: left; background: none; border: none;"');
                    }

                    echo '</td><td style="background-color: #EFFCF9;" align="right">';

                    if ($allow_stored_attribute_cost_price_to_be_changed) {

                      echo tep_draw_input_field('products_attribute_cost_total[' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . ']', number_format((float)$attribute_cost_price_total, 2), 'size="10" qty="' . $order->products[$i]['qty'] . '" class="costpricetotalinputattribute' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . '" style="text-align: right;"');
                    }

                    echo '</td>';

                    echo '<td style="background-color: #EFFCF9;"></td>
		  <td class="dataTableContent" align="right" valign="top" style="background-color: #EFFCF9;">';

                    if ($allow_stored_attribute_cost_price_to_be_changed) {

                      echo tep_draw_input_field('totalcostpricespan[' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . ']', '', 'size="2" qty="1" class="totalcostpricespan' . $order->products[$i]['attributes'][$j]['product_attribute_id'] . '" readonly style="text-align: right; border: none; background-color: transparent;"');
                    }

                    echo '</td>';

                    if ($allow_stored_attribute_cost_price_to_be_changed) {

          ?>
                      <script language="javascript">
                        <!--
                        // attribute fields
                        $(document).ready(function() {

                          update_att_totalcostpricespan(<?php echo $order->products[$i]['attributes'][$j]['product_attribute_id']; ?>, <?php echo $prod_count; ?>);

                          $('input.costpriceinput<?php echo $prod_count; ?>').on('change paste keyup', function() {
                            update_att_totalcostpricespan(<?php echo $order->products[$i]['attributes'][$j]['product_attribute_id']; ?>, <?php echo $prod_count; ?>);
                            update_att_costpricetotalinputattribute(<?php echo $order->products[$i]['attributes'][$j]['product_attribute_id']; ?>, <?php echo $prod_count; ?>);
                          });

                          $('input.tradepricediscount<?php echo $prod_count; ?>').on('change paste keyup', function() {
                            update_att_totalcostpricespan(<?php echo $order->products[$i]['attributes'][$j]['product_attribute_id']; ?>, <?php echo $prod_count; ?>);
                          });

                          $('input.costpricetotalinputattribute<?php echo $order->products[$i]['attributes'][$j]['product_attribute_id']; ?>').bind('keyup', function() {
                            update_att_costpriceinputattribute(<?php echo $order->products[$i]['attributes'][$j]['product_attribute_id']; ?>, <?php echo $prod_count; ?>);
                            update_att_totalcostpricespan(<?php echo $order->products[$i]['attributes'][$j]['product_attribute_id']; ?>, <?php echo $prod_count; ?>);
                          });

                        });
                        //
                        -->
                      </script>
              <?php

                      // attribute price exists so we need an attribute cost price
                      if (tep_not_null($order->products[$i]['attributes'][$j]['order_cost'])) { // if an attribute cost price does exist...

                        $att_cost_price = $order->products[$i]['attributes'][$j]['order_cost'];

                        if ($order->products[$i]['orders_products_trade_price_discount'] != 0) {
                          $att_cost_price = ($att_cost_price - ($att_cost_price * ($order->products[$i]['orders_products_trade_price_discount'] / 100)));
                        }

                        if ($attribute_cost_prefix == '-') {
                          $total_calculated_value -= ($att_cost_price * $order->products[$i]['qty']); // add the attribute cost price
                        } else {
                          $total_calculated_value += ($att_cost_price * $order->products[$i]['qty']); // add the attribute cost price
                        }
                      } elseif ($order->products[$i]['attributes'][$j]['price'] > 0) {
                        //$all_order_details_saved = false; // bail
                        $order_total_reduction += ($order->products[$i]['attributes'][$j]['price'] * $order->products[$i]['qty']); // remove attribute from total as we dont have a att cost price
                      }
                    } // end if($allow_stored_attribute_cost_price_to_be_changed) {

                  } else { // we have attributes but we dont need to worry about attribute cost prices

                    echo '<td class="dataTableContent" valign="top" style="background-color: #EFFCF9;"></td>
		      <td class="dataTableContent" valign="top" align="right" style="background-color: #EFFCF9;"></td>
			  <td class="dataTableContent" valign="top" align="right" style="background-color: #EFFCF9;"></td>
			  <td class="dataTableContent" valign="top" align="right" style="background-color: #EFFCF9;"></td>
			  <td class="dataTableContent" align="right" valign="top" style="background-color: #EFFCF9;"></td>';
                  }

                  echo '</tr>';
                } // if($order->products[$i]['attributes'][$j]['product_attribute_id'] != 0) {

                // Attribute Rows End
              }
            }
            // Attribute Rows End


            if ($order->products[$i]['bundle'] == 'yes') {

              echo '<tr>';
              echo '<td style="background-color: #F7F8F8;"></td>';
              echo '<td colspan="8" class="dataTableContent" valign="top" style="background-color: #F7F8F8;">' . tep_bundle_string((int)$order->products[$i]['id']) . '</td><td class="dataTableContent" valign="top" style="background-color: #FFFFFF;"></td><td colspan="5" class="dataTableContent" valign="top" align="right" style="background-color: #EFFCF9;"></td>';
              echo '</tr>';
            }

            if ($order_total_reduction) {
              $order_total_ex_vat += ((($order->products[$i]['final_price'] - $order_total_reduction) / 1.2) * $order->products[$i]['qty']);
            } else {
              $order_total_ex_vat += (($order->products[$i]['final_price'] / 1.2) * $order->products[$i]['qty']);
            }


            if ($normal_cost_price_update_is_void) {
              ?>
              <script language="javascript">
                <!--
                // disable the normal cost price field, checkbox, and text
                $(document).ready(function() {
                  // change the class so its not added to grand total
                  //$('input.costpriceinput<?php //echo $prod_count; 
                                            ?>').addClass('costpricevoidinput<?php //echo $prod_count; 
                                                                              ?>').removeClass('costpriceinput<?php //echo $prod_count; 
                                                                                                              ?>');
                  //$('input.tradepricediscount<?php //echo $prod_count; 
                                                ?>').addClass('tradepricevoiddiscount<?php //echo $prod_count; 
                                                                                      ?>').removeClass('tradepricediscount<?php //echo $prod_count; 
                                                                                                                          ?>');
                  // disable it so it cant be edited
                  //$('input.costpricevoidinput<?php //echo $prod_count; 
                                                ?>').prop( "disabled", true );
                  //$('input.tradepricevoiddiscount<?php //echo $prod_count; 
                                                    ?>').prop( "disabled", true );
                  // same for other bits..
                  //$('span.cost_price_text<?php //echo $prod_count; 
                                            ?>').hide();

                });
                //
                -->
              </script>
            <?php
            }

            //if (!tep_not_null($order->products[$i]['orders_products_cg_cost']) || $order->products[$i]['orders_products_cg_cost'] == 0) {
            if (!tep_not_null($order->products[$i]['orders_products_cg_cost'])) {

              $all_order_details_saved = false;
            } else { // the normal product order cost price value should be added to the total

              $trade_price_discount_amount = $order->products[$i]['orders_products_cg_cost'] * ($order->products[$i]['orders_products_trade_price_discount'] / 100);
              $total_calculated_value +=  $order->products[$i]['qty'] * ($order->products[$i]['orders_products_cg_cost'] - $trade_price_discount_amount);
            }

            ?>
            <script language="javascript">
              <!--
              // normal fields
              $(document).ready(function() {

                update_totalcostpricespan(<?php echo $prod_count; ?>);

                $('input.costpriceinput<?php echo $prod_count; ?>').on('change paste keyup', function() {
                  update_totalcostpricespan(<?php echo $prod_count; ?>);
                });

                $('input.tradepricediscount<?php echo $prod_count; ?>').on('change paste keyup', function() {
                  update_totalcostpricespan(<?php echo $prod_count; ?>);
                });

              });
              //
              -->
            </script>
          <?php // .$('input.tradepricediscount<?php echo $prod_count;

            $prod_count++;
          }
          ?>
          <tr>
            <td align="right" colspan="9" valign="top">
              <table border="0" cellspacing="0" cellpadding="2">
                <tr>
                  <td align="right" class="smallText">&nbsp;</td>
                  <td align="right" class="smallText">&nbsp;</td>
                </tr>
                <?php
                for ($i = 0, $n = sizeof($order->totals); $i < $n; $i++) {
                  echo '              <tr>' . "\n" .
                    '                <td align="right" class="smallText">' . $order->totals[$i]['title'] . '</td>' . "\n" .
                    '                <td align="right" class="smallText">' . $order->totals[$i]['text'] . '</td>' . "\n" .
                    '              </tr>' . "\n";
                }
                ?>
              </table>

            </td>
            <td align="right" colspan="6" valign="top">


              <table border="0" cellspacing="0" cellpadding="2">
                <tr>
                  <td align="right" class="smallText">&nbsp;</td>
                  <td align="right" class="smallText">&nbsp;</td>
                </tr>
                <tr>
                  <td align="right" class="smallText">Order Total Product Cost:</td>
                  <td align="right" class="smallText"><span class="grandtotal" style="font-weight: bold;"></span></td>
                </tr>
                <tr>
                  <td align="right" class="smallText">Order Total Product Revenue:</td>
                  <td align="right" class="smallText"><span style="font-weight: bold;"><?php echo '&pound;' . number_format((float)$order_total_ex_vat, 2); ?></span></td>
                </tr>
                <tr>
                  <td align="right" class="smallText">&nbsp;</td>
                  <td align="right" class="smallText">&nbsp;</td>
                </tr>
                <tr>
                  <td align="right" class="smallText">Save above values to this order? </td>
                  <td align="right" class="smallText"><?php echo tep_image_submit('button_save.gif', IMAGE_SAVE); ?></td>
                </tr>
                <tr>
                  <td align="right" class="smallText">&nbsp;</td>
                  <td align="right" class="smallText">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="2" align="right" class="smallText">&nbsp;
                    <?php
                    if ($all_order_details_saved) {
                      echo '<div id="details-changed">' . tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) . '&nbsp;&nbsp;All required details saved. </div>';
                    } else {
                      echo tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS) . '&nbsp;&nbsp;Some order cost price details missing.';
                    }
                    ?>
                  </td>
                </tr>
              </table>

              <?php if ($all_order_details_saved) { ?>
                <table border="0" cellspacing="0" cellpadding="2" align="right" style="margin-top: 10px;">
                  <tr>
                    <td class="main" align="right">
                      <?php
                      // product profit ex vat
                      $product_total_profit = ($order_total_ex_vat - $total_calculated_value);

                      echo 'Order Profit: &pound;' . number_format((float)$product_total_profit, 2);
                      ?>
                    </td>
                  </tr>
                  <tr>
                    <td class="main" align="right">
                      <?php

                      // $prepare_costprice = $total_calculated_value;
                      // $selling_price = $order_total_ex_vat;
                      // $margin = tep_round((((($selling_price)-($prepare_costprice))/($selling_price))*100), 0);
                      // $profit = tep_round(($selling_price)-($prepare_costprice), 2);

                      if ($order_total_ex_vat > 0) {
                        $margin = tep_round(((($order_total_ex_vat - $total_calculated_value) / $order_total_ex_vat) * 100), 0);
                      } else {
                        $margin = 0;
                      }

                      echo 'Order Profit Margin: <strong>' . $margin . '%</strong>';

                      ?>
                    </td>
                  </tr>
                </table>
              <?php } ?>
            </td>
          </tr>
        </table>
        </form>
      </td>
    </tr>
    <tr>
      <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
    </tr>
    <tr>
      <td><?php include(DIR_WS_MODULES . '/order_despatch.php'); ?></td>
    </tr>
    <tr>
      <td>

        <table border="0" cellspacing="0" cellpadding="5">
          <tr>
            <td class="main">


              <table border="0" cellspacing="0" cellpadding="5">
                <tr>
                  <td class="main">
                    <table border="1" cellspacing="0" cellpadding="5">
                      <tr>
                        <td class="smallText" align="center"><b><?php echo TABLE_HEADING_DATE_ADDED; ?></b></td>
                        <td class="smallText" align="center"><b><?php echo TABLE_HEADING_CUSTOMER_NOTIFIED; ?></b></td>
                        <td class="smallText" align="center"><b><?php echo TABLE_HEADING_STATUS; ?></b></td>
                        <td class="smallText" align="center"><b><?php echo TABLE_HEADING_COMMENTS; ?></b></td>
                        <td class="smallText" align="center"><strong><?php echo TABLE_HEADING_TRACKING_STATUS; ?></strong></td>
                        <td class="smallText" align="center"><strong>Location</strong></td>
                        <td class="smallText" align="center"><strong>User</strong></td>
                      </tr>
                      <?php
                      // also similar found in includes/classes/tracking_module.php - id = 0 different
                      $location_array = array(array('id' => '0', 'text' => ''), array('id' => '1', 'text' => 'London'), array('id' => '2', 'text' => 'Norfolk'), array('id' => '3', 'text' => 'Chester'), array('id' => '4', 'text' => 'Liverpool'), array('id' => '5', 'text' => 'Knutsford'), array('id' => '6', 'text' => 'Mayfair'), array('id' => '7', 'text' => 'L. Stashed'));

                      $orders_history_query = tep_db_query("select orders_status_history_id, orders_status_id, date_added, customer_notified, comments, tracking_id, location_id from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . tep_db_input($oID) . "' order by date_added");
                      if (tep_db_num_rows($orders_history_query)) {
                        while ($orders_history = tep_db_fetch_array($orders_history_query)) {
                          echo '          <tr>' . "\n" .
                            '            <td class="smallText" align="center">' . tep_datetime_short($orders_history['date_added']) . '</td>' . "\n" .
                            '            <td class="smallText" align="center">';
                          if ($orders_history['customer_notified'] == '1') {
                            echo tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) . "</td>\n";
                          } else {
                            echo tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS) . "</td>\n";
                          }
                          echo '            <td class="smallText">' . $orders_status_array[$orders_history['orders_status_id']] . '</td>' . "\n" .
                            '            <td class="smallText">' . nl2br(html_entity_decode(tep_db_output($orders_history['comments']))) . '&nbsp;</td>' . "\n" .
                            '            <td class="smallText" align="center">' . $tracking->display_tracking_link($orders_history['tracking_id']) . '</td>' . "\n" .
                            '            <td class="smallText">' . $location_array[$orders_history['location_id']]['text'] . '</td>' . "\n";

                            $user_history_query = tep_db_query("select a.user_name from " . TABLE_ADMINISTRATORS . " a, orders_user_log oul where oul.orders_status_history_id = '" . $orders_history['orders_status_history_id'] . "' and oul.admin_id = a.id ");
                            if (tep_db_num_rows($user_history_query)) {
                              $user_history = tep_db_fetch_array($user_history_query);
                              echo '<td class="smallText">' . $user_history['user_name'] . '</td>' . "\n";
                            } else {
                              echo '<td class="smallText"></td>' . "\n";
                            }

                            echo '</tr>' . "\n";

                        }
                      } else {
                        echo '          <tr>' . "\n" .
                          '            <td class="smallText" colspan="6">' . TEXT_NO_ORDER_HISTORY . '</td>' . "\n" .
                          '          </tr>' . "\n";
                      }
                      ?>
                    </table>
                  </td>
                </tr>
                <tr>
                  <td class="main"><br><strong>Comments:</strong></td>
                </tr>
                <tr>
                  <td>
                    <script language="javascript">
                      <!--
                      var usrdate = '';

                      function updateComment(obj, statusnum) {
                        var textareas = document.getElementsByTagName('textarea');
                        var myTextarea = textareas.item(0); {
                          myTextarea.value = obj;
                        }
                        var selects = document.getElementsByTagName('select');
                        var theSelect = selects.item(0);
                        theSelect.selectedIndex = statusnum;

                        return false;

                      }

                      function killbox() {
                        var box = document.getElementsByTagName('textarea');
                        var killbox = box.item(0);
                        killbox.value = '';
                        return false;

                      }

                      $(document).ready(function() {

                        $("select[name='status']").change(function() {
                          if ($(this).val() == '5' || $(this).val() == '9') {
                            $("#refund-message").show();
                          }
                        });

                        $("#courier-select").change(function() {
                          $("select[name='status']").val('4');
                        });

                        $("#cbutton-dispatched").click(function() {
                          $("#courier-select").val('1'); // set dropdown
                          $('.courier').hide(); // remove previous
                          $('#1').show(); // show new royal mail tracking box
                          $("select[name='status']").val('4'); // set order status to dispatched
                        });

                        <?php
                        // Laura 8 / Amy 19 / Imogen 11 / Kim 38 / Billie 54 / Sharon 60 / Olly 34 / Jess 43 / Chris 24 / Hayley 28
                        if ($admin['id'] == '8' || $admin['id'] == '19' || $admin['id'] == '11' || $admin['id'] == '38' || $admin['id'] == '54' || $admin['id'] == '60' || $admin['id'] == '34' || $admin['id'] == '43' || $admin['id'] == '24' || $admin['id'] == '28') {
                        ?>
                          $("select[name='location_id']").val('2');
                        <?php } ?>

                      });

                      //
                      -->
                    </script>
                    <?php
                    foreach ($bus_cbuttons as $CBName => $CBValue) {
                      if ($CBName == 'Dispatched') {
                        echo '<button class="cbutton" id="cbutton-dispatched" onClick="return updateComment(\'' . $CBValue . '\')">' . $CBName . '</button>&nbsp;';
                      } else {
                        echo '<button class="cbutton" onClick="return updateComment(\'' . $CBValue . '\')">' . $CBName . '</button>&nbsp;';
                      }
                    }
                    ?>
                    &nbsp;&nbsp;<button class="cbutton" onClick="return killbox();"><?php echo BUS_CBUTTON_RESET; ?></button>
                  </td>
                </tr>
                <tr><?php echo tep_draw_form('status', FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=update_order'); ?>
                  <td class="main"><?php echo tep_draw_textarea_field('comments', 'soft', '60', '5'); ?></td>
                </tr>
                <tr>
                  <td>
                    <div id="refund-message" style="line-height: 25px;"><strong>When refunding or cancelling please do one of the following :-</strong><br />
                      <span style="font-weight: 400; color: #222;">- Delete the products via the order editor (this will automatically remove any points earned)<br />
                      - Keep the products and delete the appropriate amount of points earned via the order editor.</span><br /><br />
                      <?php echo tep_draw_button('Edit Order', 'document', tep_href_link(FILENAME_ORDERS_EDIT, 'oID=' . $_GET['oID']), null); ?>
                    </div>
                    <?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?>
                  </td>
                </tr>
                <tr>
                  <td class="main"><strong>Update Options:</strong></td>
                </tr>
                <!-- tracking_module form -->
                <?php $tracking->display_form(); ?>
                <!-- end tracking_module form -->
                <tr>
                  <td class="main">
                    <strong>Order Status:</strong> <?php echo tep_draw_pull_down_menu('status', $orders_statuses, $order->info['orders_status']); ?>
                  </td>
                </tr>
                <tr>
                  <td class="main">
                    <label for="notify"><strong>Notify Customer via email:</strong></label> <input type="checkbox" name="notify" id="notify" checked />
                    <label for="notify_comments"><strong>+ Add Comments:</strong></label> <input type="checkbox" name="notify_comments" id="notify_comments" checked />
                    <label for="tracking_per_product"><strong>+ Show Tracking Info<?php if ($show_product_tracking) { ?> (incl. products if selected)<?php } ?>:</strong></label> <input type="checkbox" name="tracking_per_product" id="tracking_per_product" checked />
                  </td>
                </tr>
                <?php if ($show_sms) { ?>
                  <tr>
                    <td class="main"><label for="tracking_sms"><strong>Send customer tracking SMS:</strong></label> <input type="checkbox" name="tracking_sms" id="tracking_sms" /></td>
                  </tr>
                <?php } ?>
                <tr>
  <!-- <td colspan="2">
    <table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td class="main" width="25%"><strong>Picked By:</strong></td>
        <td class="main">
          <select name="picked_by" id="picked_by">
            <option value="">Select Staff</option>
            <?php
            foreach ($staff_array as $staff_id => $staff_name) {
              echo '<option value="' . $staff_id . '"' . 
                   ($order_info['picked_by'] == $staff_id ? ' selected' : '') . '>' . 
                   $staff_name . '</option>';
            }
            ?>
          </select>
          <?php if ($order_info['picked_date']) echo ' - ' . tep_datetime_short($order_info['picked_date']); ?>
        </td>
      </tr>
      <tr>
        <td class="main"><strong>Packed By:</strong></td>
        <td class="main">
          <select name="packed_by" id="packed_by">
            <option value="">Select Staff</option>
            <?php
            foreach ($staff_array as $staff_id => $staff_name) {
              echo '<option value="' . $staff_id . '"' . 
                   ($order_info['packed_by'] == $staff_id ? ' selected' : '') . '>' . 
                   $staff_name . '</option>';
            }
            ?>
          </select>
          <?php if ($order_info['packed_date']) echo ' - ' . tep_datetime_short($order_info['packed_date']); ?>
        </td>
      </tr>
      <tr>
        <td class="main"><strong>Checked By:</strong></td>
        <td class="main">
          <select name="checked_by" id="checked_by">
            <option value="">Select Staff</option>
            <?php
            foreach ($staff_array as $staff_id => $staff_name) {
              echo '<option value="' . $staff_id . '"' . 
                   ($order_info['checked_by'] == $staff_id ? ' selected' : '') . '>' . 
                   $staff_name . '</option>';
            }
            ?>
          </select>
          <?php if ($order_info['checked_date']) echo ' - ' . tep_datetime_short($order_info['checked_date']); ?>
        </td>
      </tr>
      <tr>
        <td class="main"><strong>Checked By 2:</strong></td>
        <td class="main">
          <select name="checked_2_by" id="checked_by">
            <option value="">Select Staff</option>
            <?php
            foreach ($staff_array as $staff_id => $staff_name) {
              echo '<option value="' . $staff_id . '"' . 
                   ($order_info['checked_2_by'] == $staff_id ? ' selected' : '') . '>' . 
                   $staff_name . '</option>';
            }
            ?>
          </select>
          <?php if ($order_info['checked_2_date']) echo ' - ' . tep_datetime_short($order_info['checked_2_date']); ?>
        </td>
      </tr>
      <tr>
        <td class="main"><strong>Weight:</strong></td>
        <td class="main">
          <input type="text" name="weight" value="<?php echo $order_info['weight']; ?>" size="10">
        </td>
      </tr>
      
      <tr>
        <td class="main"><strong>Packing Notes:</strong></td>
        <td class="main">
          <textarea name="packing_notes" rows="5" cols="50"><?php echo $order_info['packing_notes']; ?></textarea>
        </td>
      </tr>
    </table>
  </td> -->
</tr>

            <script>
            $(document).ready(function() {
              function updateOrderInfo(field, value) {
                $.ajax({
                  url: 'edit_orders_ajax.php',
                  type: 'POST',
                  data: {
                    action: 'update_order_info',
                    order_id: <?php echo (int)$oID; ?>,
                    field: field,
                    value: value
                  },
                  success: function(response) {
                    // Başarılı güncelleme sonrası tarih bilgisini güncelle
                    location.reload();
                  }
                });
              }

              $('#picked_by, #packed_by, #checked_by, #checked_2_by').change(function() {
                updateOrderInfo($(this).attr('name'), $(this).val());
              });

              $('input[name="weight"]').change(function() {
                updateOrderInfo('weight', $(this).val());
              });
              $('textarea[name="packing_notes"]').change(function() {
                updateOrderInfo('packing_notes', $(this).val());
              });
            });
            </script>
                <tr>
                  <td>
                    <?php echo tep_draw_button(IMAGE_UPDATE, 'disk', null, 'primary'); ?>
                  </td>
                  </form>
                </tr>
              </table>

            </td>
            <td valign="top">
              <?php if ($customs_warning) { ?>
                <div class="main" style="position: absolute; top: 175px; right: 10px; font-weight: 700; background-color: #990000; color: #FFF; padding: 7px; font-size: 16px; text-align: center;">Warning: Not Accepted Customs Risk.</div>
              <?php } ?>
              <?php if ($lighter_item) { ?>
                <div class="main" style="position: absolute; top: 175px; right: 10px; font-weight: 700; background-color: #990000; color: #FFF; padding: 7px; font-size: 16px; text-align: center;">Warning: Shipment contains lighter.</div>
              <?php } ?>
              <?php if ($butane_gas_item) { ?>
                <div class="main" style="position: absolute; top: 175px; right: 10px; font-weight: 700; background-color: #990000; color: #FFF; padding: 7px; font-size: 16px; text-align: center;">Warning: Shipment contains Butane gas.</div>
              <?php } ?>
              <?php if ($cadogan_warning) { ?>
                <div class="main" style="position: absolute; top: 175px; right: 10px; font-weight: 700; background-color: #990000; color: #FFF; padding: 7px; font-size: 16px; text-align: center;">PLEASE NOTIFY ADAM OF CLICK & COLLECT BEFORE SHIPPING</div>
              <?php } ?>
              <!-- START OF DELIVERY COMMENTS -->
              <table border="0" cellspacing="0" cellpadding="5">
                <tr>
                  <td class="main">
                    <table border="1" cellspacing="0" cellpadding="5">
                      <tr>
                        <td class="smallText" align="center"><b><?php echo TABLE_HEADING_DATE_ADDED; ?></b></td>
                        <td class="smallText" align="center"><b>Delivery Notes</b></td>
                      </tr>
                      <?php
                      $delivery_comments_query = tep_db_query("select orders_delivery_comments_id, date_added, comments from orders_delivery_comments where orders_id = '" . tep_db_input($oID) . "' order by date_added");
                      if (tep_db_num_rows($delivery_comments_query)) {
                        while ($delivery_comments = tep_db_fetch_array($delivery_comments_query)) {
                          echo '          <tr>' . "\n" .
                            '            <td class="smallText" align="center">' . tep_datetime_short($delivery_comments['date_added']) . '</td>' . "\n" .
                            // '            <td class="smallText">' . nl2br(html_entity_decode(tep_db_output($delivery_comments['comments']))) . '&nbsp;</td>' . "\n" .
                            '            <td class="smallText">' . nl2br(tep_db_output($delivery_comments['comments'])) . '&nbsp;</td>' . "\n" .
                            '          </tr>' . "\n";
                        }
                      } else {
                        echo '          <tr>' . "\n" .
                          '            <td class="smallText" colspan="3">No staff delivery notes</td>' . "\n" .
                          '          </tr>' . "\n";
                      }
                      ?>
                    </table>
                  </td>
                </tr>
                <tr>
                  <td class="main"><br><b>Staff Delivery Notes</b></td>
                </tr>
                <tr>
                  <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '5'); ?></td>
                </tr>
                <tr><?php echo tep_draw_form('delivery_status', FILENAME_ORDERS, tep_get_all_get_params(array('action')) . 'action=update_delivery_notes'); ?>
                  <td class="main"><?php echo tep_draw_textarea_field('delivery_comments', 'soft', '60', '5'); ?></td>
                </tr>
                <tr>
                  <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                </tr>
                <tr>
                  <td><?php echo tep_draw_button(IMAGE_UPDATE, 'disk', null, 'primary'); ?></td>
                  </form>
                </tr>
<?php 
$order_location_summary_array = array(
  array('id' => '0', 'text' => 'London'),
  array('id' => '2', 'text' => 'Norfolk'),
  array('id' => '6', 'text' => 'Chester'),
  array('id' => '7', 'text' => 'Liverpool'),
  array('id' => '999', 'text' => 'Test Account')
);
?>
                <tr>
                  <td class="main"><br><b>Create RM Label</b>
<?php 

$label_check_query = tep_db_query("select a.user_name, rm.date_added from orders_rm_labels rm left join " . TABLE_ADMINISTRATORS . " a on (rm.admin_id = a.id) where rm.orders_id = '" . tep_db_input($oID) . "'");
while ($label_check = tep_db_fetch_array($label_check_query)) {
  echo '&nbsp;&nbsp;&nbsp;&nbsp;(' . $label_check['user_name'] . ' created a label ' . tep_date_short($label_check['date_added']) . ')';
}
                
       ?>         
                </td>
                </tr>
                <tr>
                  <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '5'); ?></td>
                </tr>
                <tr><?php echo tep_draw_form('vat', 'cron_royalmail_labels.php', '', 'get'); ?>
                  <td class="main"><?php echo tep_draw_pull_down_menu('orders_location', $order_location_summary_array, $current_orders_location, 'style="width: 160px;" id="orders_location"'); ?></td>
                </tr>
                <tr>
                  <td class="main"><?php echo tep_draw_input_field('rm_grams', '', 'size="10" style="text-align: left;"'); ?> grams (total weight)</td>
                </tr>                
                <tr>
                  <td><?php echo tep_draw_hidden_field('oID', $oID) . tep_draw_hidden_field('aID', $admin['id']); ?><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                </tr>
                <tr>
                  <td><?php echo tep_draw_button('Create Label', 'disk', null, 'primary'); ?></td>
                  </form>
                </tr> 

              </table>

            </td>
          </tr>
        </table>

      </td>
    </tr>
    <tr>
      <td colspan="2" align="right">

        <?php echo tep_draw_button(IMAGE_EDIT, 'document', tep_href_link(FILENAME_ORDERS_EDIT, 'oID=' . $_GET['oID']), null) . tep_draw_button(IMAGE_ORDERS_INVOICE, 'document', tep_href_link(FILENAME_ORDERS_INVOICE, 'oID=' . $_GET['oID']), null, array('newwindow' => true)) . tep_draw_button(IMAGE_ORDERS_PACKINGSLIP, 'document', tep_href_link(FILENAME_ORDERS_PACKINGSLIP, 'oID=' . $_GET['oID']), null, array('newwindow' => true)) . tep_draw_button(IMAGE_BACK, 'triangle-1-w', tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('action')))); ?>

      </td>
    </tr>
    <script language="javascript">
      <!--
      $(document).ready(function() {
        $('span.grandtotal').html($("input[class^='totalcostpricespan']").sumValues()); // add up the spans and the delivery cost price
        $("input[class^='costpriceinput']").bind('keyup', function() {
          $('span.grandtotal').html($("input[class^='totalcostpricespan']").sumValues()); // add up the spans and the delivery cost price
          $('#details-changed').html('<?php echo tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS); ?> Some details have changed since last save');
        });
        $("input[class^='tradepricediscount']").bind('keyup', function() {
          $('span.grandtotal').html($("input[class^='totalcostpricespan']").sumValues()); // add up the spans and the delivery cost price
          $('#details-changed').html('<?php echo tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS); ?> Some details have changed since last save');
        });
        $("input[class^='costpricetotalinputattribute']").bind('keyup', function() {
          $('span.grandtotal').html($("input[class^='totalcostpricespan']").sumValues()); // add up the spans and the delivery cost price
          $('#details-changed').html('<?php echo tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS); ?> Some details have changed since last save');
        });
      });

      //
      -->
    </script>
    <?php
    if (tep_not_null($order->info['cc_type']) || tep_not_null($order->info['cc_owner']) || tep_not_null($order->info['cc_number'])) {
    ?>
      <tr>
        <td class="main-big"><?php echo ENTRY_CVV_NUMBER; ?> <?php echo $order->info['cvv_number']; ?></td>
      </tr>
    <?php
    }
    ?>
  <?php
  } else {
  ?>
    <tr>
      <td width="100%">
        <table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" width="150px"><?php echo HEADING_TITLE; ?></td>
            <td align="right">
              <?php echo tep_draw_form('orders', FILENAME_ORDERS, '', 'get'); ?>
              <table border="0" width="100%" cellspacing="0" cellpadding="0">
                <tr>
                  <td class="smallText" align="right">

                    <?php echo tep_draw_form('product_search', FILENAME_ORDERS, '', 'get'); ?>
                    <?php
                    $onfocus = ' onfocus="this.value=\'\';"';
                    echo 'Product Search: ' . tep_draw_input_field('p', '', "size=\"32\"$onfocus");
                    ?>
                    </form>
                    &nbsp;&nbsp;
                    <?php echo tep_draw_form('customer_search', FILENAME_ORDERS, '', 'get'); ?>
                    <?php
                    $onfocus = ' onfocus="this.value=\'\';"';
                    echo 'Search (order id, customer name, email or postcode): ' . tep_draw_input_field('q', '', "size=\"32\"$onfocus");
                    ?>
                    </form>
                  </td>
                </tr>
                <tr>
                  <td class="smallText" align="right">

                    <form method="get" action="<?php echo basename($_SERVER['PHP_SELF']) . '?date1=' . $date1 . '&date2=' . $date2; ?>" name="dailyreportform">

                      <table border="0" cellspacing="5px" cellpadding="0">
                        <tr>
                          <td width="150" class="main"><?php echo 'Select Order Date Range: '; ?></td>
                          <td width="150">
                            <script language="javascript">
                              cal11.writeControl();
                              cal11.dateFormat = "yyyy-MM-dd";
                              dailyreportform.date1.value = "<?php echo $date1; ?>"
                            </script>
                          </td>
                          <td width="26" class="main">to</td>
                          <td width="150" class="main">
                            <script language="javascript">
                              cal12.writeControl();
                              cal12.dateFormat = "yyyy-MM-dd";
                              dailyreportform.date2.value = "<?php echo $date2; ?>"
                            </script>
                          </td>
                          <td width="26" class="main"><input type="hidden" name="action" value="dailyreportaction"><input type="submit"> </td>
                        </tr>
                      </table>
                      <?php

                      if ($admin['id'] == 35) {
                        $order_type = 6;
                      // } elseif ($admin['id'] == 47) { // Sara Chester & Liverpool
                      // $order_type = 8;
                      } elseif ($admin['id'] == 36) {
                        $order_type = 7;
                      } elseif ($admin['id'] == 34) { // Oliver
                        $order_type = 2;
                      } else {
                        $order_type = $_GET['order_type'];
                      }

                      ?>
                      <?php echo 'Order type: ' . tep_draw_pull_down_menu('order_type', array_merge(array(array('id' => '', 'text' => TEXT_ALL_ORDERS)), $type_statuses), $order_type, 'onChange="this.form.submit();"'); ?>

                      <?php echo 'Old Shipping: ' . tep_draw_pull_down_menu('old_delivery_type', array_merge(array(array('id' => '', 'text' => 'All Shipping')), $old_delivery_statuses), '', 'onChange="this.form.submit();"'); ?>

                      <?php echo 'New Shipping: ' . tep_draw_pull_down_menu('delivery_type', array_merge(array(array('id' => '', 'text' => 'All Shipping')), $delivery_statuses), '', 'onChange="this.form.submit();"'); ?>

                      <?php echo 'Payment type: ' . tep_draw_pull_down_menu('payment_type', array_merge(array(array('id' => '', 'text' => 'All Payments')), $payment_statuses), '', 'onChange="this.form.submit();"'); ?>

                      <?php echo HEADING_TITLE_STATUS . ' ' . tep_draw_pull_down_menu('status', array_merge(array(array('id' => '', 'text' => TEXT_ALL_ORDERS), array('id' => '99', 'text' => '*Multiple Dispatch')), $orders_statuses), '', 'onChange="this.form.submit();"'); ?>

                    </form>

                    <?php echo tep_draw_form('source', FILENAME_ORDERS, '', 'get'); ?>
                    <?php echo 'Source:' . ' ' . tep_get_source_list('source', (DISPLAY_REFERRAL_OTHER == 'true' || (tep_session_is_registered('referral_id') && tep_not_null($referral_id)) ? true : false), (tep_session_is_registered('referral_id') && tep_not_null($referral_id)) ? '9999' : '', 'onChange="this.form.submit();"'); ?>
                    </form>

                  </td>
                </tr>
                <tr>
                  <td class="smallText" align="right">
                    <div style="float: right;">

                      <?php echo tep_draw_form('vat', FILENAME_ORDERS, '', 'get'); ?>
                      <?php

                      $all_on = '';
                      $off_on = '';
                      $coffee_on = '';
                      $both_on = '';
                      $norfolk_on = '';

                      if (isset($_GET['vat'])) {
                        if ($_GET['vat'] == 'all') {
                          $all_on = 'checked="checked"';
                        } elseif ($_GET['vat'] == 'off') {
                          $off_on = 'checked="checked"';
                        } elseif ($_GET['vat'] == 'coffee') {
                          $coffee_on = 'checked="checked"';
                        } elseif ($_GET['vat'] == 'both') {
                          $both_on = 'checked="checked"';
                        } elseif ($_GET['vat'] == 'norfolk') {
                          $norfolk_on = 'checked="checked"';
                        }
                      } else {
                        $all_on = 'checked="checked"';
                      }

                      $coffee_cat_text = 'All orders that contain products with the Tax Class: <strong>No VAT</strong>';

                      echo '<input type="radio" name="vat" value="all" ' . $all_on . ' onChange="this.form.submit();"/>&nbsp;&nbsp;All Orders&nbsp;&nbsp;
				      <input type="radio" name="vat" value="off" ' . $off_on . ' onChange="this.form.submit();"/>&nbsp;&nbsp;Non UK&nbsp;&nbsp;
				      <input type="radio" name="vat" value="coffee" ' . $coffee_on . ' onChange="this.form.submit();"/>&nbsp;&nbsp;Contains Coffee';

                      ?>
                      <img src="images/icons/comment2.gif" onMouseover="ddrivetip('<?php echo '' . $coffee_cat_text . ''; ?>', 'white', 300)" ; onMouseout="hideddrivetip()" align="top" border="0">
                      <?php echo '<input type="radio" name="vat" value="both" ' . $both_on . ' onChange="this.form.submit();"/>&nbsp;&nbsp;Coffee &amp; Non UK&nbsp;&nbsp;';

                      echo '<input type="radio" name="vat" value="norfolk" ' . $norfolk_on . ' onChange="this.form.submit();"/>&nbsp;&nbsp;Norfolk&nbsp;&nbsp;';

                      ?>
                      </form>
                    </div>
                    <div style="float: right; width: 480px; text-align: left;">
                      <table>
                        <td><?php echo tep_image(DIR_WS_ICONS . 'order_london.png', 'London Order'); ?></td>
                        <td>London Order </td>
                        <td><?php echo tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order'); ?></td>
                        <td>Norfolk / Alcohol Order </td>
                        <td><?php echo tep_image(DIR_WS_ICONS . 'order_chester.png', 'Chester Order'); ?></td>
                        <td>Chester Order </td>
                        <td><?php echo tep_image(DIR_WS_ICONS . 'order_liverpool.png', 'Liverpool Order'); ?></td>
                        <td>Liverpool Order </td>
                      </table>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top">
              <?php
              echo tep_draw_form('UpdateStatus', FILENAME_ORDERS, tep_get_all_get_params()); ?>
              <script language="javascript">
                function checkAll() {
                  var el = document.getElementsByName('update_oID[]')
                  for (i = 0; i < el.length; i++) {
                    el[i].checked = true;
                  }
                }

                function uncheckAll() {
                  var el = document.getElementsByName('update_oID[]')
                  for (i = 0; i < el.length; i++) {
                    el[i].checked = false;
                  }
                }
              </script>
              <table border="0" width="100%" cellspacing="0" cellpadding="2">
                <tr class="dataTableHeadingRow">
                  <td></td>
                  <td></td>
                  <td></td>
                  <td class="dataTableHeadingContent" align="center"><?php echo 'Order'; ?></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td class="dataTableHeadingContent" width="200"><?php echo TABLE_HEADING_CUSTOMERS; ?></td>
                  <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ORDER_TOTAL; ?></td>
                  <td class="dataTableHeadingContent" align="right"></td>
                  <td class="dataTableHeadingContent" width="130" align="center"><?php echo TABLE_HEADING_DATE_PURCHASED; ?></td>
                  <td class="dataTableHeadingContent" width="70" align="right">Prod. Total Ex Vat</td>
                  <td class="dataTableHeadingContent" align="right">Cost</td>
                  <td class="dataTableHeadingContent" align="right">Profit</td>
                  <td class="dataTableHeadingContent" align="right">Margin</td>
                  <td class="dataTableHeadingContent" width="110" align="center"><?php echo 'Source'; ?></td>
                  <td class="dataTableHeadingContent" width="110" align="center">Country</td>
                  <td class="dataTableHeadingContent" width="170" align="center">Order Review</td>
                  <td class="dataTableHeadingContent" align="center">Fraud</td>
                  <td class="dataTableHeadingContent" align="center">Amount</td>
                  <td class="dataTableHeadingContent" width="190" align="right"><?php echo TABLE_HEADING_STATUS; ?>&nbsp;&nbsp;</td>
                  <td class="dataTableHeadingContent" width="20" align="right"></td>
                  <td class="dataTableHeadingContent" width="20" align="right"></td>
                </tr>
                <?php

                //--------------------------------------
                // #### HURL CUSTOMER ORDER SEARCH ####
                // ### PRECONDITION: need order details based upon a customers first name and or last name
                // ### POSTCONDITION: check for the get var custName -- new to this contrib
                // if exists create an sql query based upon the customer name
                // passed from said get var
                //-------------------------------------
                if (isset($_GET['cID'])) {

                  $cID = tep_db_prepare_input($_GET['cID']);
                  $orders_query_raw = "select o.orders_id, o.customers_id, o.customers_name, o.customers_id, o.payment_method, o.date_purchased, o.last_modified, o.currency, o.currency_value, o.delivery_country, s.orders_status_name, o.third_man from " . TABLE_ORDERS . " o, " . TABLE_ORDERS_STATUS . " s where o.customers_id = '" . (int)$cID . "' and o.orders_status = s.orders_status_id " . (!is_null($search_query) ? $search_query : '') . " order by orders_id DESC";

                  //	} elseif (isset($_GET['source']) && is_numeric($_GET['source']) && ($_GET['source'] > 0)) {

                  //      $source = tep_db_prepare_input($_GET['source']);

                  // $orders_query_raw = "(select o.orders_id, o.customers_id, o.customers_name, o.payment_method, o.date_purchased, o.last_modified, o.currency, o.currency_value, o.delivery_country, i.customers_info_source_id from " . TABLE_ORDERS . " o, " . TABLE_CUSTOMERS_INFO . " i where o.customers_id = i.customers_info_id and i.customers_info_source_id = '" . (int)$source . "' order by o.orders_id DESC) UNION (select o.orders_id, o.customers_id, o.customers_name, o.payment_method, o.date_purchased, o.last_modified, o.currency, o.currency_value, o.delivery_country, e.express_info_source_id from " . TABLE_ORDERS . " o, " . TABLE_SOURCES_EXPRESS . " e where o.orders_id = e.express_orders_id and e.express_info_source_id = '" . (int)$source . "' order by o.orders_id DESC) order by orders_id DESC";

                } else {

                  $table_str .= '';
                  $where_str .= '';
                  $vat_query = false;
                  $coffee_query = false;
                  $coffee_vat_query = false;

                  if (isset($_GET['vat']) && ($_GET['vat'] == 'off' or $_GET['vat'] == 'both')) {
                    $vat_query = 'o.delivery_country_id != 0 and o.delivery_country_id not in (222)';
                  }

                  if (isset($_GET['vat']) && ($_GET['vat'] == 'coffee' or $_GET['vat'] == 'both')) {

                    $coffee_check_query = tep_db_query("select distinct p.products_id from " . TABLE_PRODUCTS . " p where p.products_tax_class_id = '4'");
                    $i = 0;

                    while ($coffee_check_total = tep_db_fetch_array($coffee_check_query)) {
                      if ($i != 0) {
                        $str_coffee_values .=  ',';
                      }
                      $str_coffee_values .= $coffee_check_total['products_id'];
                      $i++;
                    }

                    if (!$date_set) {
                      date_default_timezone_set('Europe/London');
                      $date1 = date('Y-m-d', strtotime('-6 months'));
                      $where_str .= "DATE(o.date_purchased)>=DATE(\"$date1%\") and ";
                      $date_set = true;
                    }

                    $select_str .= 'distinct op.orders_id, ';
                    $table_str .= TABLE_ORDERS_PRODUCTS . ' op, ';
                    $where_str .= 'o.orders_id = op.orders_id and ';

                    if ($str_coffee_values) {
                      $coffee_query = 'op.products_id in (' . $str_coffee_values . ')';
                    } else {
                      $coffee_query = 'op.products_id = 9999999';
                    }
                  }

                  if (isset($_GET['vat']) && ($_GET['vat'] == 'norfolk')) {
                    $where_str .= "(o.orders_location = '1' or o.orders_location = '3' or o.orders_location = '5') and ";
                  }

                  if (isset($_GET['status']) && is_numeric($_GET['status']) && ($_GET['status'] > 0)) {
                    $status = tep_db_prepare_input($_GET['status']);
                    if ($status == '99') {
                      $join_str .= "left join " . TABLE_ORDERS_STATUS_HISTORY . " osh on (o.orders_id = osh.orders_id) ";
                      $where_str .= "osh.orders_status_id = '4' and ";
                      $group_str .= "GROUP BY o.orders_id HAVING COUNT(o.orders_id) > 1 ";
                    } else {
                      $where_str .= "s.orders_status_id = '" . (int)$status . "' and ";
                    }
                  }

                  if (isset($_GET['date1']) && $_GET['date1'] != '') {
                    $where_str .= "DATE(o.date_purchased)>=DATE(\"$date1%\") and ";
                    $date_set = true;
                  }

                  if (isset($_GET['date2']) && $_GET['date2'] != '') {
                    $where_str .= "DATE(o.date_purchased)<=DATE(\"$date2%\") and ";
                    $date_set = true;
                  }

                  // 0 Online Order - London
                  // 1 Norfolk Shop Order - London
                  // 2 Online Order - Norfolk
                  // 3 Norfolk Shop Order - Norfolk
                  // 4 Online Order - Alcohol
                  // 5 Norfolk Shop Order - Alcohol

                  if ((isset($_GET['order_type']) && is_numeric($_GET['order_type']) && ($_GET['order_type'] > 0)) || is_numeric($order_type)) {
                    if ($order_type == '1') { // london
                      $where_str .= "(o.orders_location = '0' or o.orders_location = '1') and ";
                    }
                    if ($order_type == '2') { // norfolk & alcohol
                      $where_str .= "(o.orders_location = '2' or o.orders_location = '3' or o.orders_location = '4' or o.orders_location = '5') and ";
                    }
                    if ($order_type == '3') { // alcohol
                      $where_str .= "(o.orders_location = '4' or o.orders_location = '5') and ";
                    }
                    if ($order_type == '4') {
                      $where_str .= "o.shipping_module like '%mzmta%' and ";
                    }
                    if ($order_type == '5') {
                      $where_str .= "o.shipping_module like '%locker%' and ";
                    }
                    if ($order_type == '6') {
                      $where_str .= "o.orders_location = '6' and ";
                    }
                    if ($order_type == '7') {
                      $where_str .= "o.orders_location = '7' and ";
                    }
                    if ($order_type == '8') {
                      $where_str .= "(o.orders_location = '6' or o.orders_location = '7') and ";
                    }
                    if ($order_type == '99') {
                      $where_str .= "(o.orders_location = '2' or o.orders_location = '3' or o.orders_location = '4' or o.orders_location = '5') and o.subscription_status != '1' and ";
                    }
                    if ($order_type == '66') {
                      $select_str .= "distinct op.orders_id, ";
                      $table_str .= TABLE_ORDERS_PRODUCTS . " op, " . TABLE_PRODUCTS . " p, ";
                      $where_str .= "o.orders_id = op.orders_id and op.products_id = p.products_id and p.products_free_shipping = '5' and ";

                      if (!$date_set) {
                        date_default_timezone_set('Europe/London');
                        $date1 = date('Y-m-d', strtotime('-6 months'));
                        $where_str .= "DATE(o.date_purchased)>=DATE(\"$date1%\") and ";
                        $date_set = true;
                      }
                    }
                  }

                  if (!$date_set && !isset($_GET['q'])) {
                    date_default_timezone_set('Europe/London');
                    $date1 = date('Y-m-d', strtotime('-3 year'));
                    $where_str .= "DATE(o.date_purchased)>=DATE(\"$date1%\") and ";
                    $date_set = true;
                  }

                  if (isset($_GET['old_delivery_type']) && is_numeric($_GET['old_delivery_type']) && ($_GET['old_delivery_type'] > 0)) {

                    $old_delivery_type = $_GET['old_delivery_type'];
                    if ($old_delivery_type == '2') {
                      $where_str .= "o.delivery_country_id = '222' and o.shipping_module like '%mzmt_table2%' and ";
                    }
                    if ($old_delivery_type == '3') {
                      $where_str .= "o.delivery_country_id = '222' and o.shipping_module like '%mzmt_table3%' and ";
                    }
                    if ($old_delivery_type == '4') {
                      $where_str .= "o.delivery_country_id = '222' and o.shipping_module like '%mzmt_table4%' and ";
                    }

                    $where_str .= "DATE(o.date_purchased)<=DATE(\"2019-06-11%\") and ";
                  } elseif (isset($_GET['delivery_type']) && is_numeric($_GET['delivery_type']) && ($_GET['delivery_type'] > 0)) {

                    $delivery_type = $_GET['delivery_type'];
                    if ($delivery_type == '1') {
                      $where_str .= "o.delivery_country_id = '222' and (o.shipping_module like '%mzmt_table1%' or o.shipping_module like '%mzmt_table3%') and ";
                    }
                    if ($delivery_type == '2') { // Priority & Express
                      $where_str .= "o.delivery_country_id = '222' and (o.shipping_module like '%mzmt_table2%' or o.shipping_module like '%mzmt_table4%' or o.shipping_module like '%lighter_lighteruk%' or o.shipping_module like '%colibri_lighter%' or o.shipping_module like '%gas_gas%') and ";
                    }
                    if ($delivery_type == '3') { // Next Day & Courier
                      $where_str .= "o.delivery_country_id = '222' and (o.shipping_module like '%mzmt_table5%' or o.shipping_module like '%mzmt_table7%' or o.shipping_module like '%lighter_lighternd%' or o.shipping_module like '%lighter_lightersat%') and ";
                    }
                    if ($delivery_type == '4') {
                      $where_str .= "o.delivery_country_id = '222' and (o.shipping_module like '%mzmt_table6%' or o.shipping_module like '%mzmt_table8%') and ";
                    }

                    $where_str .= "DATE(o.date_purchased)>=DATE(\"2019-06-12%\") and ";
                  }

                  if (isset($_GET['payment_type']) && is_numeric($_GET['payment_type']) && ($_GET['payment_type'] > 0)) {
                    $payment_type = $_GET['payment_type'];
                    if ($payment_type == '1') {
                      $where_str .= "o.payment_method like '%Sage%' and ";
                    }
                    if ($payment_type == '2') {
                      $where_str .= "o.payment_method like '%PayPal%' and ";
                    }
                    if ($payment_type == '3') {
                      $where_str .= "o.payment_method like '%Transfer%' and ";
                    }
                    if ($payment_type == '4') {
                      $where_str .= "o.payment_method like '%Phone%' and ";
                    }
                  }

                  if (isset($_GET['p']) && $_GET['p'] != "") { // query is set in address

                    $select_str .= 'distinct op.orders_id, ';
                    $table_str .= TABLE_ORDERS_PRODUCTS . ' op, ';
                    $where_str .= 'o.orders_id = op.orders_id and ';

                    $search_query = $_GET['p'];
                    $p_array = explode(' ', ($search_query));
                    $p_product_name = '(op.products_name LIKE \'%' . $p_array[0] . '%\'';

                    for ($i = 1; $i < sizeof($p_array); $i++) {
                      $p_product_name .= ' AND op.products_name LIKE \'%' . $p_array[$i] . '%\'';
                    }

                    $p_product_name .= ')';
                    $search_query = ' AND (' . $p_product_name . ')';
                  }

                  if ($vat_query && $coffee_query) {
                    $coffee_vat_query = ' and ((' . $vat_query . ') OR (' . $coffee_query . '))';
                  } elseif ($vat_query) {
                    $coffee_vat_query = ' and ' . $vat_query . '';
                  } elseif ($coffee_query) {
                    $coffee_vat_query = ' and ' . $coffee_query . '';
                  }

                  //$orders_query_raw = "select " . $select_str . " o.orders_id, o.customers_id, o.customers_name, o.payment_method, o.date_purchased, o.last_modified, o.currency, o.currency_value, o.delivery_country, o.orders_cg_delivery_cost, o.customers_dummy_account, o.shipping_module, s.orders_status_name, ot.text as order_total from " . TABLE_ORDERS . " o left join " . TABLE_ORDERS_TOTAL . " ot on (o.orders_id = ot.orders_id) " . $join_str . ", " . $table_str . TABLE_ORDERS_STATUS . " s where " . $where_str . " o.orders_status = s.orders_status_id and ot.class = 'ot_total' " . (!is_null($search_query)?$search_query:'') . (!is_null($coffee_vat_query)?$coffee_vat_query:'') . $group_str . " order by o.orders_id DESC";

                  $orders_query_raw = "select " . $select_str . " o.*, s.orders_status_name, o.third_man from " . TABLE_ORDERS . " o " . $join_str . ", " . $table_str . TABLE_ORDERS_STATUS . " s where " . $where_str . " o.orders_status = s.orders_status_id " . (!is_null($search_query) ? $search_query : '') . (!is_null($coffee_vat_query) ? $coffee_vat_query : '') . $group_str . " order by o.orders_id DESC";
                }
                // #### HURL CUSTOMER ORDER SEARCH ####
                $orders_split = new splitPageResults($_GET['page'], MAX_DISPLAY_ORDER_SEARCH_RESULTS, $orders_query_raw, $orders_query_numrows);
                $orders_query = tep_db_query($orders_query_raw);
                while ($orders = tep_db_fetch_array($orders_query)) {

                  //if(($orders['sagepay_status'] == 1 || $orders['sagepay_status'] == 3) && (strtotime("-1 minute") > strtotime($orders['date_purchased']))){
                  //   $warningclass = 'style="background-color: #FFCC33;"';
                  //} else {
                  $warningclass = '';
                  //}

                  if ((!isset($_GET['oID']) || (isset($_GET['oID']) && ($_GET['oID'] == $orders['orders_id']))) && !isset($oInfo)) {
                    $oInfo = new objectInfo($orders);
                  }


                  // Start Batch Update Status v0.4
                  if (isset($oInfo) && is_object($oInfo) && ($orders['orders_id'] == $oInfo->orders_id)) {
                    echo '              <tr id="defaultSelected" class="dataTableRowSelected"  ' . $warningclass . ' onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" >' . "\n";
                    $onclick = 'onclick="document.location.href=\'' . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=edit') . '\'"';
                  } else {
                    echo '              <tr class="dataTableRow"  ' . $warningclass . ' onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" >' . "\n";
                    $onclick = 'onclick="document.location.href=\'' . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID')) . 'oID=' . $orders['orders_id']) . '\'"';
                  }
                ?>
                  <!-- add order number to the orders listing -->
                  <td>
                    <?php

                    $order_status_name_query = tep_db_query("SELECT o.orders_id, o.orders_location, s.orders_status_name, s.orders_status_id, o.third_man from " . TABLE_ORDERS . " o left join " . TABLE_ORDERS_TOTAL . " ot on (o.orders_id = ot.orders_id), " . TABLE_ORDERS_STATUS . " s where o.orders_status = s.orders_status_id and s.language_id = '" . (int)$languages_id . "' and o.orders_id = '" . tep_db_input($orders['orders_id']) . "' ");

                    $order_status_name = tep_db_fetch_array($order_status_name_query);

                    if ($order_status_name['orders_location'] == '0') {
                      echo tep_image(DIR_WS_ICONS . 'order_london.png', 'London Order (Online)');
                    }
                    if ($order_status_name['orders_location'] == '1') {
                      echo tep_image(DIR_WS_ICONS . 'order_london.png', 'London Order (Norfolk Shop)');
                    }
                    if ($order_status_name['orders_location'] == '2') {
                      echo tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Online)');
                    }
                    if ($order_status_name['orders_location'] == '3') {
                      echo tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Norfolk Shop)');
                    }
                    if ($order_status_name['orders_location'] == '4') {
                      echo tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Online)');
                    }
                    if ($order_status_name['orders_location'] == '5') {
                      echo tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order (Norfolk Shop)');
                    }
                    if ($order_status_name['orders_location'] == '6') {
                      echo tep_image(DIR_WS_ICONS . 'order_chester.png', 'Chester Order');
                    }
                    if ($order_status_name['orders_location'] == '7') {
                      echo tep_image(DIR_WS_ICONS . 'order_liverpool.png', 'Liverpool Order');
                    }
                    ?>
                  </td>
                  <td>
                    <?php
                    // get previous orders where not a dummy account
                    $cust_tilaukset_query = tep_db_query("select count(*) from " . TABLE_ORDERS . " where customers_id = '" . $orders['customers_id'] . "' and customers_dummy_account != 1");
                    $cust_tilaukset = tep_db_fetch_array($cust_tilaukset_query);

                    if ($cust_tilaukset['count(*)'] < "3" or $orders['customers_id'] == "0" or $orders['customers_dummy_account']) {
                      echo tep_image(DIR_WS_ICONS . 'flag_red.png', '1st or 2nd Order / Express Checkout');
                    }
                    ?>
                  </td>
                  <td class="dataTableContent"><input type="checkbox" name="update_oID[]" value="<?php echo $orders['orders_id']; ?>"></td>
                  <td class="dataTableContent" align="center" <?php echo $onclick; ?>><strong><?php echo $orders['orders_id']; ?></strong></td>
                  <td class="dataTableContent"><?php echo '<a href="' . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $orders['orders_id'] . '&action=edit') . '">' . tep_image(DIR_WS_ICONS . 'preview.gif', ICON_PREVIEW) . '</a>'; ?></td>
                  <td class="dataTableContent">
                    <?php

                    $sagepay_comments_query = tep_db_query("select sagepay_report from orders_sagepay_comments where orders_id = '" . tep_db_input($orders['orders_id']) . "'");
                    if (tep_db_num_rows($sagepay_comments_query)) {
                      $sagepay_comments = tep_db_fetch_array($sagepay_comments_query);

                      if (strpos($orders['payment_method'], 'PayPal') !== false) {
                        if ((strpos($sagepay_comments['sagepay_report'], 'Pending') !== false) || (strpos($sagepay_comments['sagepay_report'], 'Ineligible') !== false) || ($orders['third_man'] == '3')) {
                          $payment_image = 'images/icons/pay_pal_ineligible.gif';
                        } else {
                          $payment_image = 'images/icons/pay_pal.gif';
                        }
                      } else {
                        if (strpos($sagepay_comments['sagepay_report'], 'ATTEMPTONLY') !== false) {
                          $payment_image = 'images/icons/credit_card_warning2.gif';
                        } elseif (strpos($sagepay_comments['sagepay_report'], '3D Secure: OK') !== false) {
                          $payment_image = 'images/icons/credit_card.gif';
                        } else {
                          $payment_image = 'images/icons/credit_card_warning.gif';
                        }
                      }
                      echo '<img src="' . $payment_image . '" onMouseover="ddrivetip(\'' . tep_html_noquote($sagepay_comments['sagepay_report']) . '\', \'white\', 300)"; onMouseout="hideddrivetip()" align="top" border="0">';
                    }

                    ?>
                  </td>
                  <td class="dataTableContent">
                    <?php
                    $clean_comments = "";
                    $orders_history_query = tep_db_query("SELECT comments FROM " . TABLE_ORDERS_STATUS_HISTORY . " WHERE orders_id = '" . tep_db_input($orders['orders_id']) . "' ORDER BY date_added");
                    while ($orders_comments = tep_db_fetch_array($orders_history_query)) {

                      // Append each existing comment in succession
                      if (tep_not_null($orders_comments['comments'])) {
                        $clean_comments .= tep_html_noquote($orders_comments['comments']) . '<br>';
                      } //end if
                    } //end while

                    if (tep_not_null($clean_comments)) {

                    ?><img src="images/icons/comment2.gif" onMouseover="ddrivetip('<?php echo '' . $clean_comments . ''; ?>', 'white', 300)" ; onMouseout="hideddrivetip()" align="top" border="0">
                    <?php } //end 
                    ?>
                  </td>
                  <td class="dataTableContent">
                    <?php

                    $gift_comments_query = tep_db_query("select gift_message from orders_gift_comments where orders_id = '" . tep_db_input($orders['orders_id']) . "'");
                    if (tep_db_num_rows($gift_comments_query)) {
                      $gift_comments = tep_db_fetch_array($gift_comments_query);
                      echo '<img src="images/icons/gift.gif" onMouseover="ddrivetip(\'' . tep_html_noquote($gift_comments['gift_message']) . '\', \'white\', 300)"; onMouseout="hideddrivetip()" align="top" border="0">';
                    }

                    ?>
                  </td>
                  <td class="dataTableContent" <?php echo $onclick; ?>><?php echo '&nbsp;' . $orders['customers_name']; ?></td>
                  <td class="dataTableContent" align="right" <?php echo $onclick; ?>>
                  <?php

                  $order_total_query = tep_db_query("select ot.text as order_total, ot.value as order_total_value, ot.class from " . TABLE_ORDERS_TOTAL . " ot where ot.orders_id = '" . tep_db_input($orders['orders_id']) . "' and (ot.class = 'ot_total' or ot.class = 'ot_discount_coupon') ");
                  
                  $coupon_used = '';

                  while($order_total = tep_db_fetch_array($order_total_query)){   
                    if($order_total['class'] == 'ot_total'){
                      echo strip_tags($order_total['order_total']);
                      $order_total_value = number_format((float)$order_total['order_total_value'], 2);
                    }
                    if($order_total['class'] == 'ot_discount_coupon'){
                      $coupon_used = '*';
                    }
                  }

        
                  ?>
                  </td>
                  <td class="dataTableContent" align="center"><?php echo $coupon_used; ?></td>
                  <td class="dataTableContent" align="center" <?php echo $onclick; ?>><?php echo tep_datetime_short($orders['date_purchased']); ?></td>
                  <td class="dataTableContent" align="right"><?php

                                                              $order_total_ex_vat = 0;
                                                              $total_not_calculated = false;
                                                              $total_calculated_value = 0;
                                                              $order_retail_total = 0;
                                                              $order_cost_total_ex_vat = 0;

                                                              $order_products_query = tep_db_query("select orders_products_id, products_price, orders_products_cg_cost, orders_products_trade_price_discount, products_quantity from " . TABLE_ORDERS_PRODUCTS . " where orders_id = '" . $orders['orders_id'] . "'");

                                                              while ($order_products = tep_db_fetch_array($order_products_query)) {

                                                                list($add_total_not_calculated, $add_product_retail_total, $add_product_cost_total_ex_vat) = tep_get_product_figures($order_products['orders_products_id'], $order_products['products_price'], $order_products['products_quantity'], $order_products['orders_products_cg_cost'], $order_products['orders_products_trade_price_discount']);

                                                                if ($add_total_not_calculated) {
                                                                  $total_not_calculated = true;
                                                                }
                                                                if ($add_product_retail_total > 0) {
                                                                  $order_retail_total += $add_product_retail_total;
                                                                }
                                                                if ($add_product_cost_total_ex_vat > 0) {
                                                                  $order_cost_total_ex_vat += $add_product_cost_total_ex_vat;
                                                                }
                                                              } // end of product order loop

                                                              $order_retail_total_ex_vat = ($order_retail_total / 1.2);

                                                              if ($total_not_calculated) {
                                                                $total_cost_price = 'Incomplete';
                                                              } else {
                                                                $total_cost_price = '&pound;' . number_format((float)$order_cost_total_ex_vat, 2);
                                                              }

                                                              echo '&pound;' . number_format((float)$order_retail_total_ex_vat, 2); // correct

                                                              ?></td>
                  <td class="dataTableContent" align="right"><?php echo $total_cost_price; ?></td>
                  <?php
                  if ($total_cost_price != 'Incomplete') {
                    if ($order_retail_total_ex_vat != 0) {
                      $margin = tep_round((((($order_retail_total_ex_vat) - ($order_cost_total_ex_vat)) / ($order_retail_total_ex_vat)) * 100), 0);
                      $profit = '&pound;' . tep_round(($order_retail_total_ex_vat) - ($order_cost_total_ex_vat), 2);
                    } else {
                      $margin = 0;
                      $profit = 0;
                    }
                  }
                  ?>
                  <td class="dataTableContent" align="right"><?php if ($total_cost_price != 'Incomplete') {
                                                                echo $profit;
                                                              } ?></td>
                  <td class="dataTableContent" align="right"><?php if ($total_cost_price != 'Incomplete') {

                                                                if ($margin < 10) {
                                                                  echo '<strong style="color: red;">' . $margin . '%</strong>';
                                                                } else {
                                                                  echo $margin . '%';
                                                                }
                                                              }
                                                              ?></td>
                  <td class="dataTableContent" align="center" <?php echo $onclick; ?>><?php
                                                                                      $sources_id = '';
                                                                                      $sources_express_id = '';
                                                                                      $courtesy_order_id = '';
                                                                                      $courtesy_date_sent = '';

                                                                                      if ($orders['customers_id'] != 0) {
                                                                                        $sources_id_query = tep_db_query("SELECT customers_info_source_id, date_last_courtesy_email_order_id, date_last_courtesy_email_sent FROM " . TABLE_CUSTOMERS_INFO . " WHERE customers_info_id = '" . tep_db_input($orders['customers_id']) . "' ");
                                                                                        $sources_id = tep_db_fetch_array($sources_id_query);
                                                                                        echo tep_get_sources_name($sources_id['customers_info_source_id'], $orders['customers_id']);
                                                                                      } else {
                                                                                        $sources_express_id_query = tep_db_query("SELECT express_info_source_id FROM " . TABLE_SOURCES_EXPRESS . " WHERE express_orders_id = '" . tep_db_input($orders['orders_id']) . "' ");
                                                                                        $sources_express_id = tep_db_fetch_array($sources_express_id_query);
                                                                                        echo tep_get_express_sources_name($sources_express_id['express_info_source_id'], $orders['orders_id']);
                                                                                      }

                                                                                      ?></td>
                  <td class="dataTableContent" align="center" <?php echo $onclick; ?>><?php echo $orders['delivery_country']; ?></td>
                  <td class="dataTableContent" align="center" <?php echo $onclick; ?>><?php

                                                                                      if ($sources_id) {

                                                                                        $datetime1 = '';
                                                                                        $datetime2 = '';
                                                                                        $interval = '';
                                                                                        $days_ago = '';

                                                                                        $courtesy_order_id = $sources_id['date_last_courtesy_email_order_id'];
                                                                                        $courtesy_date_sent = $sources_id['date_last_courtesy_email_sent'];

                                                                                        if ($courtesy_date_sent) {
                                                                                          $courtesy_date_sent = strtotime($courtesy_date_sent);
                                                                                          $current_date = strtotime(date_default_timezone_get()); // change y with your current date var
                                                                                          $datediff = $current_date - $courtesy_date_sent;
                                                                                          $days_ago = floor($datediff / (60 * 60 * 24));
                                                                                          if ($days_ago < 1) {
                                                                                            $days_ago = 0;
                                                                                          }
                                                                                          $interval = $days_ago . ' days ago';
                                                                                        }

                                                                                        // 8 courtesy email sent
                                                                                        // 22 customer courtesy called
                                                                                        // 24 customer courtesy done
                                                                                        // 35 order review

                                                                                        if ($courtesy_order_id == 8) {
                                                                                          $courtesy_message = 'C. email sent ' . $interval;
                                                                                        } elseif ($courtesy_order_id == 22) {
                                                                                          $courtesy_message = 'C. call ' . $interval;
                                                                                        } elseif ($courtesy_order_id == 24) {
                                                                                          $courtesy_message = 'C. done ' . $interval;
                                                                                        } elseif ($courtesy_order_id == 35) {
                                                                                          $courtesy_message = 'Order Review ' . $interval;
                                                                                        } else {
                                                                                          $courtesy_message = '';
                                                                                        }
                                                                                        echo $courtesy_message;
                                                                                      } else {
                                                                                        echo 'Express Checkout';
                                                                                      }

                                                                                      ?>&nbsp;&nbsp;</td>
                  <?php
                  if ($orders['third_man'] == '0') {
                    $third_man_icon = 'icon-shield-outline.png';
                  } elseif ($orders['third_man'] == '1') {
                    $third_man_icon = 'icon-shield-check.png';
                  } elseif ($orders['third_man'] == '2') {
                    $third_man_icon = 'icon-shield-question.png';
                  } elseif ($orders['third_man'] == '3') {
                    $third_man_icon = 'icon-shield-cross.png';
                  } elseif ($orders['third_man'] == '4') {
                    $third_man_icon = 'icon-shield-zebra.png';
                  } else {
                    $third_man_icon = '';
                  }
                  ?>
                  <td class="dataTableContent" align="center"><a class="sagepay-report" href="<?php echo tep_href_link('sagepay_reporting.php', 'orders_id=' . $orders['orders_id']); ?>"><?php echo tep_image(DIR_WS_ICONS . $third_man_icon, 'Click for Sage Info'); ?></a></td>
                  <td class="dataTableContent" align="right" <?php echo $onclick; ?>>

<?php
if($orders['third_man'] >= 1 && $orders['third_man'] <= 3){
$sagepay_amount_query = tep_db_query("SELECT s.sage_3d_amount from sagepay_server_log s where s.orders_id = '" . tep_db_input($orders['orders_id']) . "' limit 1");
$sagepay_amount = tep_db_fetch_array($sagepay_amount_query);
if (is_numeric($sagepay_amount['sage_3d_amount'])) { 
  $sagepay_amount_output = number_format((float)$sagepay_amount['sage_3d_amount'], 2);
  if($sagepay_amount_output != $order_total_value){
    echo '<strong style="color:#CC0000; font-size:22px;">' . $sagepay_amount_output . '</strong>'; 
  } else {
    echo $sagepay_amount_output;
  }
}
}

?>
                  </td>
                  <td class="dataTableContent" align="right" <?php echo $onclick; ?>><?php echo $order_status_name['orders_status_name']; ?>&nbsp;&nbsp;</td>
                  <td class="dataTableContent" align="right">
<?php 

$rm_query = tep_db_query("select status from orders_rm_labels where orders_id = '" . $orders['orders_id'] . "'");

while ($rm = tep_db_fetch_array($rm_query)) {
  if($rm['status'] == '1'){
    echo '&nbsp;RM&nbsp;';
  } else {
    echo '&nbsp;XX&nbsp;';
  }
}

?>                               
                  </td>
                  <td class="dataTableContent" align="right">
                    <?php

                    if ($order_status_name['orders_location'] == '2' || $order_status_name['orders_location'] == '3' || $order_status_name['orders_location'] == '1') {
                      echo tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order');
                    } elseif ($order_status_name['orders_location'] == '4' || $order_status_name['orders_location'] == '5') {
                      echo tep_image(DIR_WS_ICONS . 'order_norfolk.png', 'Norfolk / Alcohol Order');
                    } elseif ($order_status_name['orders_location'] == '0') {
                      echo tep_image(DIR_WS_ICONS . 'order_london.png', 'London Order');
                    } elseif ($order_status_name['orders_location'] == '6') {
                      echo tep_image(DIR_WS_ICONS . 'order_chester.png', 'Chester Order');
                    } elseif ($order_status_name['orders_location'] == '7') {
                      echo tep_image(DIR_WS_ICONS . 'order_liverpool.png', 'Liverpool Order');
                    }

                    ?></td>
          </tr>
        <?php } ?>
        <tr>
          <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>
        <tr>
          <td colspan="21">

            <script language="javascript">
              <!--
              var usrdate = '';

              function updateComment(obj, statusnum) {
                var textareas = document.getElementsByTagName('textarea');
                var myTextarea = textareas.item(0); {
                  myTextarea.value = obj;
                }
                var selects = document.getElementsByTagName('select');
                var theSelect = selects.item(0);
                theSelect.selectedIndex = statusnum;

                return false;

              }

              function killbox() {
                var box = document.getElementsByTagName('textarea');
                var killbox = box.item(0);
                killbox.value = '';
                return false;

              }

              function getdate() {
                usrdate = prompt("<?php echo TEXT_PROMPT; ?>");

              }

              function getrack() {
                usrtrack = prompt("<?php echo TEXT_TRACKNO; ?>");

              }
              //
              -->
            </script>

            <table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td valign="top"><?php echo tep_draw_input_field('select_all', BUS_SELECT_ALL, 'class="cbutton" onclick="checkAll(); return false;"', '', 'submit') . tep_draw_input_field('select_none', BUS_SELECT_NONE, 'class="cbutton" onclick="uncheckAll(); return false;"', '', 'submit'); ?></td>
                <td valign="top">

                  <?php echo  tep_draw_textarea_field('comments', 'soft', '55', '5'); ?>
                  <!-- Button Section -->
                  <div style="clear: both; margin: 20px 0;">
                    <?php //print_r($bus_cbuttons);
                    foreach ($bus_cbuttons as $CBName => $CBValue) {
                      echo '<button class="cbutton" onClick="return updateComment(\'' . $CBValue . '\')">' . $CBName . '</button>&nbsp;';
                    }
                    ?>
                    <button class="cbutton" onClick="return killbox();"><?php echo BUS_CBUTTON_RESET; ?></button>
                  </div>

                </td>
                <td valign="top" class="main">
                  <div style="clear: both; margin-bottom: 10px;">
                    <?php echo BUS_TEXT_NEW_STATUS . ': ' . tep_draw_pull_down_menu('new_status', array_merge(array(array('id' => '', 'text' => 'Select')), $orders_statuses), '', ''); ?>
                  </div>

                  <div style="clear: both; margin-bottom: 10px;">
                    <input type="checkbox" name="free_gift" id="free_gift" value="1"><label for="free_gift">Free Gift added (if applicable)</label>
                  </div>

                  <div style="clear: both; margin-bottom: 10px;">
                    <input type="checkbox" name="notify" id="notify" value="1" checked="checked"><label for="notify">Notify Customer(s)</label>
                  </div>

                  <div style="clear: both;">
                    <?php echo tep_draw_input_field('submit', BUS_SUBMIT, 'style="padding: 15px; font-size: 18px;"', '', 'submit');  ?>
                  </div>
                </td>
              </tr>
            </table>
            </form>

          </td>
        </tr>
        <tr>
          <td colspan="20">
            <table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td class="smallText" valign="top"><?php echo $orders_split->display_count($orders_query_numrows, MAX_DISPLAY_ORDER_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_ORDERS); ?></td>
                <td class="smallText" align="right"><?php echo $orders_split->display_links($orders_query_numrows, MAX_DISPLAY_ORDER_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'oID', 'action'))); ?></td>
              </tr>
            </table>
          </td>
        </tr>
        </table>
      </td>
      <?php // End Batch Update Status v0.4
      ?>
      <?php
      $heading = array();
      $contents = array();

      switch ($action) {
        case 'restock':
          $heading[] = array('text' => '<b>Restock Order</b>');

          $contents = array('form' => tep_draw_form('orders', FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=restockconfirm'));
          $contents[] = array('text' => 'Are you sure you want to restock all items in this order?<br><br>');
          $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_confirm.gif', IMAGE_CONFIRM) . ' <a href="' . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
          break;
        case 'delete':
          $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_ORDER . '</b>');

          $contents = array('form' => tep_draw_form('orders', FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=deleteconfirm'));
          $contents[] = array('text' => TEXT_INFO_DELETE_INTRO . '<br><br><b>' . $cInfo->customers_firstname . ' ' . $cInfo->customers_lastname . '</b>');
          $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
          break;
        default:
          if (isset($oInfo) && is_object($oInfo)) {

            $heading[] = array('text' => '<b>[' . $oInfo->orders_id . ']&nbsp;&nbsp;' . tep_datetime_short($oInfo->date_purchased) . '</b>');

            $button_string .= tep_draw_button(IMAGE_DETAILS, 'document', tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=edit'));

            if ($admin['id'] == 1 || $admin['id'] == 2 || $admin['id'] == 8 || $admin['id'] == 15) {
              $button_string .= tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=delete'));
            }
            $contents[] = array('align' => 'center', 'text' => $button_string);

            $contents[] = array('align' => 'center', 'text' => tep_draw_button(IMAGE_ORDERS_INVOICE, 'document', tep_href_link(FILENAME_ORDERS_INVOICE, 'oID=' . $oInfo->orders_id), null, array('newwindow' => true)) . tep_draw_button(IMAGE_ORDERS_PACKINGSLIP, 'document', tep_href_link(FILENAME_ORDERS_PACKINGSLIP, 'oID=' . $oInfo->orders_id), null, array('newwindow' => true)) . tep_draw_button(IMAGE_EDIT, 'document', tep_href_link(FILENAME_ORDERS_EDIT, 'oID=' . $oInfo->orders_id)));

            if ($admin['id'] == 1 || $admin['id'] == 2 || $admin['id'] == 8 || $admin['id'] == 10 || $admin['id'] == 15 || $admin['id'] == 28 || $admin['id'] == 33 || $admin['id'] == 23 || $admin['id'] == 60) {
              $contents[] = array('align' => 'center', 'text' => tep_draw_button('Restock Order', 'document', tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('oID', 'action')) . 'oID=' . $oInfo->orders_id . '&action=restock')));
            }

            $contents[] = array('text' => '<br>' . TEXT_DATE_ORDER_CREATED . ' ' . tep_date_short($oInfo->date_purchased));
            if (tep_not_null($oInfo->last_modified)) $contents[] = array('text' => TEXT_DATE_ORDER_LAST_MODIFIED . ' ' . tep_date_short($oInfo->last_modified));
            $contents[] = array('text' => '<br>' . TEXT_INFO_PAYMENT_METHOD . ' '  . $oInfo->payment_method);
          }
          break;
      }

      if ((tep_not_null($heading)) && (tep_not_null($contents))) {
        echo '            <td width="300" valign="top">' . "\n";

        $box = new box;
        echo $box->infoBox($heading, $contents);

        echo '<table style="padding: 20px;">
            <tr>
              <td colspan="2" class="main"><strong>Sage Key</strong></td>
            </tr>
            <tr>
             <td>' . tep_image(DIR_WS_ICONS . 'icon-shield-outline.png', 'Wait for info') . '</td><td class="main">Wait for info, do not ship.</td>
            </tr>
            <tr>
             <td>' . tep_image(DIR_WS_ICONS . 'icon-shield-check.png', 'OK, Low Risk') . '</td><td class="main">OK, Low Risk</td>
            </tr>
            <tr>
             <td>' . tep_image(DIR_WS_ICONS . 'icon-shield-question.png', 'HOLD, Medium Risk') . '</td><td class="main">HOLD, Medium Risk</td>
            </tr>
            <tr>
             <td>' . tep_image(DIR_WS_ICONS . 'icon-shield-cross.png', 'REJECT, High Risk') . '</td><td class="main">REJECT, High Risk</td>
            </tr>
            <tr>
             <td>' . tep_image(DIR_WS_ICONS . 'icon-shield-zebra.png', 'Not applicable') . '</td><td class="main">Not applicable</td>
            </tr>
          </table>' . "\n";

        echo '            </td>' . "\n";
      }
      ?>
    </tr>
</table>
</td>
</tr>
<?php
  }
?>
</table>
<?php
require(DIR_WS_INCLUDES . 'template_bottom.php');
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>