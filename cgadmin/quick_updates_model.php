<?php

// RP Dynamic Quick Updates Version 1.1
// Only handles 2 tables dynamically

require('includes/application_top.php');

$page_filename = 'quick_updates_model.php';
$page_heading = 'Quick Product Model & Name Updates';

$default_num_rows = 20;

$columns_and_tables[] = array(

  'heading' => 'ID', // required for indexing
  'key' => 'products_id',
  'prefix' => 'p',
  'column' => 'products_id',
  'type' => 'hidden',
  'table' => TABLE_PRODUCTS

);

$columns_and_tables[] = array(

  'heading' => 'Model',
  'key' => 'products_id',
  'prefix' => 'p',
  'column' => 'products_model',
  'type' => 'normal',
  'table' => TABLE_PRODUCTS

);

$columns_and_tables[] = array(

  'heading' => 'MPN / Barcode',
  'key' => 'products_id',
  'prefix' => 'p',
  'column' => 'products_mpn',
  'type' => 'normal',
  'table' => TABLE_PRODUCTS

);

$columns_and_tables[] = array(

  'heading' => 'Status',
  'key' => 'products_id',
  'prefix' => 'p',
  'column' => 'products_status',
  'type' => 'text',
  'table' => TABLE_PRODUCTS

);

$columns_and_tables[] = array(

  'heading' => 'Name',
  'key' => 'products_id',
  'prefix' => 'pd',
  'column' => 'products_name',
  'type' => 'normal',
  'table' => TABLE_PRODUCTS_DESCRIPTION

);

if ($select_column_count > $other_column_count) {
  $add_blank_columns = ($select_column_count - $other_column_count);
} else {
  $add_blank_columns = 0;
}

if (isset($_GET['page'])) {
  $page = $_GET['page'];
}
if (isset($_GET['sort_by'])) {
  $sort_by = $_GET['sort_by'];
}
if (isset($_GET['row_by_page'])) {
  $row_by_page = $_GET['row_by_page'];
}
if (isset($_GET['current_category_id'])) {
  $current_category_id = $_GET['current_category_id'];
}
if (isset($_GET['search_keywords']) && $_GET['search_keywords'] != "") {
  $search_keywords = $_GET['search_keywords'];
}
if (isset($_GET['products_visible'])) {
  $products_visible = $_GET['products_visible'];
} else {
  $products_visible = 4;
}

if ($row_by_page) {
  define('MAX_DISPLAY_ROW_BY_PAGE', $row_by_page);
} else {
  $row_by_page = $default_num_rows;
  define('MAX_DISPLAY_ROW_BY_PAGE', $default_num_rows);
}

##// Uptade database
if (tep_not_null($_GET['action']) && !$restrict_user_updates) {
  switch ($_GET['action']) {
    case 'update':
      $count_update = 0;
      $item_updated = array();

      foreach ($columns_and_tables as $row) {

        $new_column_value = 'product_new_' . $row['column'];
        $old_column_value = 'product_old_' . $row['column'];

        // Add error collection for MPN uniqueness
        if (!isset($mpn_errors)) {
          $mpn_errors = array();
        }

        if ($_POST[$new_column_value]) {
          foreach ($_POST[$new_column_value] as $id => $saved_value) {
            if ($_POST[$new_column_value][$id] != $_POST[$old_column_value][$id]) {
              // Uniqueness check for products_mpn
              if ($row['column'] == 'products_mpn' && tep_not_null($saved_value) && trim($saved_value) !== '') {
                $mpn_check_query = tep_db_query("SELECT products_id, products_model FROM " . $row['table'] . " WHERE products_mpn = '" . tep_db_input($saved_value) . "' AND products_id != '" . (int)$id . "' LIMIT 1");
                if (tep_db_num_rows($mpn_check_query) > 0) {
                  $mpn_row = tep_db_fetch_array($mpn_check_query);
                  // Fetch product name from products_description
                  $name_query = tep_db_query("SELECT products_name FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id = '" . (int)$mpn_row['products_id'] . "' LIMIT 1");
                  $name_row = tep_db_fetch_array($name_query);
                  $product_name = $name_row ? $name_row['products_name'] : '';
                  $mpn_errors[] = "MPN '" . htmlspecialchars($saved_value) . "' already exists for another product (Name: " . htmlspecialchars($product_name) . ", Model: " . htmlspecialchars($mpn_row['products_model']) . "). Update skipped.";
                  continue; // Skip update for this MPN
                }
              }
              $count_update++;
              $item_updated[$id] = 'updated';
              tep_db_query("UPDATE " . $row['table'] . " SET " . $row['column'] . " = '" . $saved_value . "' WHERE " . $row['key'] . " = " . $id . "");
              if ($row['timestamp']) {
                tep_db_query("UPDATE " . $row['table'] . " SET " . $row['timestamp'] . " = now() WHERE " . $row['key'] . " = " . $id . "");
              }
            }
          }
        }
      }

      $count_item = array_count_values($item_updated);
      if ($count_item['updated'] > 0) $messageStack->add($count_item['updated'] . ' rows updated, ' . $count_update . ' fields updated.', 'success');
      // Show MPN errors if any
      if (!empty($mpn_errors)) {
        foreach ($mpn_errors as $err) {
          $messageStack->add($err, 'error');
        }
      }

      break;
  }
}
//// define the step for rollover lines per page
$row_bypage_array = array(array());
for ($i = 10; $i <= 100; $i = $i + 5) {
  $row_bypage_array[] = array(
    'id' => $i,
    'text' => $i
  );
}

require(DIR_WS_INCLUDES . 'template_top.php');

$heading_sort_string = 'row_by_page=' . $row_by_page;
$pagination_string   = '&row_by_page=' . $row_by_page;
$full_string         = 'row_by_page=' . $row_by_page;
$additional_tables   = '';
$additional_where    = '';

// control string sort page
if ($sort_by && !preg_match('/order by/', $sort_by)) {
  $pagination_string .= '&sort_by=' . $sort_by;
  $full_string       .= '&sort_by=' . $sort_by;
  $sort_by = 'order by ' . $sort_by;
}

if ($page) {
  $heading_sort_string .= '&page=' . $page;
  $full_string         .= '&page=' . $page;
}

if ($current_category_id && $current_category_id != 0) {
  $pagination_string .= '&cPath=' . $current_category_id;
  $heading_sort_string .= '&cPath=' . $current_category_id;
  $full_string         .= '&cPath=' . $current_category_id;

  $cats[] = $current_category_id;
  for ($z = 0; $z < count($cats); $z++) {
    $categorie_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$cats[$z] . "'");
    while ($categorie = tep_db_fetch_array($categorie_query)) {
      $cats[] = $categorie['categories_id'];
    }
    $cats = array_unique($cats); // sort out doubles
  }

  $additional_tables = ", " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c ";
  $additional_where = "and pd.products_id = p2c.products_id and p2c.categories_id in (" . implode(', ', $cats) . ") ";
}

if ($search_keywords) {
  // added especially for &s
  $search_url_keywords = str_replace('&', '%26', $search_keywords);
  $pagination_string .= '&search_keywords=' . $search_url_keywords;
  $heading_sort_string .= '&search_keywords=' . $search_url_keywords;
  $full_string         .= '&search_keywords=' . $search_url_keywords;
  $additional_where .= " and (pd.products_name like '%" . tep_db_input($search_keywords) . "%' or p.products_model like '%" . tep_db_input($search_keywords) . "%')";
}

if ($products_visible) {
  $pagination_string .= '&products_visible=' . $products_visible;
  $heading_sort_string .= '&products_visible=' . $products_visible;
  $full_string         .= '&products_visible=' . $products_visible;
  if ($products_visible == '2') {
    $additional_where .= 'and p.products_visible = 0 ';
  } elseif ($products_visible == '4') {
    $additional_where .= 'and p.products_status in (1,2,7) ';
  } elseif ($products_visible == '3') {
  } else {
    $additional_where .= 'and p.products_visible = 1 ';
  }
}

$products_visible_array = array(array('id' => '1', 'text' => 'Active, OOS, Limited, Sold Out'), array('id' => '4', 'text' => 'Active & OOS'), array('id' => '2', 'text' => 'Disabled'), array('id' => '3', 'text' => 'All'));

// controle lenght (lines per page)
$split_page = $page;
if ($split_page > 1) $rows = $split_page * MAX_DISPLAY_ROW_BY_PAGE - MAX_DISPLAY_ROW_BY_PAGE;

?>
<style>
  .highlight-row {
    background-color: #EEE;
  }

  .empty {
    background: none;
    border: none;
  }
</style>
<?php echo tep_draw_form('search', $page_filename, '', 'get'); ?>
<table width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 20px;">
  <tr>
    <td class="pageHeading" valign="top" style="padding: 20px 0;" colspan="2"><?php echo $page_heading; ?></td>
  </tr>
  <tr>
    <td class="smallText"><?php echo 'Search&nbsp;&nbsp;' . tep_draw_input_field('search_keywords', '', "size=\"32\"$onfocus"); ?></td>
    <td class="smallText"><?php echo 'Status&nbsp;&nbsp;' . tep_draw_pull_down_menu('products_visible', $products_visible_array, $products_visible, 'onChange="this.form.submit();"'); ?></td>
    <td class="smallText" align="center" valign="top"><?php echo 'Filter by category:&nbsp;&nbsp;' . tep_draw_pull_down_menu('cPath', tep_get_category_tree(), $current_category_id, 'onChange="this.form.submit();"'); ?></td>
  </tr>
</table>
</form>
<form name="update" method="POST" action="<?php echo $PHP_SELF . "?action=update&" . $full_string; ?>">
  <table border="0" bordercolor="#FF0000" width="99%" cellspacing="0" cellpadding="5">
    <tr class="dataTableHeadingRow">
      <?php
      $heading_count = 0;

      foreach ($columns_and_tables as $row) {

        if ($heading_count == ($select_column_count - $add_blank_columns)) {
          if ($add_blank_columns) {
            for ($k = 0; $k < $add_blank_columns; $k++) {
              echo '<td></td>';
            }
          }

          echo '</tr><tr class="dataTableHeadingRow">';
        }
        $heading_count++;

      ?>

        <?php if ($row['column'] == 'products_name') {
          $heading_count++; // add extra 
        ?>
          <td class="dataTableHeadingContent" align="left" valign="top" colspan="2">
          <?php } else { ?>
          <td class="dataTableHeadingContent" align="left" valign="top">
          <?php } ?>

          <?php if ($row['type'] != 'hidden') {
            if ($row['column'] != 'delete') { ?>
              <table border="0" cellspacing="0" cellpadding="0">
                <tr class="dataTableHeadingRow">
                  <td class="dataTableHeadingContent" align="left" valign="middle">
                    <?php echo " <a href=\"" . tep_href_link($page_filename, 'sort_by=' . $row['prefix'] . '.' . $row['column'] . ' ASC&' . $heading_sort_string) . "\" >" . tep_image(DIR_WS_IMAGES . 'icon_up.gif', 'Sort By: ' . $row['heading'] . ' Ascendingly') . "</a>
                     <a href=\"" . tep_href_link($page_filename, '&sort_by=' . $row['prefix'] . '.' . $row['column'] . ' DESC&' . $heading_sort_string) . "\" >" . tep_image(DIR_WS_IMAGES . 'icon_down.gif', 'Sort By: ' . $row['heading'] . ' Descendingly') . "</a> " . $row['heading']; ?>
                </tr>
              </table>
          <?php } else {
              echo 'Delete';
            }
          } ?>
          </td>
        <?php } ?>
    </tr>
    <?php

    $select_array = array();
    $table_array = array();
    $key_array = array();

    foreach ($columns_and_tables as $row) {
      $select_array[] = $row['prefix'] . '.' . $row['column'];
      $table_array[] =  $row['table'] . ' ' . $row['prefix'];
      $key_array[] =  $row['prefix'] . '.' . $row['key'];
    }

    $select_string = array_unique($select_array);
    $table_string = array_unique($table_array);
    $key_string = array_unique($key_array);

    $products_query_raw = "select " . implode(', ', $select_string) . " from " . implode(', ', array_unique($table_string));
    if (count($key_string) == 2) { // 2 different keys so 2 tables
      $products_query_raw .= $additional_tables . " where " . implode(' = ', $key_string) . " " . $additional_where;
    }
    $products_query_raw .= " " . $sort_by;

    // echo $products_query_raw;

    $products_split = new splitPageResults($split_page, MAX_DISPLAY_ROW_BY_PAGE, $products_query_raw, $products_query_numrows);
    $products_query = tep_db_query($products_query_raw);

    $css_row_count = 0;

    while ($products = tep_db_fetch_array($products_query)) {
      $rows++;
      if (strlen($rows) < 2) {
        $rows = '0' . $rows;
      }

      $css_row_count++;

      if ($css_row_count % 2) {
        echo '<tr class="highlight-row">';
      } else {
        echo '<tr>';
      }

      foreach ($columns_and_tables as $row) {

        if ($row_data_count == ($select_column_count - $add_blank_columns)) {
          if ($add_blank_columns) {
            for ($k = 0; $k < $add_blank_columns; $k++) {
              echo '<td></td>';
            }
          }

          if ($css_row_count % 2) {
            echo '</tr><tr class="highlight-row">';
          } else {
            echo '</tr><tr>';
          }
        }

        $row_data_count++;
    ?>

    <?php

        if ($row['column'] == 'products_name') {
          $row_data_count++; // add extra
          echo "<td class=\"smallText\" align=\"left\" colspan=\"2\">";
          $field_size = '100';
        } else {
          echo "<td class=\"smallText\" align=\"left\">";
          $field_size = '10';
        }

        $value_details = $products[$row['column']];

        $new_column_value = 'product_new_' . $row['column'] . '[' . $products[$row['key']] . ']';
        $old_column_value = 'product_old_' . $row['column'] . '[' . $products[$row['key']] . ']';
        $empty_column_value = 'empty_new_' . $row['column'] . '[' . $products[$row['key']] . ']';

        $row_class = 'class="additional-info-text-field"';


        if ($row['type'] == 'text') {
          if ($row['column'] == 'products_status') {
            if ($value_details == '1') {
              echo 'Enabled';
            } elseif ($value_details == '2') {
              echo 'Out of Stock';
            } elseif ($value_details == '3') {
              echo 'Limited';
            } elseif ($value_details == '4') {
              echo 'Sold Out';
            } elseif ($value_details == '7') {
              echo 'Showcase';
            } else {
              echo 'Disabled';
            }
          } else {
            echo $value_details;
          }
        } elseif ($row['type'] == 'hidden') {
          echo tep_draw_hidden_field($empty_column_value, $value_details);
        } elseif ($row['type'] == 'empty') {
          echo "<input type=\"text\" size=\"" . $field_size . "\" name=\"" . $empty_column_value . "\" value=\"\" class=\"empty\" readonly>";
        } elseif ($row['type'] == 'dummy') {
          echo "<input type=\"text\" size=\"" . $field_size . "\" name=\"" . $empty_column_value . "\" value=\"" . $value_details . "\" class=\"empty\" readonly>";
        } else {
          echo "<input type=\"text\" size=\"" . $field_size . "\" name=\"" . $new_column_value . "\" value=\"" . $value_details . "\" " . $row_class . ">";
          echo tep_draw_hidden_field($old_column_value, $value_details);
        }
        echo '</td>';
      }

      echo '</tr>';

      $row_data_count = 0;
    }
    //// hidden display parameters
    echo tep_draw_hidden_field('row_by_page', $row_by_page);
    echo tep_draw_hidden_field('sort_by', $sort_by);
    echo tep_draw_hidden_field('page', $split_page);

    ?>
  </table>
  <table border="0" bordercolor="#FF0000" width="99%" cellspacing="0" cellpadding="10">
    <tr>
      <td class="smallText"></td>
    </tr>
    <tr>
      <td align="right">
        <?php
        //// display bottom page buttons
        echo tep_draw_button(IMAGE_UPDATE, 'disk', null, 'primary');
        echo '&nbsp;&nbsp;' . tep_draw_button(IMAGE_CANCEL, 'document', tep_href_link($page_filename, $full_string), null);
        ?>
      </td>
    </tr>
  </table>
</form>
<table border="0" width="99%" cellspacing="0" cellpadding="10">
  <tr>
    <td class="smallText" valign="top"><?php echo $products_split->display_count($products_query_numrows, MAX_DISPLAY_ROW_BY_PAGE, $split_page, TEXT_DISPLAY_NUMBER_OF_PRODUCTS);  ?></td>
    <td class="smallText" align="right"><?php echo $products_split->display_links($products_query_numrows, MAX_DISPLAY_ROW_BY_PAGE, MAX_DISPLAY_PAGE_LINKS, $split_page, $pagination_string); ?></td>
  </tr>
</table>
<?php
require(DIR_WS_INCLUDES . 'template_bottom.php');
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>