<?php
/*
  $Id$
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com
  Copyright (c) 2010 osCommerce
  Released under the GNU General Public License
*/

require('includes/application_top.php');


if (isset($_GET['sort_id']) && is_numeric($_GET['sort_id'])) {
  if (!tep_session_is_registered('sort_id')) {
    tep_session_register('sort_id');
  }
  $sort_id = $_GET['sort_id'];
}

if (isset($_GET['lf_id']) && is_numeric($_GET['lf_id'])) {
  if (!tep_session_is_registered('lf_id')) {
    tep_session_register('lf_id');
  }
  $lf_id = $_GET['lf_id'];
}

if ($lf_id) {
  $additional_select .= " p2sl.*, ";
  $additional_tables .= ", products_to_stock_location p2sl ";
  $additional_where .= " and p.products_id = p2sl.products_id ";
}

if (isset($_GET['mf_id']) && is_numeric($_GET['mf_id'])) {
  if (!tep_session_is_registered('mf_id')) {
    tep_session_register('mf_id');
  }
  $mf_id = $_GET['mf_id'];
}

if (isset($cPath) && strpos($cPath, '_')) {
  echo tep_check_valid_cpath($cPath_array);
}

if (isset($cPath)) {

  $categories_desc_query = tep_db_query("select categories_description, categories_read_more from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int)$cPath_array[count($cPath_array) - 1] . "' and language_id = '" . (int)$languages_id . "'");

  $categories_desc = tep_db_fetch_array($categories_desc_query);

  $the_category_description = $categories_desc['categories_description'];

  $the_category_description = str_replace('$site', STORE_NAME, $the_category_description);
  $the_category_description = str_replace('$email', '<a href="mailto:' . STORE_OWNER_EMAIL_ADDRESS . '">' . STORE_OWNER_EMAIL_ADDRESS . '</a>', $the_category_description);
  $the_category_description = str_replace('$phone', '0345 604 0044', $the_category_description);

  $the_category_read_more = $categories_desc['categories_read_more'];

  $the_category_read_more = str_replace('$site', STORE_NAME, $the_category_read_more);
  $the_category_read_more = str_replace('$email', '<a href="mailto:' . STORE_OWNER_EMAIL_ADDRESS . '">' . STORE_OWNER_EMAIL_ADDRESS . '</a>', $the_category_read_more);
  $the_category_read_more = str_replace('$phone', '0345 604 0044', $the_category_read_more);
}

/* the following cPath references come from application_top.php  */
$category_depth = 'top';

if (isset($cPath) && tep_not_null($cPath)) {
  $categories_products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . (int)$current_category_id . "'");
  $categories_products = tep_db_fetch_array($categories_products_query);

  if ($categories_products['total'] > 0) {
    $category_depth = 'products'; /* display products */
  } else {
    $category_parent_query = tep_db_query("select count(*) as total from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$current_category_id . "'");
    $category_parent = tep_db_fetch_array($category_parent_query);

    if ($category_parent['total'] > 0) {
      $category_depth = 'nested'; /* navigate through the categories  */
    } else {
      $category_depth = 'products'; /* category has no products, but display the 'no products' message  */
    }
  }
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DEFAULT);
require(DIR_WS_INCLUDES . 'template_top.php');


if ($category_depth == 'nested') { // CATEGORY PAGE START

  $category_query = tep_db_query("select cd.categories_name, c.categories_image from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = '" . (int)$current_category_id . "' and cd.categories_id = '" . (int)$current_category_id . "' and cd.language_id = '" . (int)$languages_id . "'");

  $category = tep_db_fetch_array($category_query);

  /* main category image */
  if (isset($cPath) && strpos($cPath, '_')) {

    /* check to see if there are deeper categories within the current category   */

    $category_links = array_reverse($cPath_array);

    for ($i = 0, $n = sizeof($category_links); $i < $n; $i++) {

      $categories_query = tep_db_query("select count(*) as total from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd, " . TABLE_CATEGORIES_TO_STORES . " c2s where c.categories_status in (1,2,3,4,6) and c.parent_id = '" . (int)$category_links[$i] . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "' and c.categories_id = c2s.categories_id and c2s.store_cg = '1'");

      $categories = tep_db_fetch_array($categories_query);

      if ($categories['total'] < 1) {

        /* do nothing, go through the loop  */
      } else {

        $categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.categories_image, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd, " . TABLE_CATEGORIES_TO_STORES . " c2s where c.categories_status in (1,2,3,4,6) and c.parent_id = '" . (int)$category_links[$i] . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "' and c.categories_id = c2s.categories_id and c2s.store_cg = '1' order by sort_order, cd.categories_name");

        break; /* we've found the deepest category the customer is in   */
      }
    }
  } else {

    $categories_query = tep_db_query("select c.categories_id, cd.categories_name, cd.categories_description, c.categories_image, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd, " . TABLE_CATEGORIES_TO_STORES . " c2s where c.categories_status in (1,2,3,4,6) and c.parent_id = '" . (int)$current_category_id . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "' and c.categories_id = c2s.categories_id and c2s.store_cg = '1' order by sort_order, cd.categories_name");
  }

  $number_of_categories = tep_db_num_rows($categories_query);

  $rows = 0;

  $show_category_image_query = tep_db_query("select c.categories_id from " . TABLE_CATEGORIES . " c where c.categories_id = '" . $current_category_id . "' and c.sub_cats_show_image = '1'");

  if (tep_db_num_rows($show_category_image_query) > 0) {
    $show_thumb_image = true;
  }

  if ($show_thumb_image) { // BIG CREAM BUTTONS WITH IMAGE
    $category_images_type = 1;
  } else { // BIG PURPLE BUTTONS
    $category_images_type = 3;
  }

  $cat_page_string = '';

  while ($categories = tep_db_fetch_array($categories_query)) {

    $products_in_category = tep_count_products_in_category($categories['categories_id']);

    if ($products_in_category > 0) {

      $rows++;
      $cPath_new = tep_get_path($categories['categories_id']);
      if ($categories['parent_id'] == '44') {
        $sub_cat_name = str_replace('Cigars', '', $categories['categories_name']);
      } else {
        $sub_cat_name = $categories['categories_name'];
      }

      $cat_image_query = tep_db_query("select c.categories_image from " . TABLE_CATEGORIES . " c where c.categories_id = '" . $categories['categories_id'] . "'");

      $cat_image = tep_db_fetch_array($cat_image_query);

      $width = (int)(100 / MAX_DISPLAY_CATEGORIES_PER_ROW) . '%';

      if ($category_images_type == 1) {

        if ($current_category_id == 2589) { // luxury gifts
          $cat_page_string .= '<div class="pure-u-24-24 pure-u-sm-12-24">';
          $cat_image_width = 600;
          $cat_image_height = 600;
        } else {
          $cat_page_string .= '<div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-12-24 pure-u-lg-8-24 pure-u-xl-8-24">';
          $cat_image_width = 400;
          $cat_image_height = 400;
        }

        $cat_page_string .= '<div class="listing-cat-outer"><div class="listing-cat-label"><div class="listing-cat-label-inner"><a href="' . tep_href_link(FILENAME_DEFAULT, $cPath_new) . '">' . $sub_cat_name . '</a></div></div><a href="' . tep_href_link(FILENAME_DEFAULT, $cPath_new) . '">' . tep_image(DIR_WS_IMAGES . $cat_image['categories_image'], $sub_cat_name, $cat_image_width, $cat_image_height, 'class=pure-img', '1') . '</a></div></div>';
      } elseif ($category_images_type == 2) {

        $cat_page_string .= '<li class="cat_child_d active_cat"><a href="' . tep_href_link(FILENAME_DEFAULT, $cPath_new) . '">' . $sub_cat_name . '</a></li>';
      } elseif ($category_images_type == 3) {

        $cat_page_string .= '<div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-12-24 pure-u-lg-8-24 pure-u-xl-8-24"><div class="subcategory_name"><a class="button"   href="' . tep_href_link(FILENAME_DEFAULT, $cPath_new) . '">' . $sub_cat_name . '</a></div></div>';
      }
    }
  }

  /* needed for the new products module shown below    */
  $new_products_category_id = $current_category_id;

  // CATEGORY LISTING
?>
  <div id="inner-wrapper">
    <div class="pure-g">

      <div class="pure-u-24-24 pure-u-md-10-24 pure-u-lg-8-24 pure-u-xl-6-24">
        <div class="box-padding-both">
          <?php
          if ((USE_CACHE == 'true') && empty($SID)) {
            echo tep_cache_filter_menu();
          } else {
            include(DIR_WS_MODULES . 'custom_search_filter.php');
          }
          ?>
        </div>
      </div>

      <div class="pure-u-24-24 pure-u-md-14-24 pure-u-lg-16-24 pure-u-xl-18-24">

        <div class="pure-g">

          <div class="pure-u-24-24">
            <div class="box-padding-both">
              <?php

              if (!tep_check_cPath_array($cPath_array, 1847)) {
                echo '<h1>' . $category['categories_name'] . '</h1>';
              }

              if ($category_images_type == 1) {

                echo '<div class="listing-top-cats"><div class="pure-g">';
                echo $cat_page_string;
                echo '</a></div></div>';
              } elseif ($category_images_type == 2) {

                echo '<div class="listing-top-cats" style="float: left; background-color: #592249; color: #FFF;"><ul>';
                echo $cat_page_string;
                echo '</ul></div>';
              } elseif ($category_images_type == 3) {

                echo '<div class="pure-g" style="margin-top:20px;">';
                echo $cat_page_string;
                echo '</div>';
              }

              ?>
            </div>
          </div>

          <?php if ($the_category_description) { ?>
            <div class="pure-u-24-24">
              <div class="cat-desc-footer">
                <?php echo '<div class="cat-desc">' . $the_category_description; ?><br /><br />
                <?php if ($the_category_read_more) { ?>
                  <div class="read-more">Read More</div>
                  <div class="read-more-description"><?php echo $the_category_read_more; ?></div>
                <?php } ?>
                <?php echo '</div>'; ?>
              </div>
            </div>
          <?php } ?>

        </div>
      </div>
    </div>

  </div>
  </div>
<?php
  // END CATEGORY LISTING

  // START PRODUCT LISTING

} elseif ($category_depth == 'products' || (isset($_GET['manufacturers_id']) && !empty($_GET['manufacturers_id']))) {

  /* create column list   */
  $define_list = array(
    'PRODUCT_LIST_MODEL' => PRODUCT_LIST_MODEL,
    'PRODUCT_LIST_NAME' => PRODUCT_LIST_NAME,
    'PRODUCT_LIST_MANUFACTURER' => PRODUCT_LIST_MANUFACTURER,
    'PRODUCT_LIST_PRICE' => PRODUCT_LIST_PRICE,
    'PRODUCT_LIST_QUANTITY' => PRODUCT_LIST_QUANTITY,
    'PRODUCT_LIST_WEIGHT' => PRODUCT_LIST_WEIGHT,
    'PRODUCT_LIST_IMAGE' => PRODUCT_LIST_IMAGE,
    'PRODUCT_LIST_BUY_NOW' => PRODUCT_LIST_BUY_NOW
  );

  asort($define_list);

  $column_list = array();
  reset($define_list);

  while (list($key, $value) = each($define_list)) {
    if ($value > 0) $column_list[] = $key;
  }

  $select_column_list = '';

  for ($i = 0, $n = sizeof($column_list); $i < $n; $i++) {
    switch ($column_list[$i]) {
      case 'PRODUCT_LIST_MODEL':
        $select_column_list .= 'p.products_model, ';
        break;
      case 'PRODUCT_LIST_NAME':
        $select_column_list .= 'p.products_sort_order, pd.products_name, ';
        break;
      case 'PRODUCT_LIST_MANUFACTURER':
        $select_column_list .= 'm.manufacturers_name, ';
        break;
      case 'PRODUCT_LIST_QUANTITY':
        $select_column_list .= '';
        break;
      case 'PRODUCT_LIST_IMAGE':
        $select_column_list .= 'p.products_image, ';
        break;
      case 'PRODUCT_LIST_WEIGHT':
        $select_column_list .= 'p.products_weight, ';
        break;
    }
  }

  /* show the products of a specified manufacturer    */

  if (isset($_GET['manufacturers_id']) && !empty($_GET['manufacturers_id'])) {
    if (isset($_GET['filter_id']) && tep_not_null($_GET['filter_id'])) {

      /* We are asked to show only a specific category  */
      $listing_sql = "select " . $select_column_list . " p.products_id, p.manufacturers_id, p.products_quantity, p.products_price, p.products_free_shipping, p.products_date_available, p.products_date_added, p.products_tax_class_id, p.products_status, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price, p.review_average from " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_TO_STORES . " c2s, " . TABLE_PRODUCTS_TO_STORES . " p2s where p.manufacturers_id = m.manufacturers_id and m.manufacturers_id = '" . (int)$_GET['manufacturers_id'] . "' and p.products_id = p2c.products_id and pd.products_id = p2c.products_id and c.categories_id = p2c.categories_id and pd.language_id = '" . (int)$languages_id . "' and c.categories_status = '1' and c.categories_id = c2s.categories_id and c2s.store_cg = '1' and p.products_id = p2s.products_id and p2s.store_cg = '1' and p2c.categories_id = '" . (int)$_GET['filter_id'] . "' and p.hide_navigation != '1' and p.products_visible = '1'";
    } else {

      /* We show them all */
      $listing_sql = "select " . $select_column_list . " p.products_id, p.manufacturers_id, p.products_quantity, p.products_price, p.products_free_shipping, p.products_date_available, p.products_date_added,  p.products_tax_class_id, p.products_status, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price, p.review_average from " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_TO_STORES . " c2s, " . TABLE_PRODUCTS_TO_STORES . " p2s where p.products_id = p2c.products_id and c.categories_id = p2c.categories_id and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "' and p.manufacturers_id = m.manufacturers_id and c.categories_status in (1,2,3,4,6) and c.categories_id = c2s.categories_id and c2s.store_cg = '1' and p.products_id = p2s.products_id and p2s.store_cg = '1' and m.manufacturers_id = '" . (int)$_GET['manufacturers_id'] . "' and p.hide_navigation != '1' and p.products_visible = '1'";
    }
  } else {

    /* show the products in a given category  */
    if (isset($_GET['filter_id']) && tep_not_null($_GET['filter_id'])) {

      /* We are asked to show only specific category  */
      $listing_sql = "select " . $select_column_list . " p.products_id, p.manufacturers_id, p.products_quantity, p.products_price, p.products_free_shipping, p.products_date_available, p.products_date_added, p.products_tax_class_id, p.products_status, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price, p.review_average from " . TABLE_PRODUCTS . " p left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_MANUFACTURERS . " m, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_TO_STORES . " c2s, " . TABLE_PRODUCTS_TO_STORES . " p2s where p.products_id = p2c.products_id and c.categories_id = p2c.categories_id and c.categories_status in (1,2,3,4,6) and c.categories_id = c2s.categories_id and c2s.store_cg = '1' and p.products_id = p2s.products_id and p2s.store_cg = '1' and pd.products_id = p2c.products_id and p.manufacturers_id = m.manufacturers_id and m.manufacturers_id = '" . (int)$_GET['filter_id'] . "' and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$current_category_id . "' and p.hide_navigation != '1' and p.products_visible = '1'";
    } elseif (isset($_GET['cf_id']) && tep_not_null($_GET['cf_id'])) { // the 'linked' category filter on christmas gifts page

      // 1) get the products in the current category
      $quick_products_query = tep_db_query("select p2c.products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p2c.categories_id = '" . (int)$current_category_id . "'");

      while ($quick_products = tep_db_fetch_array($quick_products_query)) {
        $pf_id_array[] = $quick_products['products_id'];
      }

      $pf_id_array = array_unique($pf_id_array); // sort out doubles

      // 2) get the categories under the selected filter category
      $cf_id_array[] = $_GET['cf_id'];

      for ($z = 0; $z < count($cf_id_array); $z++) {
        $categorie_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int)$cf_id_array[$z] . "'");
        while ($categorie = tep_db_fetch_array($categorie_query)) {
          $cf_id_array[] = $categorie['categories_id'];
        }
        $cf_id_array = array_unique($cf_id_array); // sort out doubles
      }

      // 3) see where both match
      $listing_sql = "select " . $select_column_list . " p.products_id, p.manufacturers_id, p.products_quantity, p.products_price, p.products_free_shipping, p.products_date_available, p.products_date_added, p.products_tax_class_id, p.products_status, p.products_price as final_price, p.review_average from " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2c.products_id and pd.products_id = p2c.products_id and pd.language_id = '" . (int)$languages_id . "' and (p2c.products_id in (" . implode(', ', $pf_id_array) . ") and p2c.categories_id in (" . implode(', ', $cf_id_array) . ")) and p.hide_navigation != '1' and p.products_visible = '1'";
    } else {

      /* We show them all     */
      $listing_sql = "select " . $select_column_list . $additional_select . " p.products_id, p.manufacturers_id, p.products_quantity, p.products_price, p.products_free_shipping, p.products_date_available, p.products_date_added, p.products_tax_class_id, p.products_status, IF(s.status, s.specials_new_products_price, NULL) as specials_new_products_price, IF(s.status, s.specials_new_products_price, p.products_price) as final_price, p.review_average from " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS . " p left join " . TABLE_MANUFACTURERS . " m on p.manufacturers_id = m.manufacturers_id left join " . TABLE_SPECIALS . " s on p.products_id = s.products_id, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c" . $additional_tables . " where p.products_id = p2c.products_id and pd.products_id = p2c.products_id and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$current_category_id . "' and p.hide_navigation != '1' and p.products_visible = '1' " . $additional_where . " ";
    }
  }

  if ((!isset($_GET['sort'])) || (!preg_match('/^[1-8][ad]$/', $_GET['sort'])) || (substr($_GET['sort'], 0, 1) > sizeof($column_list))) {
    for ($i = 0, $n = sizeof($column_list); $i < $n; $i++) {
      if ($column_list[$i] == 'PRODUCT_LIST_NAME') {
        $_GET['sort'] = $i + 1 . 'a';
        if ($events_page) { // temp fix
          $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, p.products_event_date IS NULL, p.products_event_date asc, pd.products_name";
        } else {
          $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, pd.products_name";
        }
        break;
      }
    }
  } else {

    $sort_col = substr($_GET['sort'], 0, 1);
    $sort_order = substr($_GET['sort'], 1);

    switch ($column_list[$sort_col - 1]) {

      case 'PRODUCT_LIST_MODEL':
        $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_model " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
        break;
      case 'PRODUCT_LIST_NAME':
        if ($events_page) { // temp fix
          $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, p.products_event_date IS NULL, p.products_event_date asc, pd.products_name " . ($sort_order == 'd' ? 'desc' : '');
        } else {
          $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, pd.products_name " . ($sort_order == 'd' ? 'desc' : '');
        }
        break;
      case 'PRODUCT_LIST_MANUFACTURER':
        $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), m.manufacturers_name " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
        break;
      case 'PRODUCT_LIST_QUANTITY':
        $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_quantity " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
        break;
      case 'PRODUCT_LIST_IMAGE':
        $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), pd.products_name";
        break;
      case 'PRODUCT_LIST_WEIGHT':
        $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_weight " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
        break;
      case 'PRODUCT_LIST_PRICE':
        $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), final_price " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
        break;
    }
  }

  if (tep_check_cPath_array($cPath_array, 2365)) {
    $events_page = true;
  }

  if (!$sort_id && $events_page) { // events page
    $sort_id = 9;
  }

  if ($sort_id) {

    if ($events_page) {
      if ($lf_id == 1) { // Liverpool
        $listing_sql .= " and p2sl.stock_liverpool > 0 ";
      } elseif ($lf_id == 2) { // Chester
        $listing_sql .= " and p2sl.stock_chester > 0 ";
      } elseif ($lf_id == 3) { // London
        $listing_sql .= " and p2sl.stock_london > 0 ";
      } elseif ($lf_id == 10) { // St James
        $listing_sql .= " and p2sl.stock_london_cg > 0 ";
      } elseif ($lf_id == 4) { // Mayfair
        $listing_sql .= " and p2sl.stock_mayfair > 0 ";
      } elseif ($lf_id == 5) { // Norfolk
        $listing_sql .= " and p2sl.stock_norfolk > 0 ";
      } elseif ($lf_id == 7) { // Knutsford
        $listing_sql .= " and p2sl.stock_knutsford > 0 ";
      } elseif ($lf_id == 12) { // Leeds
        $listing_sql .= " and p2sl.stock_leeds > 0 ";
      } elseif ($lf_id == 13) { // Edinburgh
        $listing_sql .= " and p2sl.stock_edinburgh > 0 ";
      }
      if ($mf_id > 0) {
        $listing_sql .= " and MONTH(p.products_event_date) = " . $mf_id . " ";
      }
    }

    if ($sort_id == 1) {
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.products_ordered desc ";
    } elseif ($sort_id == 2) { // 2
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.review_average desc ";
    } elseif ($sort_id == 3) { // 3
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.products_price asc ";
    } elseif ($sort_id == 4) { // 4
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.products_price desc ";
    } elseif ($sort_id == 5) { // 5
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, pd.products_name asc ";
    } elseif ($sort_id == 6) { // 6
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, pd.products_name desc ";
    } elseif ($sort_id == 7) { // 7
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.products_date_added desc ";
    } elseif ($sort_id == 9) { // 9
      $listing_sql .= " order by field(p.products_status, 3, 2, 4, 6), p.products_event_date IS NULL, p.products_event_date asc ";
    } else {
      $listing_sql .= $listing_sort_sql; // default system
    }
  } else {
    $sort_id = 5;
    $listing_sql .= $listing_sort_sql; // default system
  }






  $catname = HEADING_TITLE;

  if (isset($_GET['manufacturers_id']) && !empty($_GET['manufacturers_id'])) {

    $image = tep_db_query("select manufacturers_image, manufacturers_name as catname from " . TABLE_MANUFACTURERS . " where manufacturers_id = '" . (int)$_GET['manufacturers_id'] . "'");

    $image = tep_db_fetch_array($image);

    $catname = $image['catname'];
  } elseif ($current_category_id) {

    $image = tep_db_query("select c.categories_image, cd.categories_name as catname from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = '" . (int)$current_category_id . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int)$languages_id . "'");

    $image = tep_db_fetch_array($image);

    $catname = $image['catname'];
  }


  if ($show_cats_above_listing && $categories_string_final) {
    $listing_top_cats = '<div class="listing-top-cats" style="float: left; background-color: #592249; color: #FFF;"><ul>' . $categories_string_final . '</ul></div>';
  }

  if ($show_brands_above_listing && !$mobile_view) {

    /* optional Product List Filter  */
    if (isset($_GET['manufacturers_id']) && !empty($_GET['manufacturers_id'])) {

      $filterlist_sql = "select distinct c.categories_id as id, cd.categories_name as name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd, " . TABLE_PRODUCTS_TO_STORES . " p2s, " . TABLE_CATEGORIES_TO_STORES . " c2s where p.products_status in (1,2,3,4,6) and p.products_id = p2c.products_id and p2c.categories_id = c.categories_id and p2c.categories_id = cd.categories_id and c.categories_status in (1,2,3,4,6) and cd.language_id = '" . (int)$languages_id . "' and p.manufacturers_id = '" . (int)$_GET['manufacturers_id'] . "' and p.products_id = p2s.products_id and p2s.store_cg = '1' and c.categories_id = c2s.categories_id and c2s.store_cg = '1' order by cd.categories_name";
    } else {

      $filterlist_sql = "select distinct m.manufacturers_id as id, m.manufacturers_name as name from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c, " . TABLE_CATEGORIES . " c, " . TABLE_MANUFACTURERS . " m, " . TABLE_PRODUCTS_TO_STORES . " p2s, " . TABLE_CATEGORIES_TO_STORES . " c2s where p.products_status in (1,2,3,4,6) and p.manufacturers_id = m.manufacturers_id and p.products_id = p2c.products_id and c.categories_id = p2c.categories_id and p2c.categories_id = '" . (int)$current_category_id . "' and c.categories_status in (1,2,3,4,6) and p.products_id = p2s.products_id and p2s.store_cg = '1' and p2c.categories_id = c2s.categories_id and c2s.store_cg = '1' order by m.manufacturers_name";
    }

    $filterlist_query = tep_db_query($filterlist_sql);

    if (tep_db_num_rows($filterlist_query) > 1) {
      while ($filterlist = tep_db_fetch_array($filterlist_query)) {
        if ($filterlist['id'] == $_GET['filter_id']) {
          $brands_string_final .= '<li class="cat_child_a active_cat">2<a class="purple" href="';
        } else {
          $brands_string_final .= '<li class="cat_child_a">1<a href="';
        }
        $brands_string_final .= '' . tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('filter_id')) . '&filter_id=' . $filterlist['id']) . '">';
        $brands_string_final .= $filterlist['name'];
        $brands_string_final .= '</a>';
        $brands_string_final .= '</li>';
      }

      $listing_top_cats .= '<div class="listing-top-cats"><ul>' . $brands_string_final . '</ul></div>';
    }
  }
?>

  <div id="inner-wrapper">
    <div class="pure-g">

      <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-7-24 pure-u-xl-5-24">
        <div class="box-padding-both">
          <?php
          if ((USE_CACHE == 'true') && empty($SID)) {
            echo tep_cache_filter_menu();
          } else {
            include(DIR_WS_MODULES . 'custom_search_filter.php');
          }
          ?>
        </div>
      </div>

      <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-17-24 pure-u-xl-19-24">

        <div class="pure-g">

          <div class="pure-u-24-24">

            <?php if ($current_category_id == 676) { ?>
              <img src="design/generic/styles/christmas/images/christmas-banner.jpg" width="100%">
            <?php } elseif (tep_check_cPath_array($cPath_array, 1847)) { ?>

            <?php } else { ?>
              <div class="box-padding-both">
                <h1><?php echo $catname; ?></h1>
              </div>
            <?php } ?>

            <?php if ($show_brands_above_listing && !$mobile_view) {
              echo $listing_top_cats;
            } ?>

          </div>

          <?php if ($the_category_description) { ?>
            <div class="pure-u-24-24">
              <?php if (tep_check_cPath_array($cPath_array, 1847)) { ?>
                <div class="davidoff-cat-desc">
                  <div class="davidoff-cat-desc-inner">
                    <div class="pure-g">
                      <div class="pure-u-24-24 pure-u-sm-8-24 pure-u-md-6-24 pure-u-lg-6-24 pure-u-xl-5-24"><img src="design/generic/images/davidoff-logo-gold.png" class="pure-img" /></div>
                      <div class="pure-u-24-24 pure-u-sm-16-24 pure-u-md-18-24 pure-u-lg-18-24 pure-u-xl-19-24 davidoff-flex">
                        <h1><?php echo $catname; ?></h1>
                      </div>
                      <div class="pure-u-24-24">
                        <div class="davidoff-cat-desc-text"><?php echo $the_category_description; ?></div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
          <?php
              } else {
                echo '<div class="cat-desc">' . $the_category_description;
          ?>
            <?php if ($the_category_read_more) { ?>
              <div class="read-more">Read More</div>
              <div class="read-more-description"><?php echo $the_category_read_more; ?></div>
            <?php } ?>
            <?php echo '</div>'; ?>
          <?php
              }
          ?>
        </div>
      <?php } ?>

      <?php include(DIR_WS_MODULES . FILENAME_PRODUCT_LISTING); ?>

      </div>

    </div>

  </div>
  </div>
<?php // END PROD LISTING PAGE

} else {  // START HOME PAGE

  if ($mobile_view) {
    //echo '<div style="background-color: #000; text-align: center; margin-bottom: 15px;"><a href="specials.php"><img style="margin: 0 auto; max-width: 100%;" src="images/homebanners/large/cyber_monday_mobile_new.jpg" alt="Cyber Monday Sale" title="Cyber Monday Sale" /></a></div>';

    // echo '<div style="background-color: #BD2126; text-align: center; margin-bottom: 15px;"><a href="specials.php"><img style="margin: 0 auto;  width: 100%; max-width: 700px;" src="images/homebanners/large/jan_sale_mobile.jpg" alt="January Sale" title="January Sale" /></a></div>';

    //  echo '<div style="background: url(design/css2021/themes/christmas/images/chirstmas-gifts-banner-bg.png); background-color: #BD2126; text-align: center; padding: 0 20px;"><a href="gifts-christmas-gifts-c-318_2172.html"><img style="margin: 20px auto; width: 100%; max-width: 523px;" src="design/css2021/themes/christmas/images/chrstmas-gifts-home-banner.png" alt="Christmas Gifts" title="Christmas Gifts" /></a></div>';


  } else {

    // echo '<div style="background-color: #000; text-align: center; background-image: url(design/generic/images/cyber_monday_bg.jpg);"><a href="specials.php"><img style="margin: 0 auto; box-shadow: 12px 0 15px -4px rgba(0, 0, 0, 0.8), -12px 0 8px -4px rgba(0, 0, 0, 0.8);" src="images/homebanners/large/cyber_monday_desktop_new.jpg" alt="January Sale" title="Cyber Monday Sale" /></a></div>';
    // echo '<div style="background-color: #BD2126; text-align: center;"><a href="specials.php"><img style="margin: 0 auto; width: 100%; max-width: 1200px;" src="images/homebanners/large/january_sale_desktop.jpg" alt="January Sale" title="January Sale" /></a></div>';

    //  echo '<div style="background: url(design/css2021/themes/christmas/images/chirstmas-gifts-banner-bg.png); background-color: #BD2126; text-align: center;"><a href="gifts-christmas-gifts-c-318_2172.html"><img style="margin: 30px auto; width: 100%; max-width: 523px;" src="design/css2021/themes/christmas/images/chrstmas-gifts-home-banner.png" alt="Christmas Gifts" title="Christmas Gifts" /></a></div>';

  }

  if ($design != '2025') {
    if ($design_theme == '2021') {
      include(DIR_WS_MODULES . 'home_page_2021.php');
    } elseif ($design_theme == 'generic') {
      include(DIR_WS_MODULES . 'home_page_generic.php');
    }
  } else {
    include(DIR_WS_MODULES . 'home_page_2025.php');
  }
} // END HOME PAGE

require(DIR_WS_INCLUDES . 'template_bottom.php');
require(DIR_WS_INCLUDES . 'application_bottom.php');
