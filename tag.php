<?php
require('includes/application_top.php');

// Get the tag slug from the URL (e.g., /shop/fathers-day or tag.php?tag=fathers-day)
$tag_slug = isset($_GET['tag']) ? $_GET['tag'] : '';
$tag_name = str_replace('-', ' ', $tag_slug); // Convert slug to tag name

$page_title_text = ucwords($tag_name) . ' | ' . (defined('STORE_NAME') ? STORE_NAME : 'Online Store');

$page_meta_desc = 'Discover a wide range of high-quality ' . htmlspecialchars(strtolower($tag_name)) . ' at ' .
    (defined('STORE_NAME') ? STORE_NAME : 'our online shop') .
    '. Shop now and enjoy exclusive offers, fast delivery, and exceptional service.';


// SEO meta tags
$page_title = 'Products tagged with: ' . htmlspecialchars(ucwords($tag_name));
$meta_description = 'Browse all products tagged with ' . htmlspecialchars($tag_name) . ' at ' . (defined('STORE_NAME') ? STORE_NAME : 'Our Store');

require(DIR_WS_INCLUDES . 'template_top.php');
// Query products with this tag
$listing_sql = "select 
    p.products_id, 
    p.products_model, 
    pd.products_name, 
    p.products_image, 
    p.products_price, 
    p.products_tax_class_id, 
    p.products_date_added
 from products p
 join products_tags t on p.products_id = t.product_id
 join products_description pd on p.products_id = pd.products_id and pd.language_id = 1
 where t.tag = '" . tep_db_input($tag_name) . "'
 and p.products_status = 1
 order by p.products_date_added desc";

?>
<!-- SEO meta tags -->


<div id="inner-wrapper">
    <div class="pure-g">

        <div class="pure-u-24-24">
            <div class="box-padding-both">
                <h1><?php echo htmlspecialchars(ucwords($tag_name)); ?></h1>
            </div>
        </div>

        <div class="pure-u-24-24">
            <div class="box-padding-half">
                <div class="pure-g">
                    <?php

                    include(DIR_WS_MODULES . FILENAME_PRODUCT_LISTING);

                    ?>
                </div>
            </div>
        </div>

    </div>
</div>
<?php require(DIR_WS_INCLUDES . 'template_bottom.php');
