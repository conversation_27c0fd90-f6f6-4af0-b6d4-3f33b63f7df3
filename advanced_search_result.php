<?php
/*
  $Id: advanced_search_result.php,v 1.72 2003/06/23 06:50:11 project3000 Exp $
*/

require('includes/application_top.php');

if (isset($_GET['museum']) && tep_not_null($_GET['museum'])) {
  $previous_products = 1;
}

if (isset($_GET['fav']) && tep_not_null($_GET['fav'])) {
  $favourite_products = 1;
}

if (isset($_GET['sort_id']) && is_numeric($_GET['sort_id'])) {
  if (!tep_session_is_registered('sort_id')) {
    tep_session_register('sort_id');
  }
  $sort_id = $_GET['sort_id'];
}

if (isset($_GET['display']) && $_GET['display'] == 'all') {
  $page_title_text .= 'Showing all ';
}

$page_heading = 'Search Results';

if (isset($_GET['cachename'])) {
  $remove_header = true;
  $remove_footer = true;
  if ($_GET['cachename'] == 'medium-strength-cigars') {
    $page_heading = 'Medium Strength Cigars';
  }
} elseif (isset($_GET['cat_id']) && tep_not_null($_GET['cat_id']) && is_numeric($_GET['cat_id'])) {
  $page_heading = tep_get_categories_name($_GET['cat_id']);
} elseif ($favourite_products) {
  $page_heading = 'My Favourites';
}

if (isset($_GET['keywords']) && tep_not_null($_GET['keywords']) && $_GET['keywords'] != 'Enter Search' && $_GET['keywords'] != 'Add Keywords') {

  $keywords = $_GET['keywords'];
  // remove dots and dashes etc and replace with a space
  $keywords = preg_replace("/[^A-Za-z0-9 ]/", ' ', $keywords);

  if (!tep_parse_search_string($keywords, $search_keywords)) {
    $error = true;
  }

  if (isset($search_keywords) && (sizeof($search_keywords) > 0) && (!$error)) {

    //$where_str .= "("; // used above
    for ($i = 0, $n = sizeof($search_keywords); $i < $n; $i++) {
      switch ($search_keywords[$i]) {
        case '(':
        case ')':
        case 'and':
        case 'or':
          // $where_str .= " " . $search_keywords[$i] . " ";
          break;
        default:

          $keyword = tep_db_prepare_input($search_keywords[$i]);

          $reconstructed_keywords .= $keyword . ' ';

          if (!$order_by_str) {
            // sort by whole words found first, then product names starting with
            // $order_by_str = "pd.products_name REGEXP '[[:<:]]" . tep_db_input($keyword) . "[[:>:]]' desc, pd.products_name like '" . tep_db_input($keyword) . "%' desc, p.products_date_available desc,";
            // sort by whole words found first, then product names including 
            // $order_by_str = "pd.products_name REGEXP '[[:<:]]" . tep_db_input($keyword) . "[[:>:]]' desc, pd.products_name like '%" . tep_db_input($keyword) . "%' desc, p.products_date_available desc,";
            $order_by_str = "(pd.products_name REGEXP '[[:<:]]" . tep_db_input($keyword) . "[[:>:]]' OR p.products_model LIKE '%" . tep_db_input($keyword) . "%') desc, 
                 pd.products_name like '%" . tep_db_input($keyword) . "%' desc, 
                 p.products_date_available desc,";
          }

          break;
      }
    }
  }

  $page_title_text = $reconstructed_keywords . ' Search Results';

  $array = array("Returns", "returns", "Form", "form");
  $array1 = array("Novillo", "novillo");
  $array2 = array("la Rica", "La rica", "La Rica", "la rica");

  if (0 < count(array_intersect(array_map('strtolower', explode(' ', $reconstructed_keywords)), $array))) {
    tep_redirect(tep_href_link('html/Product-Return-Form.pdf'));
  }

  if (tep_string_contains($reconstructed_keywords, $array1)) {
    $alt_product1 = true;
  }

  if (tep_string_contains($reconstructed_keywords, $array2)) {
    $alt_product2 = true;
  }
} else {
  $page_title_text .= 'Search Results';
}

if (isset($_GET['cat_id']) && tep_not_null($_GET['cat_id'])) {
  $filter = 1;
  $filter_text .= 'category';
}
if (isset($_GET['range']) && tep_not_null($_GET['range']) && $_GET['range'] != '0') {
  if ($filter == 1) {
    $filter_text .= ' & ';
  }
  $filter = 1;
  $filter_text .= 'price range';
}

if (isset($_GET['22'])) {
  if (is_array($_GET['22'])) {
    if (!in_array('0', $_GET['22'], true)) {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'region';
    }
  } else {
    if ($_GET['22'] != '0') {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'region';
    }
  }
}

if (isset($_GET['23'])) {
  if (is_array($_GET['23'])) {
    if (!in_array('0', $_GET['23'], true)) {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'ring gauge';
    }
  } else {
    if ($_GET['23'] != '0') {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'ring gauge';
    }
  }
}

if (isset($_GET['24'])) {
  if (is_array($_GET['24'])) {
    if (!in_array('0', $_GET['24'], true)) {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'strength';
    }
  } else {
    if ($_GET['24'] != '0') {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'strength';
    }
  }
}

if (isset($_GET['26'])) {
  if (is_array($_GET['26'])) {
    if (!in_array('0', $_GET['26'], true)) {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'length';
    }
  } else {
    if ($_GET['26'] != '0') {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'length';
    }
  }
}

if (isset($_GET['35'])) {
  if (is_array($_GET['35'])) {
    if (!in_array('0', $_GET['35'], true)) {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'vitola';
    }
  } else {
    if ($_GET['35'] != '0') {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'vitola';
    }
  }
}

if (isset($_GET['36'])) {
  if (is_array($_GET['36'])) {
    if (!in_array('0', $_GET['36'], true)) {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'packaging';
    }
  } else {
    if ($_GET['36'] != '0') {
      if ($filter == 1) {
        $filter_text .= ' & ';
      }
      $filter = 1;
      $filter_text .= 'packaging';
    }
  }
}

if (isset($_GET['sort']) && tep_not_null($_GET['sort'])) {
  $sort_type = $_GET['sort'];
  if ($sort_type == '2a') {
    $page_title_text .= ' sorted by name ascendingly ';
  }
  if ($sort_type == '2d') {
    $page_title_text .= ' sorted by name descendingly ';
  }
  if ($sort_type == '3a') {
    $page_title_text .= ' sorted by price ascendingly ';
  }
  if ($sort_type == '3d') {
    $page_title_text .= ' sorted by price descendingly ';
  }
}

if (isset($_GET['page']) && is_numeric($_GET['page'])) {
  $page_num = ' (Page ' . $_GET['page'] . ')';
}

if ($filter) {
  $page_title_text .= ' filtered by ' . $filter_text;
}
if ($page_num) {
  $page_title_text .= $page_num;
}


require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ADVANCED_SEARCH);


if (!$previous_products) {
  $breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ADVANCED_SEARCH));
  $breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ADVANCED_SEARCH_RESULT, tep_get_all_get_params(), 'NONSSL', true, false));
}

require(DIR_WS_INCLUDES . 'template_top.php');

// create column list
$define_list = array(
  'PRODUCT_LIST_MODEL' => PRODUCT_LIST_MODEL,
  'PRODUCT_LIST_NAME' => PRODUCT_LIST_NAME,
  'PRODUCT_LIST_MANUFACTURER' => PRODUCT_LIST_MANUFACTURER,
  'PRODUCT_LIST_PRICE' => PRODUCT_LIST_PRICE,
  'PRODUCT_LIST_QUANTITY' => PRODUCT_LIST_QUANTITY,
  'PRODUCT_LIST_WEIGHT' => PRODUCT_LIST_WEIGHT,
  'PRODUCT_LIST_IMAGE' => PRODUCT_LIST_IMAGE,
  'PRODUCT_LIST_BUY_NOW' => PRODUCT_LIST_BUY_NOW
);

asort($define_list);

$column_list = array();
reset($define_list);
while (list($key, $value) = each($define_list)) {
  if ($value > 0) $column_list[] = $key;
}

$select_column_list = '';

for ($i = 0, $n = sizeof($column_list); $i < $n; $i++) {
  switch ($column_list[$i]) {
    case 'PRODUCT_LIST_MODEL':
      $select_column_list .= 'p.products_model, ';
      break;
    case 'PRODUCT_LIST_MANUFACTURER':
      $select_column_list .= 'm.manufacturers_name, ';
      break;
    case 'PRODUCT_LIST_QUANTITY':
      $select_column_list .= 'p.products_quantity, ';
      break;
    case 'PRODUCT_LIST_IMAGE':
      $select_column_list .= 'p.products_image, ';
      break;
    case 'PRODUCT_LIST_WEIGHT':
      $select_column_list .= 'p.products_weight, ';
      break;
  }
}

if ((!isset($_GET['sort'])) || (!preg_match('/[1-8][ad]/', $_GET['sort'])) || (substr($_GET['sort'], 0, 1) > sizeof($column_list))) {
  for ($i = 0, $n = sizeof($column_list); $i < $n; $i++) {
    if ($column_list[$i] == 'PRODUCT_LIST_NAME') {
      $_GET['sort'] = $i + 1 . 'a';
      //$listing_sort_sql = ' order by ' . $order_by_str . ' field(p.products_status, 3, 2, 4, 6), pd.products_name';
      $listing_sort_sql = ' order by ' . $order_by_str . ' p.products_status, p.products_ordered desc, pd.products_name';
      break;
    }
  }
} else {
  $sort_col = substr($_GET['sort'], 0, 1);
  $sort_order = substr($_GET['sort'], 1);
  switch ($column_list[$sort_col - 1]) {
    case 'PRODUCT_LIST_MODEL':
      $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_model " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
      break;
    case 'PRODUCT_LIST_NAME':
      // $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), pd.products_name " . ($sort_order == 'd' ? "desc" : "");
      $listing_sort_sql = ' order by ' . $order_by_str . ' p.products_status, p.products_ordered desc, pd.products_name';
      break;
    case 'PRODUCT_LIST_MANUFACTURER':
      $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), m.manufacturers_name " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
      break;
    case 'PRODUCT_LIST_QUANTITY':
      $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_quantity " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
      break;
    case 'PRODUCT_LIST_IMAGE':
      $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), pd.products_name";
      break;
    case 'PRODUCT_LIST_WEIGHT':
      $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_weight " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
      break;
    case 'PRODUCT_LIST_PRICE':
      $listing_sort_sql = " order by field(p.products_status, 3, 2, 4, 6), p.products_price " . ($sort_order == 'd' ? "desc" : "") . ", pd.products_name";
      break;
  }
}

if ($sort_id) {

  if ($sort_id == 1) {
    $order_str .= " order by field(p.products_status, 3, 2, 4, 6), p.products_ordered desc ";
  } elseif ($sort_id == 2) { // 2
    $order_str .= " order by field(p.products_status, 3, 2, 4, 6), p.review_average desc ";
  } elseif ($sort_id == 3) { // 3
    $order_str .= " order by field(p.products_status, 3, 2, 4, 6), p.products_price asc ";
  } elseif ($sort_id == 4) { // 4
    $order_str .= " order by field(p.products_status, 3, 2, 4, 6), p.products_price desc ";
  } elseif ($sort_id == 5) { // 5
    $order_str .= " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, pd.products_name asc ";
  } elseif ($sort_id == 6) { // 6
    $order_str .= " order by field(p.products_status, 3, 2, 4, 6), p.products_sort_order, pd.products_name desc ";
  } elseif ($sort_id == 7) { // 7
    $order_str .= " order by field(p.products_status, 3, 2, 4, 6), p.products_date_added desc ";
  } elseif ($sort_id == 8) { // 8 Default by relevance
    $order_str = ' order by ' . $order_by_str . ' p.products_status, p.products_ordered desc, pd.products_name';
  } else {
    $order_str .= $listing_sort_sql; // default system
  }
} else {
  $sort_id = 8;
  $order_str .= $listing_sort_sql; // default system
}

if ($previous_products) {

?>

  <div class="pure-g">
    <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-6-24 pure-u-xl-4-24" style="background-color:#333;">
      <?php require(DC_BLOCKS . 'cigar_library.php'); ?>
    </div>
    <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-18-24 pure-u-xl-20-24">
      <div class="pure-g">

        <div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-12-24 pure-u-lg-12-24 pure-u-xl-12-24">
          <div class="box-padding-both">
            <h1>C.Gars Museum</h1>
          </div>
        </div>

        <div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-12-24 pure-u-lg-12-24 pure-u-xl-12-24">
          <?php

          $default_search_text = 'Museum Search';

          $museum_search_string .= '<div class="pure-u-24-24"><div id="museum-search">';
          $museum_search_string .= '<div class="submit_button"><input name="search" value="" type="submit"/></div>';
          $museum_search_string .= tep_draw_input_field('keywords', $default_search_text, 'id="search_input" onfocus="if(this.value == \'' . $default_search_text . '\') {this.value = \'\';}" onblur="if (this.value == \'\') {this.value = \'' . $default_search_text . '\';}"') . '&nbsp;' . tep_hide_session_id() . '';
          $museum_search_box_contents = tep_draw_form('museum_quick_find', tep_href_link(FILENAME_ADVANCED_SEARCH_RESULT, '', 'NONSSL', false), 'get') . $museum_search_string . '<input type="hidden" id="museum" name="museum" value="1" /></form></div></div>';

          echo $museum_search_box_contents;

          list($products, $listing_sql) = tep_site_search();

          $listing_sql = $listing_sql . $order_str;

          ?>
        </div>
        <div class="pure-u-24-24">
          <div class="box-padding-half">
            <div class="pure-g">
              <?php include(DIR_WS_MODULES . FILENAME_PRODUCT_LISTING); ?>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<?php

} else { // not museum

  echo '<div id="inner-wrapper">
         <div class="pure-g">
	        <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-8-24 pure-u-xl-5-24">
	         <div class="box-padding-both">';

  include(DIR_WS_MODULES . 'custom_search_filter.php');

  echo '
  </div>
	     </div>';

  echo '<div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-16-24 pure-u-xl-19-24">
      <div class="pure-g">';

  if ($alt_product1 || $alt_product2) {

    if ($alt_product1) {
      $alt_keyword1 = 'mitchellero';
      $alt_keyword2 = 'Mitchellero';
      $operator = 'or';
      $we_think_youll_like_title = 'Chinchalero Novillos';
    }
    if ($alt_product2) {
      $alt_keyword1 = 'Torano Carlin';
      $alt_keyword2 = 'Torano Palmita';
      $operator = 'or';
      $we_think_youll_like_title = 'La Rica cigars';
    }

    echo '<div class="pure-u-24-24">';
    require(DIR_WS_MODULES . 'we_think_you_will_like_products.php');
    echo '</div>';
  }

  echo '<div class="pure-u-24-24"><div class="box-padding-both">';
  echo '<h1>' . $page_heading . '</h1>';
  echo '</div></div>';

  list($products, $listing_sql) = tep_site_search();

  $listing_sql = $listing_sql . $order_str;

  require(DIR_WS_MODULES . FILENAME_PRODUCT_LISTING);

  echo '</div></div>';

  echo '</div></div>';
}

require(DIR_WS_INCLUDES . 'template_bottom.php');
require(DIR_WS_INCLUDES . 'application_bottom.php');
