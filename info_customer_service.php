<?php

  require('includes/application_top.php');

  $basefol = "html/";
  $fil = $_GET['fol'];
  $info_search_keywords = $_GET['search'];
  $slash = "/";
  $fol = $fol.$slash;
  $file = $_GET['id']; // Replace id with whatever you want to use, eg ?id=page
  $dirty = array("..","html/");
  $clean = array("");
  $file = str_replace($dirty, $clean, $file); // Prevent people from viewing root files
  $new = $_GET['title'];
  $title = htmlentities($new);
  $page_output = '';
  
  if (!isset($_GET['id']) || !$file) {
   $file = 'helpdesk.htm';
  }


   if($file != 'home') { // needed for all system delete after 01/01/2016
    if (!file_exists($basefol . $file)){
     if(tep_redirect_link(3)){
       $page_title_text = 'Page not found';
       $page_not_found = true;
     }
    }
   }

  $breadcrumb->add($title, '');  
  
if (file_exists($basefol . $file) && $file) {

    ob_start();
    include($basefol . $file);
    $page_output = ob_get_clean();

    // Function to extract content between tags
    function extract_tag_content($html, $tag) {
        $start_tag = '<' . $tag . '>';
        $end_tag = '</' . $tag . '>';

        $start_pos = stripos($html, $start_tag);
        if ($start_pos !== false) {
            $start_pos += strlen($start_tag);
            $end_pos = stripos($html, $end_tag, $start_pos);
            if ($end_pos !== false) {
                return substr($html, $start_pos, $end_pos - $start_pos);
            }
        }
        return false;
    }

    // First, try to extract <header>
    $info_page_header_text = extract_tag_content($page_output, 'header');

    // Only extract <title> if no <header> was found
    $page_title_text = false;
    if ($info_page_header_text === false) {
        $page_title_text = extract_tag_content($page_output, 'title');
        if ($page_title_text === false) {
            $page_title_text = extract_tag_content($page_output, 'TITLE');
        }
    }

    // Always remove <title> tags from content
    $page_output = preg_replace('/<title[^>]*>[\s\S]*?<\/title[^>]*>/i', '', $page_output);
}

  
  require(DIR_WS_INCLUDES . 'template_top.php'); 
   
  if($file != 'price_thrashers.html') {  
?>

<?php
  }
  if ($page_not_found) {
   include(DIR_WS_MODULES . '404.php');
  } else { 
?>

<div id="inner-wrapper"> 
 <div class="pure-g">     
   <div class="pure-u-24-24">
	<div class="cigar-library-page">
	 <div class="box-padding-both">
	   
<?php
 if (!$info_search_keywords) {
   if ($page_output){
    echo $page_output;	
   } 
 }	
?>
    </div>
   </div>
  </div>
 </div>
</div>

<?php
  
  }

  require(DIR_WS_INCLUDES . 'template_bottom.php');
  require(DIR_WS_INCLUDES . 'application_bottom.php');
?>