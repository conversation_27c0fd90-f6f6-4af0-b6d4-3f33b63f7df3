<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');

$loyalty_settings = tep_loyalty_scheme_logic($customer_id);

if(MAILCHIMP_ENABLED == 'true') {
  require(DIR_WS_MODULES . 'mailchimp/mailchimpRoot.php');
  $mailchimp = new Mailchimp(MAILCHIMP_API_KEY); 
}

// PWA BOF
if (isset($_GET['guest']) && $cart->count_contents() < 1) tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
// PWA EOF
if (isset($_GET['guest']) || isset($_POST['guest'])) {
  if (tep_session_is_registered('subscription_multi_product_array')) {
    tep_redirect(tep_href_link(FILENAME_SHOPPING_CART));
  }
}

// needs to be included earlier to set the success message in the messageStack
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CREATE_ACCOUNT);

$process = false;
if (isset($_POST['action']) && ($_POST['action'] == 'process') && isset($_POST['formid']) && ($_POST['formid'] == $sessiontoken)) {
  $process = true;

  if (ACCOUNT_GENDER == 'true') {
    if (isset($_POST['gender'])) {
      $gender = tep_db_prepare_input($_POST['gender']);
    } else {
      $gender = false;
    }
  }
  $firstname = tep_db_prepare_input($_POST['firstname']);
  $lastname = tep_db_prepare_input($_POST['lastname']);
  if (ACCOUNT_DOB == 'true') $dob = tep_db_prepare_input($_POST['dob']);
  $email_address = tep_db_prepare_input($_POST['email_address']);
  if (ACCOUNT_COMPANY == 'true') $company = tep_db_prepare_input($_POST['company']);
  $street_address = tep_db_prepare_input($_POST['street_address']);
  if (ACCOUNT_SUBURB == 'true') $suburb = tep_db_prepare_input($_POST['suburb']);

  $postcode = tep_db_prepare_input($_POST['postcode']);
  $postcode = preg_replace("/[^A-Za-z0-9 ]/", '', $postcode); // strip all non alphanumeric

  $city = tep_db_prepare_input($_POST['city']);
  if (ACCOUNT_STATE == 'true') {
    $state = tep_db_prepare_input($_POST['state']);
    if (isset($_POST['zone_id'])) {
      $zone_id = tep_db_prepare_input($_POST['zone_id']);
    } else {
      $zone_id = false;
    }
  }
  $country = tep_db_prepare_input($_POST['country']);
  $telephone = tep_db_prepare_input($_POST['telephone']);
  $telephone = str_replace(' ', '', $telephone); // remove spaces
  $fax = tep_db_prepare_input($_POST['fax']);

  if (isset($_POST['newsletter'])) {
    $newsletter = tep_db_prepare_input($_POST['newsletter']);
  } else {
    $newsletter = false;
  }

  if (isset($_POST['newsletter_whisky'])) {
    $newsletter_whisky = tep_db_prepare_input($_POST['newsletter_whisky']);
  } else {
    $newsletter_whisky = false;
  }

  if (isset($_POST['newsletter_pipe_tobacco'])) {
    $newsletter_pipe_tobacco = tep_db_prepare_input($_POST['newsletter_pipe_tobacco']);
  } else {
    $newsletter_pipe_tobacco = false;
  }

  $password = tep_db_prepare_input($_POST['password']);
  $confirmation = tep_db_prepare_input($_POST['confirmation']);

  $error = false;

  if (ACCOUNT_GENDER == 'true') {
    if (($gender != 'm') && ($gender != 'f')) {
      $error = true;

      $messageStack->add('create_account', ENTRY_GENDER_ERROR);
    }
  }

  if (strlen($firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
    $error = true;

    $messageStack->add('create_account', ENTRY_FIRST_NAME_ERROR);
  }

  if (strlen($lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
    $error = true;

    $messageStack->add('create_account', ENTRY_LAST_NAME_ERROR);
  }

  if (ACCOUNT_DOB == 'true') {
    if ((strlen($dob) < ENTRY_DOB_MIN_LENGTH) || (!empty($dob) && (!is_numeric(tep_date_raw($dob)) || !@checkdate(substr(tep_date_raw($dob), 4, 2), substr(tep_date_raw($dob), 6, 2), substr(tep_date_raw($dob), 0, 4))))) {
      $error = true;
      $messageStack->add('create_account', ENTRY_DATE_OF_BIRTH_ERROR);
    }
  }

  if (strlen($email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
    $error = true;

    $messageStack->add('create_account', ENTRY_EMAIL_ADDRESS_ERROR);
  } elseif (tep_validate_email($email_address) == false) {
    $error = true;

    $messageStack->add('create_account', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
  } else {
    // PWA BOF 2b
    $check_email_query = tep_db_query("select count(*) as total from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($email_address) . "' and guest_account != '1'");
    // PWA EOF 2b
    $check_email = tep_db_fetch_array($check_email_query);
    if ($check_email['total'] > 0) {
      $error = true;

      $messageStack->add('create_account', ENTRY_EMAIL_ADDRESS_ERROR_EXISTS);
    }
  }

  if (isset($_POST['contact_me_by_letter'])) {
    $error = true;
    $messageStack->add('create_account', 'Our spam protection believes you are a robot! Please try a different browser or contact us for help.');
  }

  if (strlen($street_address) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
    $error = true;

    $messageStack->add('create_account', ENTRY_STREET_ADDRESS_ERROR);
  }

  if (strlen($postcode) < ENTRY_POSTCODE_MIN_LENGTH) {
    $error = true;

    $messageStack->add('create_account', ENTRY_POST_CODE_ERROR);
  }

  if (strlen($postcode) > 10) {
    $error = true;

    $messageStack->add('create_account', 'Your Post Code must contain a maximum of 10 characters.');
  }

  if (strlen($city) < ENTRY_CITY_MIN_LENGTH) {
    $error = true;

    $messageStack->add('create_account', ENTRY_CITY_ERROR);
  }

  if (is_numeric($country) == false) {
    $error = true;

    $messageStack->add('create_account', ENTRY_COUNTRY_ERROR);
  }

  if (ACCOUNT_STATE == 'true') {
    $zone_id = 0;
    $check_query = tep_db_query("select count(*) as total from " . TABLE_ZONES . " where zone_country_id = '" . (int)$country . "'");
    $check = tep_db_fetch_array($check_query);
    $entry_state_has_zones = ($check['total'] > 0);
    if ($entry_state_has_zones == true) {
      $zone_query = tep_db_query("select distinct zone_id from " . TABLE_ZONES . " where zone_country_id = '" . (int)$country . "' and (zone_name = '" . tep_db_input($state) . "' or zone_code = '" . tep_db_input($state) . "')");
      if (tep_db_num_rows($zone_query) == 1) {
        $zone = tep_db_fetch_array($zone_query);
        $zone_id = $zone['zone_id'];
      } else {
        $error = true;

        $messageStack->add('create_account', ENTRY_STATE_ERROR_SELECT);
      }
    } else {
      if (strlen($state) < ENTRY_STATE_MIN_LENGTH) {
        $error = true;

        $messageStack->add('create_account', ENTRY_STATE_ERROR);
      }
    }
  }

  if (strlen($telephone) < ENTRY_TELEPHONE_MIN_LENGTH) {
    $error = true;

    $messageStack->add('create_account', ENTRY_TELEPHONE_NUMBER_ERROR);
  }

  // PWA BOF
  if (!isset($_GET['guest']) && !isset($_POST['guest'])) {
    // PWA EOF

    if (strlen($password) < ENTRY_PASSWORD_MIN_LENGTH) {
      $error = true;

      $messageStack->add('create_account', ENTRY_PASSWORD_ERROR);
    } elseif ($password != $confirmation) {
      $error = true;

      $messageStack->add('create_account', ENTRY_PASSWORD_ERROR_NOT_MATCHING);
    }
    // PWA BOF
  }
  // PWA EOF

  if ($error == false) {

    // recaptcha start
    $response = $_POST["g-recaptcha-response"];
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = array(
      'secret' => '6Lens3soAAAAAAsT77FC7Vv8P_ej65n-lORHnJ3Z',
      'response' => $_POST["g-recaptcha-response"]
    );
    $options = array(
      'http' => array(
        'method' => 'POST',
        'content' => http_build_query($data)
      )
    );
    $context  = stream_context_create($options);
    $verify = file_get_contents($url, false, $context);
    $captcha_success = json_decode($verify);

    if($country == '44'){
      $captcha_success->success = true;
    }

    if ($captcha_success->success == false) {

      $error = true;
      $messageStack->add('create_account', 'Please tick the I am not a robot box below');
      
    } else if ($captcha_success->success == true) {

    // PWA BOF 2b
    if (!isset($_GET['guest']) && !isset($_POST['guest'])) {
      $dbPass = tep_encrypt_password($password);
      $guestaccount = '0';
    } else {
      $dbPass = 'null';
      $guestaccount = '1';
    }


    // check for loyalty point promotion
    $check_loyalty_query = tep_db_query("select count(*) as total, customers_newsletter, customers_newsletter_whisky, customers_newsletter_pipe_tobacco from shop_customers where customers_email_address = '" . tep_db_input($email_address) . "'");
    $check_loyalty = tep_db_fetch_array($check_loyalty_query);
    if ($check_loyalty['total'] > 0) {
      $add_loyalty_points = true;
      $add_loyalty_points_amount = 200;
      $add_loyalty_points_comment = 'Shop Customer Newsletter Signup.';
      if ($check_loyalty['customers_newsletter'] == '1') {
        $newsletter = '1';
      }
      if ($check_loyalty['customers_newsletter_whisky'] == '1') {
        $newsletter_whisky = '1';
      }
      if ($check_loyalty['customers_newsletter_pipe_tobacco'] == '1') {
        $newsletter_pipe_tobacco = '1';
      }
    }

    if($newsletter != '1' && $newsletter_whisky != '1' && $newsletter_pipe_tobacco != '1'){
      // check if they have signed up via newsletter_promotions table
      $news_promo_query = tep_db_query("select count(*) as total from newsletter_promotions where np_email_address = '" . tep_db_input($email_address) . "' and np_active = '1' ");
      $news_promo = tep_db_fetch_array($news_promo_query);
      if ($news_promo['total'] > 0) {
        $newsletter = '1';
      }
    }

    // PWA EOF 2b
    $sql_data_array = array(
      'customers_firstname' => $firstname,
      'customers_lastname' => $lastname,
      'customers_email_address' => $email_address,
      'customers_telephone' => $telephone,
      'customers_fax' => $fax,
      'customers_newsletter' => $newsletter,
      'customers_newsletter_whisky' => $newsletter_whisky,
      'customers_newsletter_pipe_tobacco' => $newsletter_pipe_tobacco,
      // PWA BOF 2b
      'customers_password' => $dbPass,
      'guest_account' => $guestaccount
    );
    // PWA EOF 2b

    // Mail Chimp Sign Up Start
    if (MAILCHIMP_ENABLED == 'true') {

      $merge_values = array('FNAME'=> $firstname, 'LNAME'=> $lastname);
      $post_params = array('email_address' => $email_address, 'status' => 'subscribed', 'email_type' => 'html', 'merge_fields' => $merge_values);
	    
                   if ($newsletter == '1') { $mailchimp->lists(MAILCHIMP_LIST_ID)->members()->POST($post_params); } // C.Gars
            if ($newsletter_whisky == '1') { $mailchimp->lists('c8703a292a')->members()->POST($post_params); } // Turmeaus Whisky Mailing List
      if ($newsletter_pipe_tobacco == '1') { $mailchimp->lists('47659e5ec6')->members()->POST($post_params); } // CG Pipes

      if (isset($_POST['activate_cgars_plus']) && $_POST['activate_cgars_plus'] == '1') {
        $mailchimp->lists('b99400d7f0')->members()->POST($post_params); // CGars Plus
      }

    }

    if (ACCOUNT_GENDER == 'true') $sql_data_array['customers_gender'] = $gender;
    if (ACCOUNT_DOB == 'true') $sql_data_array['customers_dob'] = tep_date_raw($dob);

    tep_db_perform(TABLE_CUSTOMERS, $sql_data_array);

    $customer_id = tep_db_insert_id();

    $sql_data_array = array(
      'customers_id' => $customer_id,
      'entry_firstname' => $firstname,
      'entry_lastname' => $lastname,
      'entry_street_address' => $street_address,
      'entry_postcode' => $postcode,
      'entry_city' => $city,
      'entry_country_id' => $country
    );

    if (ACCOUNT_GENDER == 'true') $sql_data_array['entry_gender'] = $gender;
    if (ACCOUNT_COMPANY == 'true') $sql_data_array['entry_company'] = $company;
    if (ACCOUNT_SUBURB == 'true') $sql_data_array['entry_suburb'] = $suburb;
    if (ACCOUNT_STATE == 'true') {
      if ($zone_id > 0) {
        $sql_data_array['entry_zone_id'] = $zone_id;
        $sql_data_array['entry_state'] = '';
      } else {
        $sql_data_array['entry_zone_id'] = '0';
        $sql_data_array['entry_state'] = $state;
      }
    }

    // PWA BOF
    if (isset($_GET['guest']) or isset($_POST['guest']))
      tep_session_register('customer_is_guest');
    // PWA EOF

    tep_db_perform(TABLE_ADDRESS_BOOK, $sql_data_array);

    $address_id = tep_db_insert_id();

    tep_db_query("update " . TABLE_CUSTOMERS . " set customers_default_address_id = '" . (int)$address_id . "' where customers_id = '" . (int)$customer_id . "'");

    if (isset($_POST['activate_cgars_plus']) && $_POST['activate_cgars_plus'] == '1') {
      tep_cgars_plus_member_status((int)$customer_id, 'enable'); // adds cgars plus table entry
      tep_cp_send_welcome_email($email_address, $firstname . ' ' . $lastname);
      // tep_cp_add_bonus_points((int)$customer_id, NEW_SIGNUP_CGARS_PLUS_POINT_AMOUNT, 'Customer sign up bonus'); // changed to CGARSPLUS5 discount code
    }

    // social login start
    tep_db_query("insert into " . TABLE_CUSTOMERS_INFO . " (customers_info_id, customers_info_number_of_logons, customers_info_date_account_created, valid_address, personal_details_valid) values ('" . (int)$customer_id . "', '0', now(),1,1)");
    // social login end

    if (SESSION_RECREATE == 'True') {
      tep_session_recreate();
    }

    $customer_first_name = $firstname;
    $customer_default_address_id = $address_id;
    $customer_country_id = $country;
    $customer_zone_id = $zone_id;
    tep_session_register('customer_id');
    tep_session_register('customer_first_name');
    tep_session_register('customer_default_address_id');
    tep_session_register('customer_country_id');
    tep_session_register('customer_zone_id');

    // PWA BOF
    if (isset($_GET['guest']) or isset($_POST['guest'])) tep_redirect(tep_href_link(FILENAME_CHECKOUT_SHIPPING));
    // PWA EOF

    // reset session token
    $sessiontoken = md5(tep_rand() . tep_rand() . tep_rand() . tep_rand());

    // restore cart contents
    $cart->restore_contents();

    // restore wishlist to sesssion
    $wishList->restore_wishlist();

    // add bonus loyalty points
    if ($add_loyalty_points) {
     if ($loyalty_settings['global_lp_legacy_earn']) {

      tep_db_query("update " . TABLE_CUSTOMERS . " set customers_shopping_points = '" . $add_loyalty_points_amount . "' where customers_id = " . (int)$customer_id);

      $sql_data_array = array('customer_id' => (int)$customer_id,
      'orders_id' => 0,
      'points_comment' => tep_db_input($add_loyalty_points_comment),
      'points_pending' => $add_loyalty_points_amount,
      'date_added' => 'now()',
      'points_status' => 2);

      tep_db_perform(TABLE_CUSTOMERS_POINTS_PENDING, $sql_data_array);

     }
    }

    // build the message content
    $name = $firstname . ' ' . $lastname;

    $email_generic_heading = 'Welcome to C.Gars!';
    $email_generic_sub_heading = 'You have finished setting up your new account.';

    $email_generic_body_text = '<h4 style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 20px;margin-bottom: 8px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Dear ' . $name . ',</h4><p class="mbe" style="font-family: Helvetica, Arial, sans-serif;font-size: 16px;line-height: 23px;color: #616161;mso-line-height-rule: exactly;display: block;margin-top: 0;margin-bottom: 0;">Thank you for registering online with C.Gars. Just enter your email address and password to <u><a style="color: #9E8B7B;" href="' . tep_href_link(FILENAME_LOGIN, '', 'SSL', false) . '">login</a></u> and if you ever forget your password you can reset that <u><a style="color: #9E8B7B;" href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL', false) . '">here</a></u>. If you would like to view or change any of your details, simply login to <u><a style="color: #9E8B7B;" href="' . tep_href_link(FILENAME_ACCOUNT, '', 'SSL', false) . '">my account</a></u>.';
    if ($add_loyalty_points) {
      if ($loyalty_settings['account_cg_plus_earn']) { // (use account not global)
       $email_generic_body_text .= '<br/><br/><strong>200 CGars Plus points have been added to your account for signing up in store!</strong>';
      } elseif ($loyalty_settings['global_lp_legacy_earn']) {  
       $email_generic_body_text .= '<br/><br/><strong>200 loyalty points have been added to your account for signing up in store!</strong>'; 
      }
    }
    $email_generic_body_text .= '<br/><br/>Please note for new customers, your address must be registered with your credit card, otherwise your credit card may be declined for security reasons.<br/><br/>For help with any of our online services, please email us at: <a style="color: #9E8B7B;" href="mailto:<EMAIL>"><EMAIL></a><br/><br/>We look forward to seeing you soon.<br/><br/></p>';

    // load email templates
    include(DIR_WS_LANGUAGES . 'english/email_generic_template.php');
    include(DIR_WS_LANGUAGES . 'english/email_account_template.php');

    $email_text  = EMAIL_GENERIC_TEMPLATE_START;
    $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;
    $email_text .= EMAIL_GENERIC_TEMPLATE_BODY;
    $email_text .= EMAIL_ACCOUNT_TEMPLATE_SIGNATURE_IMAGE;
    $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
    $email_text .= EMAIL_GENERIC_TEMPLATE_END;

    tep_mail($name, $email_address, EMAIL_SUBJECT, $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);


    // rp temporary logging 15/07/2025

    $log_data = [
        'date' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'customer_id' => (int)$customer_id,
        'company' => $_POST['company'] ?? '',
        'street_address' => $_POST['street_address'] ?? '',
        'suburb' => $_POST['suburb'] ?? '',
        'city' => $_POST['city'] ?? '',
    ];

    $log_line = json_encode($log_data) . PHP_EOL;

    file_put_contents('/home/<USER>/public_html/tep/create_account.log', $log_line, FILE_APPEND);

    // temporary logging

    tep_redirect(tep_href_link(FILENAME_CREATE_ACCOUNT_SUCCESS, '', 'SSL'));

    }
  }
}

// PWA BOF
if (!isset($_GET['guest']) && !isset($_POST['guest'])) {
  $breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_CREATE_ACCOUNT, '', 'SSL'));
} else {
  $breadcrumb->add(NAVBAR_TITLE_PWA, tep_href_link(FILENAME_CREATE_ACCOUNT, 'guest=guest', 'SSL'));
}
// PWA EOF

$page_title_text = 'Create an account';

require(DIR_WS_INCLUDES . 'template_top.php');
require('includes/form_check.js.php');

$show_recaptcha = true;

?>
<div id="inner-wrapper">
  <div class="pure-g">

    <?php
    if ($messageStack->size('create_account') > 0) {
      echo '<div class="pure-u-24-24"><div class="box-padding-half">' . $messageStack->output('create_account') . '</div></div>';
    }
    ?>
    <?php echo tep_draw_form('create_account', tep_href_link(FILENAME_CREATE_ACCOUNT, (isset($_GET['guest']) ? 'guest=guest' : ''), 'SSL'), 'post', 'onSubmit="return check_form(create_account);" class="pure-form" autocomplete="none"', true) . tep_draw_hidden_field('action', 'process', true); ?>

    <?php if ($id_check == 2) { ?>
      <div class="pure-u-24-24">
      <?php } else { ?>
        <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-16-24 pure-u-xl-16-24">
        <?php } ?>
        <div class="box-padding-both">
          <?php if (!isset($_GET['guest']) && !isset($_POST['guest'])) { ?>
            <h1><?php echo HEADING_TITLE; ?></h1>
          <?php } else { ?>
            <h1><?php echo HEADING_TITLE_PWA; ?></h1>
          <?php } ?>
        </div>
        </div>

        <?php if ($id_check == 2) { ?>
          <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-16-24 pure-u-xl-16-24">
            <div class="box-padding-both">
              <div class="id-notice"><i class="material-icons"><a href="#" title="Customers purchasing cigarettes, hand rolling tobacco, vape units or liquid will be required to upload or email a copy of their I.D. A scan or photo of your drivers licence or passport is preferred. Your ID information will be deleted as soon as its has been approved. For your privacy, we do not keep this information on record or share with any third parties." class="masterTooltip">&#xe88f;</a></i>
                <span>Buying cigarettes, hand rolling tobacco, vape units or liquid?&nbsp;&nbsp;I.D. will be required for age verification.</span>
              </div>
            </div>
          </div>
        <?php } ?>

        <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-8-24 pure-u-xl-8-24">
          <div class="box-padding-both">
            <div class="login-notice">
              <a href="<?php echo tep_href_link(FILENAME_LOGIN, tep_get_all_get_params(), 'SSL'); ?>">Existing customers login here</a>
            </div>
          </div>
        </div>

        <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-8-24 pure-u-xl-8-24">
          <div class="box-padding-both">

            <h2><?php echo CATEGORY_PERSONAL; ?></h2>
            <div class="content-wrapper">
              <label for="firstname">First Name <span>*</span></label>
              <?php echo tep_draw_input_field('firstname', '', 'type="text" class="pure-input-1" autocomplete="none"'); ?>
              <label for="lastname">Last Name <span>*</span></label>
              <?php echo tep_draw_input_field('lastname', '', 'type="text" class="pure-input-1" autocomplete="none"'); ?>
              <label for="email_address">Email Address <span>*</span></label>
              <?php echo tep_draw_input_field('email_address', '', 'type="email" class="pure-input-1" autocomplete="none"'); ?>
              <label for="telephone">Telephone <span>*</span></label>
              <?php echo tep_draw_input_field('telephone', '', 'type="number" class="pure-input-1" autocomplete="none"'); ?>
            </div>
            <div class="content-wrapper" style="margin-top: 25px;">
              <label for="company">Company</label>
              <?php echo tep_draw_input_field('company', '', 'type="text" class="pure-input-1" autocomplete="none"'); ?>
            </div>

          </div>
        </div>

        <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-8-24 pure-u-xl-8-24">
          <div class="box-padding-both">
            <h2><?php echo CATEGORY_ADDRESS; ?></h2>

            <div class="login-notice" style="margin-bottom: 25px;">
              <label for="address_finder">UK Address Finder</label>
              <?php echo tep_draw_input_field('address_finder', '', 'type="text" class="pure-input-1" placeholder="Start typing your address or post code..." autocomplete="none"'); ?>
            </div>

            <div class="content-wrapper">

              <label for="street_address">Street Address <span>*</span></label>
              <?php echo tep_draw_input_field('street_address', '', 'type="text" class="pure-input-1" autocomplete="none"'); ?>
              <?php echo tep_draw_input_field('suburb', '', 'type="text" class="pure-input-1" autocomplete="none"'); ?>
              <label for="city">City <span>*</span></label>
              <?php echo tep_draw_input_field('city', '', 'type="text" class="pure-input-1" autocomplete="none"'); ?>
              <label for="state">County / State <span>*</span></label>
              <?php
              if ($process == true) {
                if ($entry_state_has_zones == true) {
                  $zones_array = array();
                  $zones_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int)$country . "' order by zone_name");
                  while ($zones_values = tep_db_fetch_array($zones_query)) {
                    $zones_array[] = array('id' => $zones_values['zone_name'], 'text' => $zones_values['zone_name']);
                  }
                  echo tep_draw_pull_down_menu('state', $zones_array, $state, 'class="pure-input-1"');
                } else {
                  echo tep_draw_input_field('state', '', 'type="text" class="pure-input-1" autocomplete="none"');
                }
              } else {
                echo tep_draw_input_field('state', '', 'type="text" class="pure-input-1" autocomplete="none"');
              }
              ?>
              <label for="postcode">Post Code <span>*</span></label>
              <?php echo tep_draw_input_field('postcode', '', 'type="text" class="pure-input-1" autocomplete="none"'); ?>
              <label for="country">Country <span>*</span></label>
              <?php echo tep_get_country_list('country', '', 'class="pure-input-1" autocomplete="none"'); ?>

            </div>

          </div>
        </div>

        <div class="pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-8-24 pure-u-xl-8-24">
          <div class="box-padding-both">
            <?php if (!isset($_GET['guest']) && !isset($_POST['guest'])) { ?>
              <h2><?php echo CATEGORY_PASSWORD; ?></h2>
              <div class="content-wrapper">

                <label for="password">Password <span>*</span></label>
                <?php echo tep_draw_password_field('password', '', 'type="password" class="pure-input-1"'); ?>
                <label for="confirmation">Confirm Password <span>*</span></label>
                <?php echo tep_draw_password_field('confirmation', '', 'type="password" class="pure-input-1"'); ?>

                We recommend using a password with 8 characters or more. Preferably with a combination of letters, numbers and symbols.
              </div>
            <?php
            } else { // Ingo PWA Ende
              echo tep_draw_hidden_field('guest', 'guest');
            }
?>
<?php  
   if($loyalty_settings['global_cg_plus_earn']){ 
    if (!isset($_GET['guest']) && !isset($_POST['guest'])) {

    $loyalty_points_jquery_code = "

$('.pure-button.activate-cgars-plus-button').on('click', function(e) {
    e.preventDefault(); // Prevent the default button action
    var checkbox = $('input[name=\"activate_cgars_plus\"]');
    checkbox.prop('checked', !checkbox.prop('checked'));
});

";
    
    // Temporary switch off ?>
<style>
.cps-wrapper { border: 1px solid #EEE; padding: 10px 15px 20px 15px; border-radius: 5px; }
.cps-image, .cps-checkbox, .cps-checkbutton { vertical-align: middle; display: inline-block; height: 62px; line-height: 62px; padding-right: 12px; }
.cps-checkbox {padding: 6px 10px 0 0; }  .cps-checkbox input[type=checkbox] { transform: scale(1.5); }
.activate-cgars-plus-button { background-color: #009999; color: #FFF; }
</style>
<div class="content-wrapper">
  <div class="cps-wrapper">
  <?php
        echo '<div class="cps-image"><img src="design/generic/images/CGars_Plus.png"></div>';
        echo '<div class="cps-checkbox">' . tep_draw_checkbox_field('activate_cgars_plus', '1') . '</div> <div class="cps-checkbutton"><button class="pure-button activate-cgars-plus-button">Sign me up!</button></div>';
        echo '<div>Earn reward points to spend on tobacco products and more!</strong></div>';
   ?> 
   </div>    
</div>
<?php } } ?>
            <h2>Send me...</h2>
            <div class="content-wrapper">
              <label for="newsletter" class="pure-checkbox"><?php echo tep_draw_checkbox_field('newsletter', '1', true); ?><strong>Cigar</strong> Newsletter and Promotional emails</label>
              <label for="newsletter_whisky" class="pure-checkbox"><?php echo tep_draw_checkbox_field('newsletter_whisky', '1', true); ?><strong>Whisky</strong> Newsletter and Promotional emails</label>
              <label for="newsletter_pipe_tobacco" class="pure-checkbox"><?php echo tep_draw_checkbox_field('newsletter_pipe_tobacco', '1', true); ?><strong>Pipe Tobacco</strong> Newsletter and Promotional emails</label>
            </div>
            <div class="login-notice" style="margin-bottom: 20px;">
              <?php if (!isset($_GET['guest']) && !isset($_POST['guest'])) { ?>By creating an account,<?php } else { ?>By continuing,<?php } ?> you agree to the <a href="cigar-library/terms.htm">Terms and Conditions</a> and <a href="privacy_policy.php">Privacy Policy</a> of C.Gars.
            </div>

            <input type="checkbox" name="contact_me_by_letter" value="1" style="display:none !important" tabindex="-1" autocomplete="off">
            <div style="margin-bottom: 20px; margin-right: 20px; float: left;" class="g-recaptcha" data-sitekey="6Lens3soAAAAAMOzYvl2TEf4vmVErj5XKQb1iEC2"></div>
            <p style="clear: both;"><?php echo tep_draw_button(IMAGE_BUTTON_CONTINUE, 'person', null, 'primary'); ?></p>

          </div>
        </div>

        </form>

      </div>

      <div class="pure-u-24-24">
        <div class="button-bottom-left">&nbsp;&nbsp;</div>
      </div>

  </div>
  <?php
  require(DIR_WS_INCLUDES . 'template_bottom.php');
  require(DIR_WS_INCLUDES . 'application_bottom.php');
  ?>