<?php
register_hook('instant_mpn_checker', function () {
?>
    <td class="main">Products MPN / Barcode:</td>
    <td class="main">
        <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;' . tep_draw_input_field('products_mpn', $pInfo->products_mpn, 'id="products_mpn_input"'); ?>
        <span id="mpn-ajax-warning" style="color: red; margin-left: 10px;"></span>
    </td>
    </tr>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var mpnInput = document.getElementById('products_mpn_input');
            var warningSpan = document.getElementById('mpn-ajax-warning');
            var productsId = <?php echo isset($pInfo->products_id) ? (int)$pInfo->products_id : 0; ?>;
            if (mpnInput) {
                mpnInput.addEventListener('input', function() {
                    var mpn = mpnInput.value.trim();
                    if (mpn === '') {
                        warningSpan.textContent = '';
                        return;
                    }
                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', 'ajax/ajax_check_mpn.php', true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4 && xhr.status === 200) {
                            try {
                                var resp = JSON.parse(xhr.responseText);
                                if (resp.exists) {
                                    warningSpan.textContent = 'MPN already exists: ' + resp.name + ' (Model: ' + resp.model + ')';
                                } else {
                                    warningSpan.textContent = '';
                                }
                            } catch (e) {
                                warningSpan.textContent = '';
                            }
                        }
                    };
                    xhr.send('mpn=' + encodeURIComponent(mpn) + '&products_id=' + productsId);
                });
            }
        });
    </script>
<?php
});
