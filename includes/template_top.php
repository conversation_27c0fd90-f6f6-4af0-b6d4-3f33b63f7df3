<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" <?php echo HTML_PARAMS; ?>>
<head>
  <?php
  if (HTTPS_SERVER == 'https://www.cgarsltd.co.uk') {
  include('design/content/js/ga.php');
  } 
  ?>

  <?php include(DIR_WS_INCLUDES . 'header_tags.php'); ?>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <base href="<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_CATALOG; ?>" />
  <link rel="canonical" href="<?php echo htmlspecialchars(tep_seo_current_url()); ?>" />
  <link rel="icon" type="image/png" href="<?php echo HTTP_SERVER; ?>/favicon2.png">
<?php
  require('design/generic/design_functions.php');
  if (isset($_GET['cv']) && is_numeric($_GET['cv'])) { 
    $cv = (int)$_GET['cv'];
  } else {
    $cv = 129;
  }
  $defer_css_list = '';
  $current_customer_id = (int)$customer_id;
  $hide_delivery_countdown = true;
  // $show_christmas = true;
  ?>
  <?php 
  $css_files = [
    'design/css/ext/fallovers/grids-responsive-old-ie.min.css',
    'design/css2021/ext/pure.min.css',
    'design/css2021/all/common-layout.css',
    'design/css2021/all/header-layout.css',
    'design/css2021/all/listing-layout.css',
    'design/css2021/all/footer-layout.min.css',
    'design/css2021/all/advsearch-layout.css',
    'design/css2021/ext/roboto_font.min.css',
    'design/css/ext/grids-responsive.min.css',
    'design/css/ext/fallovers/ie8.min.css',
    'design/css/ext/fallovers/ie.min.css',
];

if($design != '2025'){
    $css_files[] = 'design/css2021/ext/material_icons.min.css';
}

    if ($our_shops_page_url) {
    $css_files[] = 'design/css2021/pages/our_shops.php.min.css';
    }

  if ($mobile_view) {
      $css_files[] = 'design/css2021/ext/mgmenu_mobile.min.css';
      
      if ($main_page_url) {
      $css_files[] = 'design/css2021/pages/index.php.css';
      }
 
     } else { 
  //styles 2021
 $css_files[] = 'design/css2021/ext/mgmenu.min.css';
 $css_files[] = 'design/css2021/pages/index.php.css';

} ?>

  <?php if ($show_owl_carousel_two) { ?>
  <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.0.0-beta.3/assets/owl.carousel.min.css'>
  <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.0.0-beta.3/assets/owl.theme.default.min.css'>
  <?php } ?>
 
    <?php
    if ($show_owl_carousel && !$show_owl_carousel_two) {
      $css_files[] = 'design/css2021/ext/owl.carousel.min.css';
      $css_files[] = 'design/css2021/ext/owl.theme.min.css';
    }
    if ($modal_needed) {
      $css_files[] = 'design/content/modal/css/landing-modal.min.css';
    } elseif ($news_modal_needed) {
      $css_files[] = 'design/content/modal/css/news-modal.min.css';
    }
    if ($show_colorbox) {
      $css_files[] = 'ext/jquery/colorbox/colorbox.min.css';
    }

    if ($show_nouislider) {
      $css_files[] =  DC_STYLES . 'nouislider.min.css';
    }
    if ($show_select2) {
      $css_files[] = DC_STYLES . 'select2.min.css';
    }
    if ($show_featured) {
      $css_files[] = DC_STYLES . 'featured/featured-layout.css';
    }
    if ($show_prod_info) {
      $css_files[] = 'design/css2021/pages/prod_info.php.css';
    }
    if ($show_tool_tip) {
      $css_files[] = DC_STYLES . 'tip-yellowsimple.css';
    }
    if ($show_html_extras) {
      $css_files[] = 'design/css2021/all/html.pages.min.css';
    }
    if ($show_cigar_library_old) {
      $css_files[] = DC_STYLES . 'cigar_library_menu.css';
    }
    if ($show_countdown) {
      $css_files[] = 'design/css2021/ext/countdown.min.css';
    }
    if ($main_page_url || $show_delivery_countdown) {
      $css_files[] = 'design/css2021/ext/countdown2.min.css';
    }
    if ($show_customer_service) {
      $css_files[] = DC_STYLES . 'customer_service_menu.css';
    }
    if ($show_cigar_library_menu) {
      if ($mobile_view) {
        $css_files[] = 'ext/jquery/mm/css/clmenu_mobile.css';
      } else {
        $css_files[] = 'ext/jquery/mm/css/clmenu.css';
      }
    }
    if ($show_fotorama) {
      $css_files[] = DC_STYLES . 'fotorama.css';
    }
    if ($show_christmas) {
      $css_files[] = 'design/css2021/themes/christmas/c-layout-2023.css';
    }
    if ($show_davidoff) {
      $css_files[] = 'html/portals/davidoff/css/icon-fonts.css';
      $css_files[] = 'html/portals/davidoff/css/fancybox.css';
      $css_files[] = 'html/portals/davidoff/css/main.css';
    }
    if ($show_subscription_scripts || $show_cg_plus_scripts) {
      $css_files[] = DC_STYLES . 'datepicker.css';
    }
    ?>

<style>
<?php if ($main_page_url) { ?>
  .aspect-ratio-box #owl-home {
    align-items: flex-start !important;
  }
<?php if ($mobile_view) { ?>
.aspect-ratio-box #owl-home > *:nth-child(n+2) {
  display: none; /* Hide all items from the 2nd onward */
}
<?php } else { ?>
.aspect-ratio-box #owl-home > *:nth-child(n+4) {
  display: none; /* Hide all items from the 4th onward */
}
<?php } ?>
<?php } ?>
  </style>
  <?php // optional external css ?>
  <?php if ($show_fancybox) { ?>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.3.5/jquery.fancybox.min.css" />
  <?php } ?>
  <?php if ($show_pca) { ?>
    <link rel="stylesheet" type="text/css" href="https://services.postcodeanywhere.co.uk/css/captureplus-2.10.min.css?key=hb28-uj26-bf88-xe97" />
  <?php } ?>
  <?php if ($show_flick_ui) { ?>
    <link rel="stylesheet" type="text/css" href="https://code.jquery.com/ui/1.10.4/themes/flick/jquery-ui.css">
  <?php } ?>
  <?php if($prod_info_layout != '2'){
   if ((isset($_GET['halloween'])) && ($_GET['halloween'] == '1')) {  ?>
    <link rel="stylesheet" type="text/css" href="<?php echo DC_STYLES ?>halloween/h-layout.css?version=4" />
  <?php }
  } 
 ?>
<?php if ($modal_needed) { ?>
  <script>
document.addEventListener('DOMContentLoaded',function(){const modal=document.getElementById('popup');const overlay=document.querySelector('.overlay');const closeModalButton=document.getElementById('close-modal');function showModal(){modal.classList.add('show');overlay.classList.add('show')}
function closeModal(){modal.classList.remove('show');overlay.classList.remove('show')}
showModal();closeModalButton.addEventListener('click',function(event){event.preventDefault();closeModal()});overlay.addEventListener('click',closeModal)})
</script>
<?php } ?>
<?php if ($main_page_url) { echo tep_display_banner_images(22, 0, $mobile_view, true); } ?>
<?php $css_query = urlencode(implode(',', $css_files)); 
$debug = isset($_GET['debug']) ? '&debug=1' : '';
if(isset($_GET['design'])){
  if (!tep_session_is_registered('design')) {
    tep_session_register('design');
  }
  $design = $_GET['design'];
}
if($design == '2025'){ $design_param = '&design=2025'; } else { $design_param = ''; }
if($design == '2025'){ ?>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
<?php } ?>
<link rel="stylesheet" type="text/css" href="minify.php?type=css&files=<?php echo $css_query . $debug . $design_param; ?>">
<?php run_hook('head');?>
</head>
<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T6XKZFG4"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->  
  <?php if ($header_message) {
    echo '<div class="header-message" style="background-color: #009966; padding: 10px; color: #FFF; font-weight: 700; text-align: center;">' . $header_message . '</div>';
  } ?>
  <?php if (!$mobile_view && $remove_this_to_turn_on_snow) { ?>
    <div id="snowflakeContainer">
      <p class="snowflake">*</p>
    </div>
  <?php } ?>

<?php if ($modal_needed) { ?>

<!-- Modal Structure -->
<div id="popup" class="modal" role="dialog" aria-labelledby="Modal title" aria-describedby="Modal description">
	<div class="modal-content">
		<div class="header">
			<img width="319" height="77" src="design/content/modal/images/store_logo_l.png" title="C.Gars Cuban Cigars" alt="C.Gars Cuban Cigars">
		</div>
		<div class="copy">
			<p>Cookies help us deliver the best experience on our website. By using our website, you agree to our use of cookies in accordance with the C.Gars 
            <strong><a href="privacy_policy.php">Privacy Policy</a></strong></p>
			<p><button class="close-button" id="close-modal">Over 18 - Click here to enter</button></p>
		</div>
	</div>
</div>
<!-- Overlay Element with Top and Bottom Images -->
<div class="overlay">
	<!-- Top section with images -->
	<div class="overlay-top">
        <p>This site contains tobacco images. Please leave now if you are under 18</p>
	</div>
	<!-- Bottom section with images -->
	<div class="overlay-footer">
        <a href="customer-service/japanese_welcome.html"><img width="136" height="50" src="design/content/modal/images/japanese_button_new.jpg" /></a>
        <a href="customer-service/cantonese_welcome.html"><img width="140" height="50" src="design/content/modal/images/cantonese_button_new.jpg" /></a>
        <a href="customer-service/mandarin_welcome.html"><img width="144" height="50" src="design/content/modal/images/mandarin_button_new.jpg" /></a>
	</div>
</div>
<?php } elseif ($news_modal_needed) { $show_recaptcha_v3 = true; ?>
<?php // Theres more to come in our January Sales... ?>
<div id="popup" class="modal" role="dialog" aria-labelledby="Modal title" aria-describedby="Modal description">
	<div class="modal-content">
  <div class="modal-banner-outer"><div class="modal-banner"><button class="close-button" id="close-modal">X</button><div class="modal-banner-inner">Get Latest News 
  and Never Miss an Offer...</div></div></div>
  <div id="news-landing">
  <div class="news-landing-inner">
          <h2>Unlock the best deals</h2>
          <p>Want to receive money off your order NOW?</p><p>Sign up for our newsletter and receive our latest offers and reveal your deal*</p>
          <strong></strong>
          <form name="news_promo" method="post" id="news_promo">
          <div class="news-landing-buttons">
              <input type="hidden" name="campaign_id" value="1">
              <input type="text" id="news-customers-name" name="np_customers_name" placeholder="First Name">
              <input type="text" id="news-email-address" name="np_email_address" placeholder="Email Address"> 
          </div>
          <div class="honeypot-field" style="display:none;">
           <input type="text" name="hp_email_address" value="" autocomplete="off" />
          </div>
          <div class="recaptcha-container">
          <input type="hidden" name="recaptcha_token" id="recaptcha_token">
          </div>
          <p>*Discounts on non tobacco items only.</p>
          <input id="news-email-submit" type="submit" value="Sign Up!">
          </form>
    </div>
    </div>

	</div>
</div>

<?php } ?>

  <?php if (!$popup_url && !$remove_header) { ?>
    <!-- begin bodyWrapper //-->
    <?php if (!$print_view) {
       if($design == '2025'){
        require(DIR_WS_INCLUDES . 'header_2025.php');
       } else {
        require(DIR_WS_INCLUDES . 'header.php');
       }  
      } ?>
    <?php

if($design != '2025'){

    $header_banner1 = '<a href="samplers-and-selection-packs-virtual-herf-samplers-c-317_1942_2365.html" title="C.Gars Tickets and Events"><i class="material-icons">event</i><span><strong>C.Gars Tickets and Events</strong><div class="sub-text">Join us at one of our upcoming events.</div></span></a>';
    $header_banner2 = '<a href="cigar-library/Cigar_sub.htm" title="C.Gars Cigar Subscription"><i class="material-icons">&#xe80e;</i><span><strong>NEW! C.Gars Cigar Subscription</strong><div class="sub-text">Receive 5 high quality cigars each month.</div></span></a>';
    // $header_banner3 = '<a href="customer-service/order.htm" title="We now accept PayPal"><i class="material-icons"><img src="design/generic/images/paypal-icon.png" /></i><span><strong>We now accept PayPal!</strong><div class="sub-text">Unavailable on Cuban &amp; tobacco products</div></span></a>';
    $header_banner3 = '<a href="customer-service/shipping.htm" title="Free UK Mainland Delivery"><i class="material-icons">&#xe558;</i><span><strong>Free UK Mainland Delivery</strong><div class="sub-text">Orders over &pound;50.00 (Exclusions apply)</div></span></a>';
    $header_banner4 = '<a href="price_match.php" title="Price Match Guarantee"><i class="material-icons">check_circle_outline</i><span><strong>Price Match Guarantee</strong><div class="sub-text">We\'ll price match any online Cigar merchant!</div></span></a>';

    $header_banners = array($header_banner1, $header_banner2, $header_banner3, $header_banner4);

    shuffle($header_banners);

    $header_banner_rocky = '<a href="rocky_patel_portal.php" title="Rocky Patel Cigars"><i class="lcdh-logo-small"><img src="design/generic/images/rp-logo-small.png" /></i><span><strong>Discover Rocky Patel Cigars</strong><div class="sub-text">Search for Perfection</div></span></a>'; 

    $header_banner_aladino = '<a href="aladino_portal.php" title="Aladino Cigars"><i class="lcdh-logo-small"><img src="design/generic/images/aladino-logo-small.png" /></i><span><strong>Discover Aladino Cigars</strong><div class="sub-text">Exclusive to C.Gars</div></span></a>';

    $header_banner_oliva = '<a href="oliva_portal.php" title="Oliva Cigars"><i class="lcdh-logo-small"><img src="design/generic/images/oliva-logo-small.png" /></i><span><strong>Discover Oliva Cigars</strong><div class="sub-text">The world of Oliva</div></span></a>';

    $header_banners_brands = array($header_banner_rocky, $header_banner_aladino, $header_banner_oliva);

    shuffle($header_banners_brands);

    ?>

    <?php if (!$mobile_view && !$print_view) { ?>

      <div class="nav-container">
        <div class="nav-container-inner off-white-bg">
          <div id="full-wrapper">
            <div id="mgmenu_davidoff" class="mgmenu_container_davidoff">
              <!-- Begin Mega Menu Container -->
              <ul class="mgmenu clearmenu pure-g">
                <!-- Begin Mega Menu -->
                <?php

                if ((USE_CACHE == 'true') && empty($SID)) {
                  echo tep_cache_mega_menu_davidoff();
                } else {
                  require(DC_BLOCKS . 'categories_davidoff.php');
                }

                //<li class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-6-24 pure-u-xl-6-24">
                //  <div class="sub-banner-pad sub-banner-1"><a href="LCDH_Portal.php" title="La Casa Del Habano Cigars"><i class="lcdh-logo-small"><img src="design/generic/images/lcdh-logo-small.png" /></i><span><strong>La Casa Del Habano Cigars</strong>
                //        <div class="sub-text">Special releases exclusive to LCDH</div>
                //      </span></a></div>
                //</li>

                ?>
                <li class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-6-24 pure-u-xl-6-24">
                  <div class="sub-banner-pad sub-banner-1"><a href="cigars-cuban-cigars-c-317_44.html" title="The Stamp of Quality for Havanas in the UK"><i class="lcdh-logo-small"><img src="design/generic/images/ems-logo-small.png" /></i><span><strong>English Market Selection</strong>
                        <div class="sub-text">The Stamp of Quality for Havanas in the UK</div>
                      </span></a></div>
                </li>
                <li class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-6-24 pure-u-xl-6-24">
                  <div class="sub-banner-pad sub-banner-1"><?php echo $header_banners_brands[0]; ?></div>
                </li>
                <li class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-6-24 pure-u-xl-6-24">
                  <div class="sub-banner-pad sub-banner-4"><?php echo $header_banners[0]; ?></div>
                </li>  
              </ul><!-- End Mega Menu -->
            </div><!-- End Mega Menu Container -->
          </div>

        </div>
      </div>
    <?php 

     } // if (!$mobile_view && !$print_view) {
    } // if($design != '2025'){

    ?>
    <?php if ($enable_bread) { ?>
    <div class="nav-container-inner">
      <div id="inner-wrapper">
        <?php if ($enable_bread) { ?><?php echo $breadcrumb->trail_schema(''); ?><?php } ?>
      </div>
    </div>
    <?php } ?>
    <?php if ($current_page == 'index.php' && $cyber_monday && !$main_page_url) { ?>
      <div class="nav-container">
        <div id="inner-wrapper">
          <div class="pure-u-24-24">
            <a class="bfd-small" href="specials.php">
              <div class="pure-g">
                <div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-8-24 pure-u-xl-8-24">
                  <div <?php if (!$mobile_view) {
                          echo 'style="float: left; padding-left: 20px;"';
                        } ?>>January Sale</div>
                </div>
                <div class="pure-u-24-24 pure-u-sm-12-24 pure-u-md-8-24 pure-u-lg-8-24 pure-u-xl-8-24">Shop Now!</div>
                <div class="pure-u-24-24 pure-u-sm-8-24 pure-u-md-8-24 pure-u-lg-8-24 pure-u-xl-8-24"></div>
              </div>
            </a>
          </div>
        </div>
      </div>
    <?php } ?>
  <?php } // end popup 
  ?>

  <?php
  if (isset($_GET['error_message']) && tep_not_null($_GET['error_message'])) {
  ?>
    <div class="nav-container-inner">
      <div id="inner-wrapper">
        <div class="pure-g">
          <div class="pure-u-24-24">
            <div class="box-padding-half">
              <div class="messagestack-error"><?php echo htmlspecialchars(stripslashes(urldecode($_GET['error_message']))); ?></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <?php
  }

  if (isset($_GET['info_message']) && tep_not_null($_GET['info_message'])) {
  ?>
    <div class="nav-container-inner">
      <div id="inner-wrapper">
        <div class="pure-g">
          <div class="pure-u-24-24">
            <div class="box-padding-half">
              <div class="messagestack-output"><?php echo htmlspecialchars(stripslashes(urldecode($_GET['info_message']))); ?></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <?php
  }
  ?>