<?php

function clean_html_comments($clean_html) {
global $its_cleaned;

if ( strpos($clean_html,'<!--//*')>1 ) {
  $the_end1= strpos($clean_html,'<!--//*')-1;
  $the_start2= strpos($clean_html,'*//-->')+7;

  $its_cleaned= substr($clean_html,0,$the_end1);
  $its_cleaned.= substr($clean_html,$the_start2);
} else {
  $its_cleaned= $clean_html;
}
  return $its_cleaned;
}
  
  function tep_og_image($src){
   $src = tep_image(DIR_WS_IMAGES . $src, $alt = '', 1000, 1000); // Thumbnail
   preg_match('/src="(.+?)"/', $src, $matches); // get filename
   $src = $matches[1];
   return HTTP_SERVER . '/' . $src;
  }

require(DIR_WS_LANGUAGES . $language . '/' . 'header_tags.php');

$tags_array = array();
$swap_charset = false;
// Define specific settings per page:
switch (true) {

	// CIGAR LIBRARY
    case (strstr($_SERVER['PHP_SELF'],'info_cigar_library.php') or strstr($PHP_SELF,'info_cigar_library.php')):
    
	if($page_title_text) {
     $tags_array['title'] = $page_title_text;
	} else {
	 $tags_array['title'] = 'Cigar Library';
	}
 
	break;
	
	// CUSTOMER SERVICE
    case (strstr($_SERVER['PHP_SELF'],'info_customer_service.php') or strstr($PHP_SELF,'info_customer_service.php')):
    
	if($page_title_text) {
     $tags_array['title'] = $page_title_text;
	} else {
	 $tags_array['title'] = 'Customer Service';
	}

	break;	

	// ARTICLES.PHP
    case (strstr($_SERVER['PHP_SELF'],'information.php') or strstr($PHP_SELF,'information.php')):

	$tags_array['desc'] = 'The C.Gars Ltd Media Centre. View all C.Gars Ltd media in this area.';
    $tags_array['title'] = 'Media Centre';
    
	break;

	// My Favourites
    case (strstr($_SERVER['PHP_SELF'],'favourites.php') or strstr($PHP_SELF,'favourites.php')):

	$tags_array['desc'] = 'The C.Gars Ltd Customer Favourites page. Customers can view their favourites using this page.';
    $tags_array['title'] = 'My Favourites';
    
	break;
	
	// My Wishlist
    case (strstr($_SERVER['PHP_SELF'],'wishlist.php') or strstr($PHP_SELF,'wishlist.php')):

	$tags_array['desc'] = 'The C.Gars Ltd Customer Wish list page. Customers can view their wish list using this page.';
    $tags_array['title'] = 'My Wishlist';
    
	break;		

	// Shopping Cart
    case ( strstr($_SERVER['PHP_SELF'],'shopping_cart.php') or strstr($PHP_SELF,'shopping_cart.php') ):

	$tags_array['title']= $title . ' ' . 'Cigar Shopping Basket';	
	
	break;				
			
	// Our Shops
  	case ( strstr($_SERVER['PHP_SELF'],'shop_locator.php') or strstr($PHP_SELF,'shop_locator.php') ):

	  $tags_array['title']= $title . ' ' . 'Shop Locator';	
	
	break;

	// INDEX.PHP
  case (strstr($_SERVER['PHP_SELF'],FILENAME_DEFAULT) or strstr($PHP_SELF,FILENAME_DEFAULT) ):

$the_category_query = tep_db_query("
    SELECT cd.categories_name, cd.categories_htc_title_tag, cd.categories_htc_desc_tag, cd.categories_htc_keywords_tag, c.categories_image
    FROM " . TABLE_CATEGORIES . " c
    JOIN " . TABLE_CATEGORIES_DESCRIPTION . " cd ON c.categories_id = cd.categories_id
    WHERE c.categories_id = '" . (int)$current_category_id . "'
    AND cd.language_id = '" . (int)$languages_id . "'
    LIMIT 1
");

    $the_category = tep_db_fetch_array($the_category_query);

    $the_manufacturers_query= tep_db_query("select manufacturers_name from " . TABLE_MANUFACTURERS . " where manufacturers_id = '" . (int)$_GET['manufacturers_id'] . "'");
    $the_manufacturers = tep_db_fetch_array($the_manufacturers_query);
 
    $showCatTags = false;
    if ($category_depth == 'nested' || ($category_depth == 'products' || isset($_GET['manufacturers_id']))) 
      $showCatTags = true;
    
    if (HTDA_DEFAULT_ON=='1') {
      if ($showCatTags == true) {
         if (HTTA_CAT_DEFAULT_ON=='1') {
           $tags_array['desc']= $the_category['categories_htc_desc_tag'] . ' ' . HEAD_DESC_TAG_DEFAULT . ' ' . HEAD_DESC_TAG_ALL;
         } else {
           $tags_array['desc']= $the_category['categories_htc_desc_tag'] . ' ' . HEAD_DESC_TAG_ALL;
         }
      } else {
        $tags_array['desc']= HEAD_DESC_TAG_DEFAULT . ' ' . HEAD_DESC_TAG_ALL;
      }
    } else {
      if ($showCatTags == true) {
         if (HTTA_CAT_DEFAULT_ON=='1') {
           $tags_array['desc']= $the_category['categories_htc_desc_tag'] . ' ' . HEAD_DESC_TAG_DEFAULT;
         } else {
           $tags_array['desc']= $the_category['categories_htc_desc_tag'];
         }
      } else {
        $tags_array['desc']= HEAD_DESC_TAG_DEFAULT;
      }  
    }

    if (HTTA_DEFAULT_ON=='1') {
      if ($showCatTags == true) {
        if (HTTA_CAT_DEFAULT_ON=='1') {
          $tags_array['title']= $the_category['categories_htc_title_tag'] .' '.  HEAD_TITLE_TAG_DEFAULT . " " .  $the_manufacturers['manufacturers_name'] . ' ' . HEAD_TITLE_TAG_ALL;
        } else {
          $tags_array['title']= $the_category['categories_htc_title_tag'] .' '.  $the_manufacturers['manufacturers_name'] . ' ' . HEAD_TITLE_TAG_ALL;
        }
      } else {
        $tags_array['title']= HEAD_TITLE_TAG_DEFAULT . " " . $the_category['categories_name'] . $the_manufacturers['manufacturers_name'] . ' ' . HEAD_TITLE_TAG_ALL;
      }
    } else {
      if ($showCatTags == true) {
        if (HTTA_CAT_DEFAULT_ON=='1') {
          $tags_array['title']= $the_category['categories_htc_title_tag'] . ' ' . HEAD_TITLE_TAG_DEFAULT;
        } else {
          $tags_array['title']= $the_category['categories_htc_title_tag'];
        } 
      } else {
        $tags_array['title']= HEAD_TITLE_TAG_DEFAULT;
      }  
    }

if ($showCatTags == true) {
    $open_graph  = '<meta property="og:title" content="' . $the_category['categories_htc_title_tag'] . '">' . "\n";
    $open_graph .= '<meta property="og:description" content="' . $the_category['categories_htc_desc_tag'] . '">' . "\n";
    $open_graph .= '<meta property="og:url" content="' . HTTP_SERVER . '">' . "\n";
    $open_graph .= '<meta property="og:image" content="' . tep_og_image($the_category['categories_image']) . '">' . "\n";
    $open_graph .= '<meta property="og:type" content="website">' . "\n";
} else { // index.php
    $open_graph  = '<meta property="og:title" content="' . HEAD_TITLE_TAG_DEFAULT . '">' . "\n";
    $open_graph .= '<meta property="og:description" content="' . HEAD_DESC_TAG_DEFAULT . '">' . "\n";
    $open_graph .= '<meta property="og:url" content="' . HTTP_SERVER . '">' . "\n";
    $open_graph .= '<meta property="og:image" content="' . HTTP_SERVER . '/images/optimize/720x450_CIGARBANNERHOMEPAGE.webp">' . "\n";
    $open_graph .= '<meta property="og:type" content="website">' . "\n";
}


    break;

// PRODUCT_INFO.PHP
  case ( strstr($_SERVER['PHP_SELF'],FILENAME_PRODUCT_INFO) or strstr($PHP_SELF,FILENAME_PRODUCT_INFO) ):
//    $the_product_info_query = tep_db_query("select p.products_id, pd.products_name, pd.products_description, pd.products_head_title_tag, pd.products_head_keywords_tag, pd.products_head_desc_tag, p.products_model, p.products_quantity, p.products_image, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = '" . $_GET['products_id'] . "' and pd.products_id = '" . $_GET['products_id'] . "'");
    $the_product_info_query = tep_db_query("select pd.language_id, p.products_id, pd.products_name, pd.products_description, pd.products_head_title_tag, pd.products_head_keywords_tag, pd.products_head_desc_tag, p.products_model, p.products_quantity, p.products_image, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = '" . $_GET['products_id'] . "' and pd.products_id = '" . $_GET['products_id'] . "'" . " and pd.language_id ='" .  $languages_id . "'");
    $the_product_info = tep_db_fetch_array($the_product_info_query);

    if (empty($the_product_info['products_head_desc_tag'])) {
      $tags_array['desc']= HEAD_DESC_TAG_ALL;
    } else {
      if ( HTDA_PRODUCT_INFO_ON=='1' ) {
        $tags_array['desc']= $the_product_info['products_head_desc_tag'] . ' ' . HEAD_DESC_TAG_ALL;
      } else {
        $tags_array['desc']= $the_product_info['products_head_desc_tag'];
      }
    }

    if (empty($the_product_info['products_head_keywords_tag'])) {
      $tags_array['keywords']= HEAD_KEY_TAG_ALL;
    } else {
      if ( HTKA_PRODUCT_INFO_ON=='1' ) {
        $tags_array['keywords']= $the_product_info['products_head_keywords_tag'] . ' ' . HEAD_KEY_TAG_ALL;
      } else {
        $tags_array['keywords']= $the_product_info['products_head_keywords_tag'];
      }
    }

    if (empty($the_product_info['products_head_title_tag'])) {
      $tags_array['title']= HEAD_TITLE_TAG_ALL;
    } else {
      if ( HTTA_PRODUCT_INFO_ON=='1' ) {
        $tags_array['title']= clean_html_comments($the_product_info['products_head_title_tag']) . ' ' . HEAD_TITLE_TAG_ALL;
      } else {
        $tags_array['title']= clean_html_comments($the_product_info['products_head_title_tag']);
      }
    }


    if ($new_price = tep_get_products_special_price($the_product_info['products_id'])) { 
	  $twitter_price = $currencies->display_price($new_price, tep_get_tax_rate($the_product_info['products_tax_class_id']));
    } else {
	  $twitter_price = $currencies->display_price($the_product_info['products_price'], tep_get_tax_rate($the_product_info['products_tax_class_id']));
    }	 

   if (tep_not_null($the_product_info['products_model'])) { 
   	$twitter_model = $the_product_info['products_model']; 
   } else {    
	$twitter_model = $the_product_info['products_id'];  
   } 
  
  $description = 'Price: ' . $twitter_price . ' - ' ;
  $description .= $the_product_info['products_description'];
  $description = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $description);
  $description = str_replace('"', "", $description);
  $description = str_replace("'", "", $description);
  $description = strip_tags($description, '<br>');
  $description = preg_replace( "/\r|\n/", "", $description);
  $description = preg_replace("/<br>/", " ", $description);
  $description = wordwrap($description, 196);
  //$description = substr($description, 0, 196);  
  $twitter_image = str_replace(' ', '%20', $the_product_info['products_image']);

?>
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:site" content="@cgarsltd">
<meta name="twitter:creator" content="">
<meta name="twitter:title" content="<?php echo $the_product_info['products_name']; ?>">
<meta name="twitter:description" content="<?php echo $description; ?> ">
<meta name="twitter:image:src" content="<?php echo 'https://www.cgarsltd.co.uk/images/' . $twitter_image; ?>">
<meta name="twitter:domain" content="https://www.cgarsltd.co.uk">
<?php

    break;			

// ALL OTHER PAGES NOT DEFINED ABOVE
  default:
    $tags_array['desc'] = $page_meta_desc;
    $tags_array['keywords'] = $page_meta_keywords;
    if($page_title_text) {
	 $tags_array['title'] = $page_title_text;
	} else {
	 $tags_array['title'] = HEAD_TITLE_TAG_ALL;
	}    
    break;
  }


if (!$info_page_header_text) { // default

echo '<title>' . $tags_array['title'] . '</title>' . "\n";
if($tags_array['desc']){
echo '<meta name="Description" Content="' . $tags_array['desc'] . '">' . "\n";
}
if($swap_charset) {
 echo '<meta http-equiv="Content-Type" content="text/html; charset=utf-8">'."\n";
} else {
 echo '<meta http-equiv="Content-Type" content="text/html; charset=' . CHARSET  . '">'."\n";
}

} else { // info page header text

 echo $info_page_header_text;

}


if (strstr($_SERVER['PHP_SELF'],FILENAME_DEFAULT) or strstr($PHP_SELF,FILENAME_DEFAULT)) {
 if(count($_GET) == 0) { // home page only
echo '<meta name="google-site-verification" content="duj0kV42udoRlUwf9RhkXml4VmiBj3y8la7kehsklFg" />'."\n";
echo '<meta name="GOOGLEBOT" content="ALL">'."\n"; 
echo '<meta name="REVISIT-AFTER" content="7 days">'."\n"; 
echo '<meta name="ROBOTS" CONTENT="all,follow,index">'."\n";
echo '<meta name="DISTRIBUTION" content="worldwide">'."\n"; 
echo '<meta name="RESOURCE-TYPE" content="document">'."\n"; 
echo '<meta name="DOCUMENT-STATE" CONTENT="DYNAMIC">'."\n"; 

  if($_SERVER['REQUEST_URI'] == '/index.php') {
echo '<link rel="canonical" href="' . HTTP_SERVER . '" />	
'."\n";   
  }
 }
} 

if($open_graph){ echo $open_graph; }

?>