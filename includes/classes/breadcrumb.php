<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

class breadcrumb {
    var $_trail;

    function __construct() {
      $this->reset();
    }

    function reset() {
      $this->_trail = array();
    }

    function add($title, $link = '') {
      $this->_trail[] = array('title' => $title, 'link' => $link);
    }

    function trail($separator = ' - ') {
      $trail_string = '';

      for ($i = 0, $n = sizeof($this->_trail); $i < $n; $i++) {
        if (isset($this->_trail[$i]['link']) && tep_not_null($this->_trail[$i]['link'])) {
          $trail_string .= '<span><a href="' . $this->_trail[$i]['link'] . '">' . $this->_trail[$i]['title'] . '</a></span>';
        } else {
          $trail_string .= '<span>' . $this->_trail[$i]['title'] . '</span>';
        }

        if (($i + 1) < $n) {
          $trail_string .= '<span class="material-icons">&#xe5cc;</span>' . $separator;
        }
      }

      return $trail_string;
    }

function trail_schema() {
  
  $position = 1;
  $trail_string = ''; // Initialize

  foreach ($this->_trail as $crumb) {
    $trail_string .= '<li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';

    if (!empty($crumb['link'])) {
      $trail_string .= '<a itemprop="item" href="' . htmlspecialchars($crumb['link']) . '">';
      $trail_string .= '<span itemprop="name">' . htmlspecialchars($crumb['title']) . '</span></a>';
    } else {
      $trail_string .= '<span itemprop="name">' . htmlspecialchars($crumb['title']) . '</span>';
    }

    $trail_string .= '<meta itemprop="position" content="' . $position . '" />';
    $trail_string .= '</li>';

    $position++;
  }

  if ($trail_string) {
    $final_trail_string  = '<ol class="breadcrumbs_box" itemscope itemtype="https://schema.org/BreadcrumbList">';
    $final_trail_string .= $trail_string;
    $final_trail_string .= '</ol>';
  } else {
    $final_trail_string = '';
  }

  return $final_trail_string;
  
}


}

?>
