<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

$defer_switch = 'defer';

?>
<?php if (!$popup_url && !$remove_footer) { ?>
    <?php if (!$print_view) {
     if ($design == '2025') {
        require(DIR_WS_INCLUDES . 'footer_2025.php');
     } else {
        require(DIR_WS_INCLUDES . 'footer.php');
     }
    } ?>
<?php } // end popup 
?>
<?php if ($show_fancybox) { ?>
    <script <?php echo $defer_switch; ?> src="//code.jquery.com/jquery-3.3.1.min.js"></script>
    <script <?php echo $defer_switch; ?> src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.3.5/jquery.fancybox.min.js"></script>
<?php } else { ?>
    <script <?php echo $defer_switch; ?> src="design/generic/js/jquery-2.1.1.min.js"></script>
<?php } ?>
<?php if ($add_jquery_full_ui) { ?>
    <script <?php echo $defer_switch; ?> src="design/generic/js/jquery-ui-1.11.1.js"></script>
<?php } ?>
<!-- <script src="design/generic/js/jquery-ui-1.11.1.js"></script>-->
<!-- TrustBox script -->
<script type="text/javascript" src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js" async></script>
<!-- End TrustBox script -->
<script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/respond.src.js?v=<?php echo $cv; ?>"></script>
<script <?php echo $defer_switch; ?> type="text/javascript" src="ext/jquery/mm/mgmenu_plugins.js?v=<?php echo $cv; ?>"></script>
<?php if ($mobile_view) { ?>
    <script <?php echo $defer_switch; ?> src="ext/jquery/mm/mgmenu_mobile.js?v=<?php echo $cv; ?>"></script><!-- Mega Menu Script -->
<?php } else { ?>
    <script <?php echo $defer_switch; ?> src="ext/jquery/mm/mgmenu.js?v=<?php echo $cv; ?>"></script><!-- Mega Menu Script -->
    <?php if ($remove_this_to_turn_on_snow) { ?><script src="design/generic/js/fallingsnow_v6.js?v=<?php echo $cv; ?>"></script><?php } ?>
<?php } ?>

<?php if ($show_nouislider) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/nouislider.min.js?v=<?php echo $cv; ?>"></script><?php } ?>
<?php if ($show_select2) { ?><script <?php echo $defer_switch; ?> src="design/generic/js/select2.min.js?v=<?php echo $cv; ?>"></script><?php } ?>
<?php if ($show_colorbox) { ?><script <?php echo $defer_switch; ?> src="ext/jquery/colorbox/jquery.colorbox-min.js?v=<?php echo $cv; ?>"></script><?php } ?>
<?php if ($show_owl_carousel && !$show_owl_carousel_two) { ?><script defer src="design/generic/js/owl.carousel.min.js"></script><?php } ?>
<?php if ($show_owl_carousel_two) { ?><script <?php echo $defer_switch; ?> src='https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.0.0-beta.3/owl.carousel.min.js'></script><?php } ?>
<?php if ($main_page_url) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js" async></script><?php } ?>
<?php if ($show_prod_info) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/jquery.poshytip.min.js"></script><?php } ?>
<?php if ($show_prod_info) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/product.page.js"></script><?php } ?>
<?php if ($show_pca) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="https://services.postcodeanywhere.co.uk/js/captureplus-2.10.min.js?key=hb28-uj26-bf88-xe97"></script><?php } ?>
<?php if ($show_html_extras) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/modernizr.custom.js"></script><?php } ?>
<?php if ($show_html_extras) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/jquery.dlmenu.js"></script><?php } ?>
<?php if ($show_html_extras) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="html/mini-site/js/domtab.js"></script><?php } ?>
<?php if ($show_countdown || $show_reverse_auction || $show_delivery_countdown) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/jquery.plugin.min.js"></script><?php } ?>
<?php if ($show_countdown || $show_reverse_auction || $show_delivery_countdown) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/jquery.countdown.min.js"></script><?php } ?>
<?php if ($show_ddaccordion) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="/html/ddaccordion.js"></script><?php } ?>
<?php if ($show_filter_menu) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/jquery.expander.min.js"></script><?php } ?>
<?php if ($show_fotorama) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/generic/js/fotorama.js"></script><?php } ?>
<?php if ($show_cigar_library_menu && $mobile_view) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="ext/jquery/mm/clmenu_mobile.js"></script><?php } ?>
<?php if ($show_davidoff) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="html/portals/davidoff/js/modernizr.js"></script><?php } ?>
<?php if ($show_davidoff) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="html/portals/davidoff/js/plugins.js"></script><?php } ?>
<?php if ($show_davidoff) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="html/portals/davidoff/js/main.js"></script><?php } ?>
<?php if ($our_shops_page_url) { ?><script <?php echo $defer_switch; ?> type="text/javascript" src="design/js/our-shops.min.js?v=1"></script><?php } ?>
<?php if ($show_recaptcha) { ?><script src='https://www.google.com/recaptcha/api.js'></script><?php } ?>
<?php if ($show_recaptcha_v3) { ?><script src="https://www.google.com/recaptcha/api.js?render=6LcFk2IpAAAAACstUS6VNZwG_VK7hi3495g8t8-7"></script><?php } ?>
<?php // if (!isset($_GET['rp'])){ 
?>
<script>
    window.addEventListener('load', function() { // defer inline js

        $('#mgmenu_top').universalMegaMenu({
            menu_effect: 'open_close_fade',
            menu_speed_show: 1,
            menu_speed_hide: 1,
            menu_speed_delay: 1,
            menu_click_outside: true,
            menubar_trigger: false,
            menubar_hide: false,
            menu_responsive: true
        });

        $('#mgmenu_davidoff').universalMegaMenu({
            menu_effect: 'open_close_fade',
            menu_speed_show: 1,
            menu_speed_hide: 1,
            menu_speed_delay: 1,
            menu_click_outside: true,
            menubar_trigger: false,
            menubar_hide: false,
            menu_responsive: true
        });

        $('#mgmenu1').universalMegaMenu({
            menu_effect: 'open_close_fade',
            menu_speed_show: 1,
            menu_speed_hide: 1,
            menu_speed_delay: 1,
            menu_click_outside: true,
            menubar_trigger: false,
            menubar_hide: false,
            menu_responsive: true
        });

        $('.menu-shopping-cart span').html($('.shopping-cart-links').clone());

        var maxHeight = -1;

        $('.subcategory_name p').each(function() {
            maxHeight = maxHeight > $(this).height() ? maxHeight : $(this).height();
        });

        $('.subcategory_name p').each(function() {
            $(this).height(maxHeight);
        });

        // fix jQuery 1.8.0 and jQuery UI 1.8.22 bug with dialog buttons; http://bugs.jqueryui.com/ticket/8484
        if ($.attrFn) {
            $.attrFn.text = true;
        }

        <?php if ($show_tool_tip) { ?>
            // Tooltip only Text
            $('.masterTooltip').hover(function() {
                // Hover over code
                var title = $(this).attr('title');
                $(this).data('tipText', title).removeAttr('title');
                $('<p class="tooltip"></p>')
                    .text(title)
                    .appendTo('body')
                    .fadeIn('slow');
            }, function() {
                // Hover out code
                $(this).attr('title', $(this).data('tipText'));
                $('.tooltip').remove();
            }).mousemove(function(e) {
                var mousex = e.pageX + 20; //Get X coordinates
                var mousey = e.pageY + 10; //Get Y coordinates
                $('.tooltip')
                    .css({
                        top: mousey,
                        left: mousex
                    })
            });
        <?php } ?>
        <?php if ($show_select2) { ?>

            function getInfo(parameters) {

                $.ajax({
                    type: 'GET',
                    url: 'ajax_filter.php?' + parameters,
                    dataType: 'json',
                    cache: false,
                    timeout: 10000,
                    success: function(data) {

                        if (data.cigar_region) {
                            $('#topatt22').empty().append(data.cigar_region);
                        }
                        if (data.ring_gauge) {
                            $('#topatt23').empty().append(data.ring_gauge);
                        }
                        if (data.cigar_strength) {
                            $('#topatt24').empty().append(data.cigar_strength);
                        }
                        if (data.cigar_length) {
                            $('#topatt26').empty().append(data.cigar_length);
                        }
                        if (data.vitola) {
                            $('#topatt35').empty().append(data.vitola);
                        }

                        $('#update_results').empty().val('Update Results (' + data.stock_total + ')');

                    }
                });

            }

            function formInfo() {
                var parameters = $('#top_filter_update').serialize();
                getInfo(parameters);
            }

            $(".js-top-search-select").select2();
            $(".js-top-normal-select").select2({
                minimumResultsForSearch: Infinity
            });

            $(".js-search-select").select2();
            $(".js-normal-select").select2({
                minimumResultsForSearch: Infinity
            });

            $('#top_filter_update').change(function(event) {
                if ($(event.target).is('#price-min') || $(event.target).is('#price-max')) {
                    // setTimeout(formInfo, 1200); // Longer delay for price-min and price-max - turn it off
                } else {
                    setTimeout(formInfo, 800); // Shorter delay for all other changes
                }
            });

            <?php
            if (isset($_GET['rating']) && is_numeric($_GET['rating'])) {
                $filter_star_rating = $_GET['rating'];
            } else {
                $filter_star_rating = '0';
            }
            ?>

                (function($) {
                    $.fn.addRating = function(options) {
                        var obj = this;
                        var settings = $.extend({
                            max: 5,
                            half: true,
                            fieldName: 'rating',
                            fieldId: 'rating',
                            icon: '<i class="star-on">&#xe838;</i>',
                            selectedRatings: <?php echo $filter_star_rating; ?>
                        }, options);
                        this.settings = settings;

                        // create the stars
                        for (var i = 1; i <= settings.max; i++) {
                            var star = $('<i/>').addClass('material-icons').html(this.settings.icon + '_border').data('rating', i).appendTo(this).click(
                                function() {
                                    obj.setRating($(this).data('rating'));
                                }
                            ).hover(
                                function(e) {
                                    obj.showRating($(this).data('rating'), false);
                                },
                                function() {
                                    obj.showRating(0, false);
                                }
                            );

                        }
                        // $(this).append('<input type="hidden" name="' + settings.fieldName + '" id="' + settings.fieldId + '" value="' + settings.selectedRatings + '" />');

                        obj.showRating(settings.selectedRatings, true);
                    };

                    $.fn.setRating = function(numRating) {
                        var obj = this;
                        $('#' + obj.settings.fieldId).val(numRating);
                        obj.showRating(numRating, true);
                        setTimeout(formInfo, 800);
                    };

                    $.fn.showRating = function(numRating, force) {
                        var obj = this;
                        if ($('#' + obj.settings.fieldId).val() == '' || force) {
                            $(obj).find('i').each(function() {
                                var icon = '<i class="star-off">&#xe838;</i>';
                                $(this).removeClass('selected');

                                if ($(this).data('rating') <= numRating) {
                                    icon = obj.settings.icon;
                                    $(this).addClass('selected');
                                }
                                $(this).html(icon);
                            })
                        }
                    }

                }(jQuery));

            $('.slider-rating').addRating();

        <?php } ?>

        <?php if ($show_nouislider) { //////////////////////////////// 
        ?>
            <?php if ($show_prod_info) { ?>

                <?php echo $slider_jquery_function; ?>

                <?php echo $slider_jquery; ?>

                <?php } else { // its the advanced search

                if (isset($_GET['price-min']) && is_numeric($_GET['price-min'])) {
                    $price_min = $_GET['price-min'];
                } else {
                    $price_min = '0';
                }
                if (isset($_GET['price-max']) && is_numeric($_GET['price-max'])) {
                    $price_max = $_GET['price-max'];
                } else {
                    $price_max = '10000';
                }

                if ($slider_removed) {
                ?>

                    var snapSlider = document.getElementById('slider-snap');

                    noUiSlider.create(snapSlider, {
                        start: [<?php echo $price_min; ?>, <?php echo $price_max; ?>],
                        snap: true,
                        connect: true,
                        range: {
                            'min': 0,
                            '10%': 5,
                            '20%': 10,
                            '30%': 25,
                            '40%': 50,
                            '50%': 100,
                            '60%': 250,
                            '70%': 500,
                            '80%': 1000,
                            'max': 10000
                        }
                    });

                    var snapValues = [
                        document.getElementById('slider-price-min'),
                        document.getElementById('slider-price-max')
                    ];

                    snapSlider.noUiSlider.on('update', function(values, handle) {
                        snapValues[handle].value = Math.round(values[handle]);
                    });

                    snapSlider.noUiSlider.on('change', function(values, handle) {
                        setTimeout(formInfo, 800);
                    });

                <?php } ?>
            <?php } ?>
        <?php } ?>

        <?php if ($news_modal_needed) { //////////////////////////////// 
        ?>

$(function () {
    $("#news_promo").on('submit', function (e) {
        e.preventDefault(); // Prevent default form submission

        var form = $(this);

        grecaptcha.ready(function () {
            grecaptcha.execute('6LcFk2IpAAAAACstUS6VNZwG_VK7hi3495g8t8-7', { action: 'newsletter_signup' }).then(function (token) {

                // Add token to hidden input
                $('#recaptcha_token').val(token);

                // 🚨 Wait for token to be added before sending AJAX
                var formData = form.serialize();

                $.ajax({
                    type: 'post',
                    url: 'ajax_news_promo.php',
                    data: formData,
                    dataType: 'json',
                    success: function (data) {
                        if (data.response_code == 100) {
                            $('#news-landing strong').html("<span>" + data.message + "</span>");
                            $('#news-email-submit').hide();
                        } else if (data.response_code == 502) {
                            $("#news-landing strong").html("<span><p>You're already signed up!</p><p>Before you go, why not enjoy &pound;5 off &pound;30? Just enter code:</p><h1>TREAT5</h1><p>at the checkout to get &pound;5.00 off when you spend &pound;30 or more on alcohol (one time use)</p></span>");
                            $('#news-email-submit').hide();
                        } else {
                            $('#news-landing strong').html("<span>" + data.message + "</span>");
                        }
                    },
                    error: function () {
                        alert("There was an error processing your request. Please try again.");
                    }
                });
            });
        });

        return false;
    });
});


    // Delay modal display
    var news_modal_milliseconds = <?php echo $news_modal_time_remaining; ?> * 1000;
    setTimeout(function () {
        var $modal = $('#popup');
        var $overlay = $('.overlay');
        var $closeModalButton = $('#close-modal');

        function showModal() {
            $modal.addClass('show');
            $overlay.addClass('show');
        }

        function closeModal() {
            $modal.removeClass('show');
            $overlay.removeClass('show');
        }

        showModal();

        $closeModalButton.on('click', function (event) {
            event.preventDefault();
            closeModal();
        });

        $overlay.on('click', closeModal);
    }, news_modal_milliseconds);

    $('#news-option-two').click(function () {
        parent.$.colorbox.close();
        return false;
    });

    $('#cboxClose').remove();

        <?php } ?>

        <?php if ($show_pca) { //////////////////////////////// 
        ?>

            capturePlus.listen("options", function(options) {
                options.bar = options.bar || {};
                options.bar.showCountry = false;
                options.suppressAutocomplete = false;
            });

            function isChn(str) {
                return /[\u3400-\u9FBF]/.test(str);
            }

            function isArabic(str) {
                return /[\u0600-\u06FF]/.test(str);
            }

            $('fieldset input').on('keyup input paste touchend', function() {
                var v = $(this).val();
                if (isChn(v) || isArabic(v)) {
                    $(this).val('');
                }
            });

            <?php if ($loyalty_points_jquery_code) {
                echo $loyalty_points_jquery_code;
            } ?>

        <?php } ?>

        <?php if ($show_owl_carousel) { ?>
            <?php if ($main_page_url) { ?>
                <?php if ($design == '2025') { ?>

                    var owlhomelarge = $("#owl-home-large");

                    owlhomelarge.owlCarousel({
                        transitionStyle: "fade",
                        items: 1, // 10 items above 1000px browser width
                        itemsDesktop: [1380, 1], //5 items between 1000px and 901px
                        itemsDesktopSmall: [979, 1], // betweem 900px and 601px
                        itemsTablet: [745, 1], //2 items between 600 and 0
                        itemsMobile: [479, 1], // itemsMobile disabled - inherit from itemsTablet
                        autoPlay: 6000,
                        pagination: false
                    });

var owlhomereasons = $("#owl-home-reasons");

owlhomereasons.owlCarousel({
    items: 3, // 10 items above 1000px browser width
    itemsDesktop: [1380, 3], //5 items between 1000px and 901px
    itemsDesktopSmall: [979, 2], // betweem 900px and 601px
    itemsTablet: [745, 1], //2 items between 600 and 0
    itemsMobile: [479, 1], // itemsMobile disabled - inherit from itemsTablet
    autoPlay: 6000,
    pagination: true
});   

var owlhomefeaturedone = $("#owl-home-featured-one");

owlhomefeaturedone.owlCarousel({
    transitionStyle: "fade",
    items: 4, // 10 items above 1000px browser width
    itemsDesktop: [1380, 4], //5 items between 1000px and 901px
    itemsDesktopSmall: [979, 2], // betweem 900px and 601px
    itemsTablet: [745, 2], //2 items between 600 and 0
    itemsMobile: [479, 2], // itemsMobile disabled - inherit from itemsTablet
    autoPlay: false,
    pagination: true
}); 

var owlhomefeaturedtwo = $("#owl-home-featured-two");

owlhomefeaturedtwo.owlCarousel({
    items: 4, // 10 items above 1000px browser width
    itemsDesktop: [1380, 4], //5 items between 1000px and 901px
    itemsDesktopSmall: [979, 2], // betweem 900px and 601px
    itemsTablet: [745, 2], //2 items between 600 and 0
    itemsMobile: [479, 2], // itemsMobile disabled - inherit from itemsTablet
    autoPlay: false,
    pagination: true
}); 

var owlhomevitola = $("#owl-home-vitola");

owlhomevitola.owlCarousel({
    items: 6, // 10 items above 1000px browser width
    itemsDesktop: [1380, 6], //5 items between 1000px and 901px
    itemsDesktopSmall: [979, 2], // betweem 900px and 601px
    itemsTablet: [745, 2], //2 items between 600 and 0
    itemsMobile: [479, 2], // itemsMobile disabled - inherit from itemsTablet
    autoPlay: false,
    pagination: true
}); 

var owlhomebrands = $("#owl-home-brands");

owlhomebrands.owlCarousel({
    items: 6, // 10 items above 1000px browser width
    itemsDesktop: [1380, 6], //5 items between 1000px and 901px
    itemsDesktopSmall: [979, 4], // betweem 900px and 601px
    itemsTablet: [745, 2], //2 items between 600 and 0
    itemsMobile: [479, 2], // itemsMobile disabled - inherit from itemsTablet
    autoPlay: false,
    pagination: true
}); 


                <?php } ?>

                var owlhome = $("#owl-home");

                owlhome.owlCarousel({
                    itemsScaleUp: false,

                    items: 3, // 10 items above 1000px browser width
                    itemsDesktop: [1380, 3], //5 items between 1000px and 901px
                    itemsDesktopSmall: [979, 1], // betweem 900px and 601px
                    itemsTablet: [745, 1], //2 items between 600 and 0
                    itemsMobile: [479, 1], // itemsMobile disabled - inherit from itemsTablet

                    autoPlay: 6000,
                    pagination: true
                });

            <?php } // end home page 
            ?>
            <?php if ($show_cart_scripts) { ?>

                var owlcart = $("#owl-cart");

                owlcart.owlCarousel({
                    <?php if ($pair_carousel_string) { ?>
                        items: 2, //4 items above 1000px browser width
                        itemsDesktop: [1000, 2], //3 items between 1000px and 760px
                    <?php } else { ?>
                        items: 4, //4 items above 1000px browser width
                        itemsDesktop: [1000, 3], //3 items between 1000px and 760px
                    <?php } ?>
                    itemsTablet: [760, 2], //2 items between 760 and 480
                    itemsMobile: [480, 1] //1 item between 480 and 0
                });

                var owlpair = $("#owl-pair");

                owlpair.owlCarousel({
                    items: 2, //4 items above 1000px browser width
                    itemsDesktop: [1000, 2], //3 items between 1000px and 760px
                    itemsTablet: [760, 2], //2 items between 760 and 480
                    itemsMobile: [480, 1] //1 item between 480 and 0
                });

                var owlfreeze = $("#owl-freeze");

                owlfreeze.owlCarousel({
                    items: 4, //4 items above 1000px browser width
                    itemsDesktop: [1000, 3], //3 items between 1000px and 760px
                    itemsTablet: [760, 2], //2 items between 760 and 480
                    itemsMobile: [480, 1] //1 item between 480 and 0
                });

            <?php } ?>
            <?php if ($owl_prod_info_slider || $show_nouislider) { ?>

                var owlalso = $("#owl-also");

                owlalso.owlCarousel({
                    items: 5, //4 items above 1000px browser width
                    navigation: true,
                    navigationText: [
                        "<i class='material-icons'>&#xe5cb;</i>",
                        "<i class='material-icons'>&#xe5cc;</i>"
                    ],
                    pagination: false
                });

            <?php } ?>
            <?php if ($owl_prod_info_slider) { ?>

                var owlalso = $("#owl-also2");

                owlalso.owlCarousel({
                    items: 5, //4 items above 1000px browser width
                    itemsDesktop: [1000, 4], //3 items between 1000px and 760px
                    itemsTablet: [760, 3], //2 items between 760 and 480
                    itemsMobile: [480, 2] //1 item between 480 and 0
                });

                <?php echo $prod_info_owl_related; ?>
                <?php echo $prod_info_owl_cross; ?>
                <?php echo $prod_info_owl_vitola; ?>

                var prod_info_bigimage = $("#prod_info_big");
                var prod_info_thumbs = $("#prod_info_thumbs");
                //var totalslides = 10;
                var syncedSecondary = true;

                prod_info_bigimage.owlCarousel({
                    items: 1,
                    slideSpeed: 4000,
                    nav: true,
                    autoplay: true,
                    autoplayHoverPause: true,
                    dots: false,
                    loop: true,
                    responsiveRefreshRate: 200,
                    navText: [
                        '<i class="material-icons" style="line-height: 40px;" aria-hidden="true">&#xe5cb;</i>',
                        '<i class="material-icons" style="line-height: 40px;" aria-hidden="true">&#xe5cc;</i>'
                    ],
                    responsive: {
                        0: {
                            autoplay: false
                        },
                        768: {
                            autoplay: true
                        }
                    }
                }).on("changed.owl.carousel", syncPosition);


                prod_info_thumbs.on("initialized.owl.carousel", function() {
                    prod_info_thumbs
                        .find(".owl-item")
                        .eq(0)
                        .addClass("current");
                }).owlCarousel({
                    items: 4,
                    dots: true,
                    nav: false,
                    navText: [
                        '<i class="fa fa-arrow-left" aria-hidden="true"></i>',
                        '<i class="fa fa-arrow-right" aria-hidden="true"></i>'
                    ],
                    smartSpeed: 200,
                    slideSpeed: 500,
                    slideBy: 4,
                    responsiveRefreshRate: 100
                }).on("changed.owl.carousel", syncPosition2);

                function syncPosition(el) {

                    //to disable loop, comment this block
                    var count = el.item.count - 1;
                    var current = Math.round(el.item.index - el.item.count / 2 - 0.5);

                    if (current < 0) {
                        current = count;
                    }
                    if (current > count) {
                        current = 0;
                    }
                    //to this
                    prod_info_thumbs
                        .find(".owl-item")
                        .removeClass("current")
                        .eq(current)
                        .addClass("current");
                    var onscreen = prod_info_thumbs.find(".owl-item.active").length - 1;
                    var start = prod_info_thumbs
                        .find(".owl-item.active")
                        .first()
                        .index();
                    var end = prod_info_thumbs
                        .find(".owl-item.active")
                        .last()
                        .index();

                    if (current > end) {
                        prod_info_thumbs.data("owl.carousel").to(current, 100, true);
                    }
                    if (current < start) {
                        prod_info_thumbs.data("owl.carousel").to(current - onscreen, 100, true);
                    }
                }

                function syncPosition2(el) {
                    if (syncedSecondary) {
                        var number = el.item.index;
                        prod_info_bigimage.data("owl.carousel").to(number, 100, true);
                    }
                }

                prod_info_thumbs.on("click", ".owl-item", function(e) {
                    e.preventDefault();
                    var number = $(this).index();
                    prod_info_bigimage.data("owl.carousel").to(number, 300, true);
                    prod_info_bigimage.trigger('stop.owl.autoplay');
                });

            <?php } // if($owl_prod_info_slider){ 
            ?>
        <?php } // end owl 
        ?>
        <?php if ($show_prod_info) { ?>

            <?php if (tep_session_is_registered('customer_id')) { ?>

                function checkForm() {
                    var error = 0;
                    var error_message = "<?php echo JS_ERROR; ?>";

                    var review = document.product_reviews_write.review.value;

                    if (review.length < <?php echo REVIEW_TEXT_MIN_LENGTH; ?>) {
                        error_message = error_message + "<?php echo JS_REVIEW_TEXT; ?>";
                        error = 1;
                    }

                    if ((document.product_reviews_write.rating[0].checked) || (document.product_reviews_write.rating[1].checked) || (document.product_reviews_write.rating[2].checked) || (document.product_reviews_write.rating[3].checked) || (document.product_reviews_write.rating[4].checked)) {} else {
                        error_message = error_message + "<?php echo JS_REVIEW_RATING; ?>";
                        error = 1;
                    }

                    if (error == 1) {
                        alert(error_message);
                        return false;
                    } else {
                        return true;
                    }
                }
            <?php } ?>

            <?php if ($show_cigar_length_java) { ?>

                function toFraction() {

                    var n = $("#products-cigar-length").text();
                    var x = n;
                    var result = 0;

                    if (!isNaN(n) && n !== '' && n !== '0' && n !== '0.0000000000' && n !== 'Infinity' && n !== '-Infinity') {
                        var tolerance = 1.0E-6;
                        var h1 = 1;
                        var h2 = 0;
                        var k1 = 0;
                        var k2 = 1;
                        do {
                            var a = Math.floor(n);
                            var aux = h1;
                            h1 = a * h1 + h2;
                            h2 = aux;
                            aux = k1;
                            k1 = a * k1 + k2;
                            k2 = aux;
                            n = 1 / (n - a);
                        } while (Math.abs(x - h1 / k1) > x * tolerance);


                        var num = h1;
                        var den = k1;

                        var num2 = num % den;
                        var den2 = den;
                        var fr = (num - num2) / den;

                        if (fr !== 0) {
                            if (num2 !== 0) {
                                result = '' + fr + ' ' + num2 + '/' + den2 + '';
                            } else {
                                result = '' + fr + '';
                            }
                        } else {
                            result = '' + num + '/' + den + '';
                        }

                    }

                    $("#products-cigar-length").html(result);

                }

                toFraction();

            <?php } ?>
            <?php if (tep_session_is_registered('customer_id')) { ?>

                $("#add-to-fav").click(function() {
                    $.ajax({
                        url: 'ajax_add_to_favourites.php?products_id=<?php echo $product_info['products_id']; ?>',
                        dataType: 'json',
                        cache: false,
                        success: function(data) {

                            if (data.status == 1) {
                                $("#add-to-fav").switchClass("add-to-fav-off", "add-to-fav-on", 1000, "easeInOutQuad");
                            }

                            if (data.status == 2) {
                                $("#add-to-fav").switchClass("add-to-fav-on", "add-to-fav-off", 1000, "easeInOutQuad");
                            }

                            $('.fav-num').empty();
                            $('.fav-num').append(data.fav_num);

                            $('.favourite-span').empty();
                            $('.favourite-span').append(data.message);

                        }
                    });
                });
                $("#add-to-notify").click(function() {
                    $.ajax({
                        url: 'ajax_add_to_notify.php?products_id=<?php echo $product_info['products_id']; ?>',
                        dataType: 'json',
                        cache: false,
                        success: function(data) {

                            if (data.status == 1) {
                                $("#add-to-notify").switchClass("notify-button-off", "notify-button-on", 1000, "easeInOutQuad");
                            }

                            if (data.status == 2) {
                                $("#add-to-notify").switchClass("notify-button-on", "notify-button-off", 1000, "easeInOutQuad");
                            }

                            $("#add-to-notify").html(data.message);

                        }
                    });
                });

            <?php } ?>

            // $('#cross-sell-box').delay(4000).slideDown('slow').delay(6000).slideUp('slow');
            $('#cross-sell-box').delay(4000).slideDown('slow');
            $('#cross-sell-box').hover(function() {
                $(this).stop(true, true).slideDown();
            }, function() {
                // $(this).stop(true, true).slideUp();
            });

            $('.cross-sell-cross').click(function() {
                $('#cross-sell-box').hide();
            });

            jQuery(function($) {
                var input = $('#cart_quantity'),
                    preview = $('.cart_quantity_value');

                input.keyup(function(e) {
                    preview.text(input.val());
                });
            });

        <?php } ?>

        <?php if ($show_colorbox) { //////////////////////////////// 
        ?>

            $(".group2").colorbox({
                rel: 'group2',
                transition: "fade",
                arrowKey: false,
                current: false,
                maxWidth: "90%",
                maxHeight: "90%"
            });
            $("#availability").colorbox({
                iframe: true,
                transition: "fade",
                arrowKey: false,
                width: "90%",
                height: "90%"
            });
            $(".source-product").colorbox({
                iframe: true,
                transition: "fade",
                arrowKey: false,
                width: "90%",
                height: "90%"
            });
            $(".request-price-button").colorbox({
                iframe: true,
                transition: "fade",
                arrowKey: false,
                width: "90%",
                height: "90%"
            });
            $(".upload-button").colorbox({
                iframe: true,
                width: "90%",
                height: "90%"
            });
            $(".inline").colorbox({
                inline: true,
                width: "50%"
            });
            <?php if ($exclusive_product == '1') { ?>
                $(".exclusive-button").colorbox({
                    inline: true,
                    href: '#exclusive-modal',
                    innerWidth: 300,
                    innerHeight: 160
                });
            <?php } ?>
            $("#cookie-help-chrome").colorbox({
                iframe: true,
                width: "90%",
                height: "90%"
            });
            $("#cookie-help-firefox").colorbox({
                iframe: true,
                width: "90%",
                height: "90%"
            });
            $("#cookie-help-edge").colorbox({
                iframe: true,
                width: "90%",
                height: "90%"
            });
            $("#cookie-help-ie").colorbox({
                iframe: true,
                width: "90%",
                height: "90%"
            });
            $("#cookie-help-opera").colorbox({
                iframe: true,
                width: "90%",
                height: "90%"
            });
            $("#cookie-help-safari").colorbox({
                iframe: true,
                width: "90%",
                height: "90%"
            });

            $('.youtubeVideo').colorbox({
                rel: 'group2',
                iframe: true,
                width: '90%',
                height: '90%',
                href: function() {
                    var videoId = new RegExp('[\\?&]v=([^&#]*)').exec(this.href);
                    if (videoId && videoId[1]) {
                        return 'https://www.youtube.com/embed/' + videoId[1] + '?rel=0&wmode=transparent';
                    }
                }
            });

        <?php } ?>

        <?php if ($show_html_extras) { ?>

            $(function() {
                $('#dl-menu').dlmenu({
                    animationClasses: {
                        classin: 'dl-animate-in-2',
                        classout: 'dl-animate-out-2'
                    }
                });
            });

            $('font').contents().unwrap();

            $('#limited h9').each(function() {
                var tis = $(this),
                    state = false,
                    answer = tis.next('div').hide().css('height', 'auto').slideUp();
                tis.click(function() {
                    state = !state;
                    answer.slideToggle(state);
                    tis.toggleClass('active', state);
                });
            });

            $('#faqs h5').each(function() {
                var tis = $(this),
                    state = false,
                    answer = tis.next('div').hide().css('height', 'auto').slideUp();
                tis.click(function() {
                    state = !state;
                    answer.slideToggle(state);
                    tis.toggleClass('active', state);
                });
            });

            var sourceSwap = function() {
                var $this = $(this);
                var newSource = $this.data('alt-src');
                $this.data('alt-src', $this.attr('src'));
                $this.attr('src', newSource);
            }

            $(function() {
                $('img[data-alt-src]').each(function() {
                    new Image().src = $(this).data('alt-src');
                }).hover(sourceSwap, sourceSwap);
            });


            $(".team-link").colorbox({
                inline: true,
                width: '80%',
                className: 'team-modal'
            });
            $('#cboxClose').remove();

        <?php } ?>

        <?php if ($show_countdown || $show_delivery_countdown) { //////////////////////////////// 
        ?>

            function serverTime() {
                var time = null;
                $.ajax({
                    cache: false,
                    url: '<?php echo HTTP_SERVER; ?>/serverTime.php',
                    async: false,
                    dataType: 'text',
                    success: function(text) {
                        time = new Date(text);
                    },
                    error: function(http, message, exc) {
                        time = new Date();
                    }
                });
                return time;
            }

        <?php } ?>
        <?php if ($show_delivery_countdown) { ////////////////////////////////

            function tep_check_invalid_date($date, $type = 'cutoff', $lighter_or_gas_in_cart = false) {

                // is it a weekend
                $day = date('N', strtotime($date)); // 1 = Monday 7 = Sunday

                // is it a holiday
                if ($type == 'cutoff') {
                    $column = 'shipping_holiday_no_sending';
                } else {
                    $column = 'shipping_holiday_no_receiving';
                }
                $holiday_query = tep_db_query("select shipping_holiday_id from shipping_holidays where " . $column . " = '1' and shipping_holiday_date = '" . date('Y/m/d 00:00:00', strtotime($date)) . "'");
                if (tep_db_num_rows($holiday_query)) {
                    $holiday = true;
                }

                if ($day == 6 && $type == 'cutoff') { // Saturday
                    return date('Y/m/d G:i:s', strtotime($date . " +2 days"));
                } elseif ($day == 6 && $type == 'delivery' && $lighter_or_gas_in_cart) { // No Gas / Lighter Delivery on Saturday
                    return date('Y/m/d G:i:s', strtotime($date . " +2 days"));
                } elseif ($day == 7 || $holiday) { // Sunday or holiday
                    return date('Y/m/d G:i:s', strtotime($date . " +1 days"));
                } else {
                    return false; // The date is good
                }
            }

            function tep_approved_date($date, $type = 'cutoff', $lighter_or_gas_in_cart = false) {

                $date_check = true;

                while ($date_check) {
                    $date_check = tep_check_invalid_date($date, $type, $lighter_or_gas_in_cart);
                    if ($date_check) {
                        $date = $date_check;
                    }
                }

                return $date;
            }

            $dc_current_date = date('Y/m/d');
            $dc_cutoff_hour = 14; // 14:00:00
            $dc_cutoff_time = $dc_cutoff_hour . ':00:00';

            // 1) Cut Off times...

            if (date('H') < $dc_cutoff_hour) {
                $cutoff_one = $dc_current_date . ' ' . $dc_cutoff_time; // today 2pm
            } else {
                $cutoff_one = date('Y/m/d', strtotime($dc_current_date . " +1 days")) . ' ' . $dc_cutoff_time; // tomorrow 2pm
            }
            $cutoff_one = tep_approved_date($cutoff_one, 'cutoff', $lighter_or_gas_in_cart);

            $cutoff_two = date('Y/m/d', strtotime($cutoff_one . " +1 days")) . ' ' . $dc_cutoff_time;

            $cutoff_two = tep_approved_date($cutoff_two, 'cutoff', $lighter_or_gas_in_cart);

            // 2) Delivery dates...

            $delivery_one = date('Y/m/d', strtotime($cutoff_one . " +1 days")) . ' ' . $dc_cutoff_time;

            $delivery_one = tep_approved_date($delivery_one, 'delivery', $lighter_or_gas_in_cart);

            $delivery_two = date('Y/m/d', strtotime($cutoff_two . " +1 days")) . ' ' . $dc_cutoff_time;

            $delivery_two = tep_approved_date($delivery_two, 'delivery', $lighter_or_gas_in_cart);

            // 3) Delivery days...

            if (date('Y/m/d', strtotime($dc_current_date . " +1 days")) == date('Y/m/d', strtotime($delivery_one))) {
                $delivery_day_one = 'tomorrow';
            } else {
                $delivery_day_one = date('l', strtotime($delivery_one));
            }
            $delivery_day_two = date('l', strtotime($delivery_two));

            if ((date('H') < $dc_cutoff_hour && date('N', strtotime($dc_current_date)) == '5') || (date('H') >= $dc_cutoff_hour && date('N', strtotime($dc_current_date)) == '4')) {
                $countdown_delivery_method = 'UK Royal Mail Saturday Delivery';
                $countdown_delivery_method_two = '';
            } else {
                $countdown_delivery_method = 'UK Royal Mail Next Day Delivery';
                $countdown_delivery_method_two = '';
            }

        ?>

            var cutoff_one = '<?php echo $cutoff_one; ?>';
            var cutoff_two = '<?php echo $cutoff_two; ?>';

            var cutoffLayout = '<span>Order within the next {h<}<span class="remove-at-zero-{hn}"><strong>{hn}</strong> {hl}</span>{h>} {m<}<span><strong>{mn}</strong> {ml} </span>{m>} for UK delivery <strong><?php echo $delivery_day_one; ?></strong><div class="deliveryCountdownSubtext"><?php echo $countdown_delivery_method; ?></div></span>';

            var cutoffLayoutTwo = '<span>Order within the next {h<}<span class="remove-at-zero-{hn}"><strong>{hn}</strong> {hl}</span>{h>} {m<}<span><strong>{mn}</strong> {ml} </span>{m>} for UK delivery <strong><?php echo $delivery_day_two; ?></strong><div class="deliveryCountdownSubtext"><?php echo $countdown_delivery_method; ?></div></span>';

<?php if ($design == '2025') { ?>

            var cutoffLayoutThree = '<div>Order within&nbsp;<span>{h<}{hnn}:{h>}{m<}{mnn}:{m>}{s<}{snn}{s>}</span> for UK delivery <?php echo $delivery_day_one; ?> <?php echo $countdown_delivery_method_two; ?></div>';

            var cutoffLayoutFour = '<div>Order within&nbsp;<span>{h<}{hnn}:{h>}{m<}{mnn}:{m>}{s<}{snn}{s>}</span> for UK delivery <?php echo $delivery_day_two; ?> <?php echo $countdown_delivery_method_two; ?></div>';

<?php } else { ?>

            var cutoffLayoutThree = '<div>Order within&nbsp;&nbsp;<span>{h<}{hnn}:{h>}{m<}{mnn}:{m>}{s<}{snn}{s>}</span><br />for UK delivery <?php echo $delivery_day_one; ?> <?php echo $countdown_delivery_method_two; ?></div>';

            var cutoffLayoutFour = '<div>Order within&nbsp;&nbsp;<span>{h<}{hnn}:{h>}{m<}{mnn}:{m>}{s<}{snn}{s>}</span><br />for UK delivery <?php echo $delivery_day_two; ?> <?php echo $countdown_delivery_method_two; ?></div>';

<?php } ?>  

            function nextDeliveryAlt() {
                setDeliveryTimeAlt = new Date(cutoff_two);
                $('#deliveryCountdownTwo').countdown('option', {
                    until: setDeliveryTimeAlt,
                    serverSync: serverTime,
                    format: 'HMS',
                    layout: cutoffLayoutFour,
                    onExpiry: function() {
                        $('#deliveryCountdownTwo').hide();
                    }
                });
            }

            setDeliveryTimeAlt = new Date(cutoff_one);
            $('#deliveryCountdownTwo').countdown({
                until: setDeliveryTimeAlt,
                serverSync: serverTime,
                format: 'HMS',
                layout: cutoffLayoutThree,
                onExpiry: nextDeliveryAlt
            });

        <?php } ?>

        <?php if ($christmas && $main_page_url) { ?>

            ChristmasStarts = new Date('2020/12/25 00:00:00');
            $('#christmasCountdown').countdown({
                until: ChristmasStarts,
                serverSync: serverTime,
                layout: '<div><span><strong> {dn}</strong> {dl}, <strong>{hn}</strong> {hl}, <strong>{mn}</strong> {ml}, and <strong>{sn}</strong> {sl} <strong>until Christmas! </strong></span></div>',
                onExpiry: function() {
                    $('#christmasCountdown').hide();
                }
            });

        <?php } ?>
        <?php if ($show_cigar_nominations) { ?>

            // select brand and get products
            $('.cigar_brand').change(function() {
                $this = $(this);
                var id = $(this).attr('data-id');
                replaceProductPlaceholder($this, id); //replace product image with placeholder

                $.ajax({
                    url: "ajax_cigar_of_the_year.php",
                    type: 'GET',
                    data: 'action=getProductsFromBrand&cID=' + $(this).val(),
                    success: function(res) {
                        json = JSON.parse(res);
                        if (json.prod_opt != '') {
                            $this.parents('.cigar_block').find('.cigar_product').html(json.prod_opt);
                        } else {
                            $this.parents('.cigar_block').find('.cigar_product').html('<option value=""><?php echo SELECT_CIGAR; ?></option>');
                        }
                    },
                    error: function(e) {
                        alert('Error! while performing this action. Please try again!');
                    }
                });
            });

            // select products
            $('.cigar_product').change(function() {
                var pid = $(this).val();
                var id = $(this).attr('data-id');
                $this = $(this);

                if (pid != '') {
                    $.ajax({
                        url: "ajax_cigar_of_the_year.php",
                        type: 'GET',
                        data: 'action=getProductsFromId&pID=' + $(this).val(),
                        success: function(res) {
                            json = JSON.parse(res);
                            if (json.prod_opt != '') {
                                $this.parents('.cigar_block').find('.cigar_block_img').html(json.prod_opt);
                            } else {
                                replaceProductPlaceholder($this, id);
                            }
                        },
                        error: function(e) {
                            alert('Error! while performing this action. Please try again!');
                        }
                    });
                } else {
                    replaceProductPlaceholder($this, id);
                }

                // show/hide form if all products are selected
                if ($("#cigar_1_product").val() != '' && $("#cigar_2_product").val() != '' && $("#cigar_3_product").val() != '') {
                    $('.cigar_form').show();
                } else {
                    $('.cigar_form').hide();
                }
            });

            function replaceProductPlaceholder($this, id) {
                if (id == 1) {
                    $this.parents('.cigar_block').find('.cigar_block_img').html('<img width="300px" height="300px" src="design/generic/images/vote_gold.jpg" />');
                } else if (id == 2) {
                    $this.parents('.cigar_block').find('.cigar_block_img').html('<img width="300px" height="300px" src="design/generic/images/vote_silver.jpg" />');
                } else {
                    $this.parents('.cigar_block').find('.cigar_block_img').html('<img width="300px" height="300px" src="design/generic/images/vote_bronze.jpg" />');
                }
            }

        <?php } ?>
        <?php if ($show_cg_plus_scripts) { ?>

            var currentDate = new Date();
            var eighteenYearsAgo = new Date();
            eighteenYearsAgo.setFullYear(currentDate.getFullYear() - 18);

            $('#birthday_date').datepicker({
                inline: true,
                showOtherMonths: true,
                dayNamesMin: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                changeYear: true, // Allow changing the year
                yearRange: "-100:+0", // Set a range of years (100 years back till now)
                maxDate: eighteenYearsAgo, // Maximum date is 18 years ago from today
                dateFormat: 'dd/mm/yy'
            });

            $('#copy-button').click(function() {
                var referralLink = $('#referral-link').text(); // Get the referral link text

                // Create a temporary input element to hold the referral link text
                var $tempInput = $('<input>');
                $('body').append($tempInput);
                $tempInput.val(referralLink).select();

                // Copy the text to the clipboard
                document.execCommand('copy');

                // Remove the temporary input element after copying
                $tempInput.remove();

                // Optional: Show an alert or notification
                alert('Your link has been copied to clipboard!');
            });

        <?php } ?>
        <?php if ($show_ship_rates) { ?>

            $('#load-shipping').load('shipping_rates.php');

            $(document).ajaxComplete(function() {
                // fire when any Ajax requests complete
                $('.ship-container .ship-country').click(function(event) {

                    event.preventDefault();

                    if ($(this).hasClass('country-selected')) {
                        $('.ship-country').removeClass('country-selected');
                    } else {
                        $('.ship-country').removeClass('country-selected');
                        $(this).addClass('country-selected');
                    }

                    // hide all span
                    var $this = $(this).parent().find('.ship-table');

                    $(".ship-container .ship-table").not($this).hide();

                    // here is what I want to do

                    $this.show();
                });

                $('.ship-table').click(function(event) {
                    $(".ship-table").hide();
                    $('.ship-country').removeClass('country-selected');
                });
            });

        <?php } ?>
        <?php if ($show_subscription_scripts) { //////////////////////////////// 
        ?>

            $(".subscription-cart-update").click(function() {

                var update_type = $(this).val();

                $.ajax({
                    type: 'post',
                    url: 'subscriptions_update_ajax.php?ajax_sub_action=' + update_type,
                    data: $(this).closest('form').serialize(),
                    dataType: 'json',
                    success: function(data) {

                        $('#qty' + data.products_id).empty();
                        $('#qty' + data.products_id).append(data.quantity);
                        if (data.notice == '1') {
                            $.colorbox({
                                inline: true,
                                href: '#subscription-modal',
                                innerWidth: 300,
                                innerHeight: 240
                            });
                        }
                    }

                });

                return false;

            });


        <?php } ?>
        <?php if ($show_subscription_renewal_scripts) { //////////////////////////////// 
        ?>

            $(function() {
                $("#subscription_interval").bind('submit', function() {
                    $.ajax({
                        type: 'post',
                        url: 'ajax_subscriptions.php',
                        data: $('form').serialize(),
                        dataType: 'json',
                        success: function(data) {

                            $('#output-intervals-long').empty();
                            $('#output-intervals-long').append(data.output_intervals);

                            $('#sub-message').empty().hide();
                            $('#sub-message').append(data.sub_message).fadeIn(2000);

                            if (data.interval_ready == 1) {
                                // $('input[name="interval_ready"]').val(1);

                                var skip_ready = $('input[name="skip_ready"]').val();

                                if (skip_ready == 1) {
                                    $('.continue-button').html('Continue to Checkout<i class=\"material-icons\">&#xe5cc;</i>');
                                    $('.continue-button').attr('href', '<?php echo tep_href_link(FILENAME_CHECKOUT_PAYMENT); ?>');
                                }

                                if (data.next_dispatch_date) {
                                    $('#check-renewal-date').html('<strong><i class=\"material-icons material-green\">&#xe834;</i> <span>Renewal Dates</span></strong><br />Your next scheduled date is<br />' + data.next_dispatch_date);
                                    $('#check-renewal-date-long').html(data.next_dispatch_date_long);
                                    $('#warning-renewal-date').empty().hide();
                                    $('.hide-me').empty().hide();
                                    $('.reveal-me').show();
                                }

                            }

                        }

                    });

                    return false;

                });
            });

            function getsubdate(sub_interval, sub_day, activation_date, sID, order_today) {

                $.ajax({
                    url: 'ajax_subscriptions.php?sub_interval=' + sub_interval + '&sID=' + sID + '&sub_day=' + sub_day + '&order_today=' + order_today + '&activation_date=' + activation_date.replace(/\//g, "-"),
                    dataType: 'json',
                    cache: false,
                    timeout: 10000,
                    success: function(data) {

                        $('#output-intervals-short').empty();
                        $('#output-intervals-short').append(data.output_intervals);

                    }
                });
            }

            var sub_interval = $("select[name='sub_interval']");
            var sub_day = $("select[name='sub_day']");
            var activation_date = $('#activation_date');
            var sID_interval = $('#sID_interval');
            var order_today = $('#order_today');

            getsubdate(sub_interval.val(), sub_day.val(), activation_date.val(), sID_interval.val(), order_today.val());

            sub_interval.change(function() {

                if ($(this).val() == 1 || $(this).val() == 2) {
                    $('select[name="sub_day"] option').remove();
                    $('<option value="1">1st</option>').appendTo(sub_day);
                    $('<option value="9">9th</option>').appendTo(sub_day);
                    $('<option value="17">17th</option>').appendTo(sub_day);
                    $('<option value="25">25th</option>').appendTo(sub_day);
                }

                if ($(this).val() == 3 || $(this).val() == 4) {
                    $('select[name="sub_day"] option').remove();
                    $('<option value="1">Monday</option>').appendTo(sub_day);
                    $('<option value="2">Tuesday</option>').appendTo(sub_day);
                    $('<option value="3">Wednesday</option>').appendTo(sub_day);
                    $('<option value="4">Thursday</option>').appendTo(sub_day);
                    $('<option value="5">Friday</option>').appendTo(sub_day);
                }

                getsubdate(sub_interval.val(), sub_day.val(), activation_date.val(), sID_interval.val(), order_today.val());

            });

            sub_day.change(function() {

                getsubdate(sub_interval.val(), sub_day.val(), activation_date.val(), sID_interval.val(), order_today.val());

            });

            activation_date.change(function() {

                getsubdate(sub_interval.val(), sub_day.val(), activation_date.val(), sID_interval.val(), order_today.val());

            });

            var currentDate = new Date();

            $('#activation_date').datepicker({
                inline: true,
                showOtherMonths: true,
                dayNamesMin: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                changeYear: false,
                <?php if (!$subscriptions_js_start_date) { ?>
                    minDate: currentDate,
                <?php } else { ?>
                    minDate: currentDate,
                    //    minDate: new Date('<?php //echo $subscriptions_js_start_date; 
                                                ?>'),
                <?php } ?>
                dateFormat: 'dd/mm/yy'
            });

            <?php if (!$subscriptions_start_date) { ?>
                $("#activation_date").datepicker("setDate", currentDate);
            <?php } ?>

        <?php } ?>
        <?php if ($show_subscription_list_scripts) { //////////////////////////////// 
        ?>
            $(".sagepay-iframe").colorbox({
                iframe: true,
                innerWidth: 600,
                innerHeight: 500,
                overlayClose: false,
                escKey: false,
                width: '70%',
                height: '80%',
                className: 'color-sagepay'
            });
            $('#cboxClose').remove();
            $(".sagepay-iframe").colorbox({
                onClosed: function() {
                    parent.location.reload();
                }
            });

        <?php } ?>
        <?php if ($our_shops_page_url) { //////////////////////////////// 
        ?>
            <?php if ($mobile_view) { ?>
                $(".our-shops-iframe").colorbox({
                    iframe: true,
                    innerWidth: 600,
                    innerHeight: 500,
                    width: '90%',
                    height: '90%',
                    className: 'color-our-shops'
                });
            <?php } else { ?>
                $(".our-shops-iframe").colorbox({
                    iframe: true,
                    innerWidth: 600,
                    innerHeight: 500,
                    width: '70%',
                    height: '80%',
                    className: 'color-our-shops'
                });
            <?php } ?>
        <?php } ?>
        <?php if ($show_ddaccordion) { //////////////////////////////// 
        ?>

            ddaccordion.init({
                headerclass: "mypets", //Shared CSS class name of headers group
                contentclass: "thepet", //Shared CSS class name of contents group
                revealtype: "mouseover", //Reveal content when user clicks or onmouseover the header? Valid value: "click", "clickgo", or "mouseover"
                mouseoverdelay: 200, //if revealtype="mouseover", set delay in milliseconds before header expands onMouseover
                collapseprev: true, //Collapse previous content (so only one open at any time)? true/false
                defaultexpanded: [0], //index of content(s) open by default [index1, index2, etc]. [] denotes no content.
                onemustopen: false, //Specify whether at least one header should be open always (so never all headers closed)
                animatedefault: false, //Should contents open by default be animated into view?
                scrolltoheader: true, //scroll to header each time after it's been expanded by the user?
                persiststate: true, //persist state of opened contents within browser session?
                toggleclass: ["", "openpet"], //Two CSS classes to be applied to the header when it's collapsed and expanded, respectively ["class1", "class2"]
                togglehtml: ["none", "", ""], //Additional HTML added to the header when it's collapsed and expanded, respectively  ["position", "html1", "html2"] (see docs)
                animatespeed: "fast", //speed of animation: integer in milliseconds (ie: 200), or keywords "fast", "normal", or "slow"
                oninit: function(expandedindices) { //custom code to run when headers have initalized
                    //do nothing
                },
                onopenclose: function(header, index, state, isuseractivated) { //custom code to run whenever a header is opened or closed
                    //do nothing
                }
            })

        <?php } ?>

        <?php if ($show_cigar_library_menu && $mobile_view) { //////////////////////////////// 
        ?>
            $('#clmenu').universalCigarLibraryMenu({
                menu_effect: 'open_close_fade',
                menu_speed_show: 1,
                menu_speed_hide: 1,
                menu_speed_delay: 1,
                menu_click_outside: true,
                menubar_trigger: false,
                menubar_hide: false,
                menu_responsive: true
            });
        <?php } ?>
        <?php if ($show_filter_menu) { //////////////////////////////// 
        ?>

            function prepareList() {

                $('.attibute-filter').find('li:has(ul)').click(function(event) {
                    if (this == event.target) {
                        $(this).toggleClass('expanded');
                        $(this).toggleClass('collapsed');
                        $(this).children('ul').toggle(1);
                    }
                });

                $('.pre-closed').addClass('collapsed').children('ul').hide();
                $('.pre-open').addClass('expanded');

            };

            /**
             * navigation.js RP - SEARCH FILTER DROP DOWN
             *
             * Handles toggling the navigation menu for small screens and enables tab
             * support for dropdown menus.
             */

            (function() {
                var container, button, menu, links, subMenus;

                container = document.getElementById('site-navigation');
                if (!container) {
                    return;
                }

                button = container.getElementsByTagName('button')[0];
                if ('undefined' === typeof button) {
                    return;
                }

                menu = container.getElementsByTagName('ul')[0];

                // Hide menu toggle button if menu is empty and return early.
                if ('undefined' === typeof menu) {
                    button.style.display = 'none';
                    return;
                }

                menu.setAttribute('aria-expanded', 'false');
                if (-1 === menu.className.indexOf('nav-menu')) {
                    menu.className += ' nav-menu';
                }

                button.onclick = function() {
                    if (-1 !== container.className.indexOf('open')) {
                        container.className = container.className.replace(' open', '');
                        button.setAttribute('aria-expanded', 'false');
                        menu.setAttribute('aria-expanded', 'false');
                        //document.getElementById('fm').value = '0';
                    } else {
                        container.className += ' open';
                        button.setAttribute('aria-expanded', 'true');
                        menu.setAttribute('aria-expanded', 'true');
                        //document.getElementById('fm').value = '1';
                    }
                };

                <?php if ($filter_menu_active) { ?>
                    container.className += ' open';
                    button.setAttribute('aria-expanded', 'true');
                    menu.setAttribute('aria-expanded', 'true');
                <?php } ?>


                // Get all the link elements within the menu.
                links = menu.getElementsByTagName('a');
                subMenus = menu.getElementsByTagName('ul');

                // Set menu items with submenus to aria-haspopup="true".
                for (var i = 0, len = subMenus.length; i < len; i++) {
                    subMenus[i].parentNode.setAttribute('aria-haspopup', 'true');
                }

                // Each time a menu link is focused or blurred, toggle focus.
                for (i = 0, len = links.length; i < len; i++) {
                    links[i].addEventListener('focus', toggleFocus, true);
                    links[i].addEventListener('blur', toggleFocus, true);
                }

                /**
                 * Sets or removes .focus class on an element.
                 */
                function toggleFocus() {
                    var self = this;

                    // Move up through the ancestors of the current link until we hit .nav-menu.
                    while (-1 === self.className.indexOf('nav-menu')) {

                        // On li elements toggle the class .focus.
                        if ('li' === self.tagName.toLowerCase()) {
                            if (-1 !== self.className.indexOf('focus')) {
                                self.className = self.className.replace(' focus', '');
                            } else {
                                self.className += ' focus';
                            }
                        }

                        self = self.parentElement;
                    }
                }
            })();

            // Another expander
            // you can override default options globally, so they apply to every .expander() call
            $.expander.defaults.slicePoint = 700;
            $.expander.defaults.expandText = 'Read More';
            $.expander.defaults.userCollapseText = 'Read Less';
            $.expander.defaults.moreClass = 'read-more2';
            $.expander.defaults.lessClass = 'read-less2';

            // simple example, using all default options unless overridden globally
            $('.expandable').expander();

            prepareList();



// Standard toggle
function setupToggle(triggerSelector, contentSelector, moreText = "Read More", lessText = "Read Less") {
    $(contentSelector).hide();

    $(triggerSelector).each(function() {
        const $trigger = $(this);
        $trigger.html(`<span class="toggle-text">${moreText}</span> <span class="material-icons toggle-icon">expand_more</span>`);

        $trigger.click(function() {
            const $content = $trigger.next(contentSelector);
            const $text = $trigger.find(".toggle-text");
            const $icon = $trigger.find(".toggle-icon");

            $content.slideToggle(300, function() {
                if ($content.is(":visible")) {
                    $text.text(lessText);
                    $icon.text("expand_less");
                } else {
                    $text.text(moreText);
                    $icon.text("expand_more");
                }
            });
        });
    });
}

// Scroll version
function setupScrollToggle(triggerSelector, contentSelector, offset = 100) {
    $(contentSelector).hide();

    $(triggerSelector).each(function() {
        const $trigger = $(this);

        $trigger.click(function() {
            const $content = $(contentSelector);

            $content.slideDown(300, function() {
                $('html, body').animate({
                    scrollTop: $content.offset().top - offset
                }, 500);
            });
        });
    });
}

// Usage
setupToggle(".read-more", ".read-more-description");
setupToggle(".vitola-more", ".vitola-more-content", "Show Vitola Details", "Hide Vitola Details");
setupScrollToggle(".scroll-read-more", ".scroll-read-more-description");

        <?php } ?>

        <?php if ($show_checkout_scripts) { //////////////////////////////// 
        ?>

            $('.checkout-toggle').click(function(e) {
                e.preventDefault();

                var $this = $(this);

                if ($this.next().hasClass('show')) {
                    $this.next().removeClass('show');
                    $this.next().slideUp(350);
                } else {
                    $this.parent().parent().find('li .inner').removeClass('show');
                    $this.parent().parent().find('li .inner').slideUp(350);
                    $this.next().toggleClass('show');
                    $this.next().slideToggle(350);
                }
            });

            $('input:checked').closest('.inner').css("display", "block");
            $('#cross-sell-box').delay(4000).slideDown('slow');
            $('#cross-sell-box').hover(function() {
                $(this).stop(true, true).slideDown();
            }, function() {
                // $(this).stop(true, true).slideUp();
            });

            $('.cross-sell-cross').click(function() {
                $('#cross-sell-box').hide();
            });
            <?php if ($show_dob_picker) { ?>
                $("#dob").datepicker({
                    dateFormat: "<?php echo JQUERY_DATEPICKER_FORMAT; ?>",
                    changeMonth: true,
                    changeYear: true,
                    yearRange: "-100:+0"
                });
            <?php } ?>

            <?php if ($loyalty_points_jquery_code) {
                echo $loyalty_points_jquery_code;
            } ?>


        <?php } ?>
        <?php if ($show_prevent_double) { //////////////////////////////// 
        ?>
            $('form[name=checkout_confirmation]').submit(function() {
                $('.continue-button').attr('disabled', 'disabled');
            });
        <?php } ?>
        <?php if ($show_cart_scripts) { ?>
            <?php echo $cart_jquery_update; ?>

            // used on shopping cart page
            // Hide the extra content initially, using JS so that if JS is disabled, no problemo:
            $('.read-more-content').addClass('hide')
            $('.read-more-show, .read-more-hide').removeClass('hide')

            // Set up the toggle effect:
            $('.read-more-show').on('click', function(e) {
                $(this).next('.read-more-content').removeClass('hide');
                $(this).addClass('hide');
                e.preventDefault();
            });

            // Changes contributed by @diego-rzg
            $('.read-more-hide').on('click', function(e) {
                var p = $(this).parent('.read-more-content');
                p.addClass('hide');
                p.prev('.read-more-show').removeClass('hide'); // Hide only the preceding "Read More"
                e.preventDefault();
            });

        <?php } ?>
        <?php if ($mobile_view) { ?>

            var loadCSSFiles = function() {
                var links = [<?php echo $defer_css_list; ?>],
                    headElement = document.getElementsByTagName("head")[0],
                    linkElement, i;
                for (i = 0; i < links.length; i++) {
                    linkElement = document.createElement("link");
                    linkElement.rel = "stylesheet";
                    linkElement.href = links[i];
                    headElement.appendChild(linkElement);
                }
            };
            var raf = requestAnimationFrame || mozRequestAnimationFrame || webkitRequestAnimationFrame || msRequestAnimationFrame;
            if (raf) {
                raf(loadCSSFiles);
            } else {
                window.addEventListener("load", loadCSSFiles);
            }

        <?php } ?>

    }); // defer end
</script>

<?php // } 
?>
<?php // ALL PAGES //////////////////////////////// 
?>
<script>
    // Check if running on IE10 or earlier and add a specific class to HTML
    if ( /*@cc_on!@*/ false) {
        document.documentElement.className += 'ie10';
    }

    // Attach click event listener to document
    document.addEventListener('click', function(e) {
        if (e.target.id !== 'suggestions' && e.target.id !== 'search_input' && !e.target.classList.contains('ajaxPageResults')) {
            const suggestions = document.getElementById('suggestions');
            if (suggestions) suggestions.style.display = 'none'; // Equivalent to fadeOut in jQuery
        }
    });

    // Function to serialize form data
    function serializeForm(form) {
        const formData = new FormData(form);
        return new URLSearchParams(formData).toString();
    }

    // Function to handle lookup and AJAX requests
    function lookup(inputString) {
        const suggestions = document.getElementById('suggestions');

        if (inputString.length === 0) {
            if (suggestions) suggestions.style.display = 'none'; // Equivalent to fadeOut in jQuery
        } else {
            let dataform;
            // Check the condition for serializing data
            const form = document.getElementById('ajax_search');
            if (form) {
                if ('<?php echo $search_keywords; ?>' === inputString) {
                    dataform = serializeForm(form);
                } else {
                    // For filtering inputs, manually construct data string
                    const filteredInputs = Array.from(form.elements).filter(el => !el.classList.contains('ignore_page'));
                    const filteredData = new FormData();
                    filteredInputs.forEach(input => filteredData.append(input.name, input.value));
                    dataform = new URLSearchParams(filteredData).toString();
                }
            }

            // Use fetch API instead of jQuery.ajax
            fetch('<?php echo HTTP_SERVER; ?>/ajax_search.php?' + dataform, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8',
                    },
                    cache: 'default',
                })
                .then(response => response.text())
                .then(data => {
                    if (suggestions) {
                        suggestions.style.display = 'block'; // Equivalent to fadeIn in jQuery
                        suggestions.innerHTML = data; // Populate suggestions box
                    }
                })
                .catch(error => console.error('Error fetching data:', error));
        }
    }

    // Debounce for the input keyup event
    let timer;
    const searchInput = document.getElementById('search_input');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            clearTimeout(timer);
            const ms = 600; // milliseconds
            const val = this.value;
            timer = setTimeout(function() {
                lookup(val);
            }, ms);
        });
    }

    // Function to handle page changes with AJAX
    function changepage(inputString) {
        const form = document.getElementById('ajax_search');
        if (form) {
            const dataform = serializeForm(form) + "&page=" + inputString;

            // Use fetch API instead of jQuery.ajax
            fetch('<?php echo HTTP_SERVER; ?>/ajax_search.php?' + dataform, {
                    method: 'GET',
                    cache: 'default',
                })
                .then(response => response.text())
                .then(data => {
                    const suggestions = document.getElementById('suggestions');
                    if (suggestions) suggestions.innerHTML = data; // Fill the suggestions box
                })
                .catch(error => console.error('Error fetching data:', error));
        }
    }
</script>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        if (window.innerWidth <= 768) {
            const headers = document.querySelectorAll(".toggle-header");

            headers.forEach(header => {
                header.innerHTML = header.innerHTML + ' <span class="toggle-sign">+</span>';

                header.addEventListener("click", function() {
                    const content = this.nextElementSibling;
                    const toggleSign = this.querySelector(".toggle-sign");

                    if (content.style.display === "none" || content.style.display === "") {
                        content.style.display = "block";
                        toggleSign.innerText = "-";
                    } else {
                        content.style.display = "none";
                        toggleSign.innerText = "+";
                    }
                });

                header.nextElementSibling.style.display = "none";
            });
        }
    });
</script>

<!-- <script>
document.addEventListener("DOMContentLoaded", function () {
    var header = document.getElementById("header");
    var advancedSearch = document.querySelector(".advanced-search-link a");
    var shippingInfo = document.querySelector(".header-shipping");
    var headerSocial = document.querySelector("#header-social");
    var cgarsNav = document.getElementById("cgars-nav");
    var logo = document.querySelector(".cgars-logo");

    function checkSticky() {
        var stickyPoint = header.getBoundingClientRect().top;

        if (stickyPoint <= 0) { 
            header.classList.add("sticky");
            if (advancedSearch) advancedSearch.textContent = ""; // Remove Advanced Search link
            if (shippingInfo) shippingInfo.style.display = "none"; // Hide Free UK Shipping text
            if (headerSocial) headerSocial.style.marginTop = "21px";
            if (cgarsNav) cgarsNav.style.top = "91px";
            if (logo) logo.style.maxWidth = "201px";
        } else {
            header.classList.remove("sticky");
            if (advancedSearch) advancedSearch.textContent = "Advanced Search"; // Show again when scrolling up
            if (shippingInfo) shippingInfo.style.display = "block"; // Show again when scrolling up
            if (headerSocial) headerSocial.style.marginTop = "0";
            if (cgarsNav) cgarsNav.style.top = "105px";
            if (logo) logo.style.maxWidth = "260px";
        }
    }

    window.addEventListener("scroll", checkSticky);
});
</script> -->
<?php

if (HTTPS_SERVER == 'https://www.cgarsltd.co.uk') {
    if (!$remove_footer) {
        include('design/content/js/zopim.php');
    }
}

?>
</body>

</html>