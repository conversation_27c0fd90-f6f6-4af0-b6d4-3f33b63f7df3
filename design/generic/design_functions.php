<?php

    $useragent = $_SERVER['HTTP_USER_AGENT'];

if(preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i',$useragent)||preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i',substr($useragent,0,4))) { $mobile_view = true; } else { $mobile_view = false; }

    if (isset($_GET['mobile'])) {
     if ($_GET['mobile'] == '1') { $mobile_view = true; }
     if ($_GET['mobile'] == '2') { $mobile_view = false; }
    }

    if (isset($_GET['print'])) {
     if ($_GET['print'] == '1') { $print_view = true; }
    }

	$unishop_grids = 3; // dont delete

    if ($_SERVER["SERVER_PORT"] == '443') { $ssl_switch = 'SSL'; } else { $ssl_switch = 'NONSSL'; }

	/* constants    */
	define (DC_FOLDER, 'design/generic/');
	define (DC_STYLES, DC_FOLDER . 'styles/');
	define (DC_SCRIPTS, DC_FOLDER . 'js/');
	define (DC_BLOCKS, DC_FOLDER . 'blocks/');
	define (DC_IMAGES, DC_FOLDER . 'images/');

    $main_page_url = false;
	$product_page_url = true;
    $other_page = false;
	$popup_url = false;
	$show_container = true;
	$subscription_shopping_list_active = true;

    // Set the current page in the session
    $current_page = basename($_SERVER['PHP_SELF']); // Use $_SERVER['PHP_SELF'] for better security
    $_SESSION['current_page'] = $current_page;

	if (!isset($_GET['cPath']) && $current_page == FILENAME_DEFAULT) { $main_page_url = true; }
	if (isset($_GET['cPath']) || $main_page_url == true) { $product_page_url = false; $show_container = false; }

// turn on widescreen
if ($current_page == '404.php') { $main_page_url = true; }
if ($current_page == FILENAME_ADDRESS_BOOK_PROCESS)      { $show_pca = true; }
if ($current_page == FILENAME_ADVANCED_SEARCH){ $product_page_url = false; $show_container = false; $show_nouislider = true; $show_select2 = true; $show_owl_carousel = true; }
if ($current_page == FILENAME_ADVANCED_SEARCH_RESULT)    { $show_cigar_library_menu = true; $product_page_url = false; $show_container = false; $show_nouislider = true; $show_select2 = true; $show_filter_menu = true; $show_owl_carousel = true; }
if ($current_page == FILENAME_ALLPRODS)       { $product_page_url = false; $show_container = false; }
if ($current_page == FILENAME_ARTICLES)       { $show_cigar_library_menu = true; }
if ($current_page == FILENAME_ARTICLE_INFO)   { $show_cigar_library_menu = true; $show_fotorama = true; }
if ($current_page == FILENAME_CHECKOUT_CONFIRMATION)     { $show_colorbox = true; $show_tool_tip = true; $show_flick_ui = true; $show_prod_info = true; $show_prevent_double = true; $show_delivery_countdown = true; $show_zopim = true; }
if ($current_page == FILENAME_CHECKOUT_PAYMENT)          { $show_colorbox = true; $show_checkout_scripts = true; $show_filter_menu = true; $show_delivery_countdown = true; $show_zopim = true; }
if ($current_page == FILENAME_CHECKOUT_PAYMENT_ADDRESS)  { $show_pca = true; }
if ($current_page == FILENAME_CHECKOUT_SHIPPING_ADDRESS) { $show_pca = true; }
if ($current_page == FILENAME_CONTACT_US)     { $product_page_url = false; $show_container = false; $show_customer_service = true; $show_zopim = true; }
if ($current_page == 'competitions.php')      { $show_colorbox = true; }
if ($current_page == FILENAME_CREATE_ACCOUNT) { $show_pca = true; }
if ($current_page == FILENAME_CREATE_ACCOUNT_SUCCESS)    { $show_colorbox = true; }
if ($current_page == 'customer_service.php')  { $product_page_url = false; $show_container = false; }
if ($current_page == FILENAME_DEFAULT)        { $show_owl_carousel = true; $product_page_url = false; $show_container = false; $show_delivery_countdown = true; if($main_page_url) {  } else { $show_nouislider = true; $show_select2 = true; $show_filter_menu = true; $enable_bread = true; }  }
if ($current_page == 'document.php')          { $product_page_url = false; $show_container = false; }
if ($current_page == FILENAME_FAVOURITES)     { $product_page_url = false; $show_container = false; }
if ($current_page == FILENAME_DYNAMIC_SITEMAP){ $product_page_url = false; $show_container = false; $show_customer_service = true; }
if ($current_page == 'info_customer_service.php')        { $show_colorbox = true; $show_html_extras = true; $show_customer_service = true; $show_fotorama = true; if($_GET['id'] == 'shipping.htm') { $show_ship_rates = true; }}
if ($current_page == 'info_cigar_library.php')           { $show_colorbox = true; $show_html_extras = true; $show_cigar_library_old = true; $show_fotorama = true; $show_countdown = true; if($_GET['id'] == 'shipping.htm') { $show_ship_rates = true; } if($_GET['id'] == 'publicity_and_press_releases_cgarsltd.html') { $show_ddaccordion = true; } }
if ($current_page == 'info_our_shops.php')    { $our_shops_page_url = true; $show_colorbox = true; $show_html_extras = true; $show_fotorama = true; }
if ($current_page == FILENAME_LOGIN)          { $show_container = false; }
if ($current_page == FILENAME_NEWS)           { $show_cigar_library_menu = true; }
if ($current_page == FILENAME_PREVIOUS_PRODUCTS)  { $product_page_url = false; $show_container = false; $show_cigar_library_menu = true; }
if ($current_page == FILENAME_PRODUCT_INFO)   { $show_colorbox = true; $show_tool_tip = true; $show_flick_ui = true; $show_prod_info = true; $show_owl_carousel = true; $product_page_url = false; $show_container = false; $show_nouislider = true; $show_delivery_countdown = true; $enable_bread = true; $show_zopim = true; $show_filter_menu = true; }
if ($current_page == FILENAME_PRODUCT_INFO_PREVIOUS)     { $show_colorbox = true; $show_tool_tip = true; $show_flick_ui = true; $show_prod_info = true; $product_page_url = false; $show_container = false; $show_cigar_library_menu = true; }
if ($current_page == FILENAME_PRODUCTS_NEW)   { $show_cigar_library_menu = true; $product_page_url = false; $show_container = false; $show_nouislider = true; $show_select2 = true; $show_filter_menu = true; $show_owl_carousel = true; }
if ($current_page == FILENAME_REVIEWS)        { $show_cigar_library_menu = true;  }
if ($current_page == FILENAME_SHOPPING_CART)  { $show_colorbox = true; $show_container = false; $show_owl_carousel = true; $show_cart_scripts = true; $enable_bread = true; $show_delivery_countdown = true; $show_subscription_scripts = true; }
if ($current_page == FILENAME_SOURCE_PRODUCT) { $show_tool_tip = true; $show_flick_ui = true; $show_prod_info = true; $popup_url = true; }
if ($current_page == FILENAME_SPECIALS) { $show_cigar_library_menu = true; $product_page_url = false; $show_container = false; $show_nouislider = true; $show_select2 = true; $show_filter_menu = true; $show_owl_carousel = true; }
if ($current_page == FILENAME_SUBSCRIPTIONS)  { $show_subscription_scripts = true; $show_subscription_list_scripts = true; $show_colorbox = true; }
if ($current_page == FILENAME_SUBSCRIPTIONS_SHOPPING_LIST)  { $show_subscription_renewal_scripts = true; $show_subscription_list_scripts = true; $show_subscription_scripts = true; $show_colorbox = true; }
if ($current_page == FILENAME_CHECKOUT_SUBSCRIPTIONS)       { $show_subscription_renewal_scripts = true; $show_subscription_list_scripts = true; $show_subscription_scripts = true; }
if ($current_page == FILENAME_TESTIMONIALS)   { $product_page_url = false; $show_container = false; $show_cigar_library_old = true; }
if ($current_page == FILENAME_WISHLIST)       { $show_colorbox = true; }
if ($current_page == FILENAME_CIGAR_NOMINATIONS)       { $show_cigar_nominations = true; }

?>
