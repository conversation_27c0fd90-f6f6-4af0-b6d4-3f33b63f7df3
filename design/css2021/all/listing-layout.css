.flex-me {
    display: flex
}

.four {
    float: left;
    width: 25%
}

.button-bottom-right {
    clear: both;
    float: right;
    padding: 15px 0 15px 15px
}

.button-bottom-left {
    clear: both;
    float: left;
    padding: 15px 15px 15px 0
}

.button-bottom-left a {
    margin-right: 10px
}

.button-bottom-right button[type="submit"] {
    margin: 0
}

.button-bottom-left button[type="submit"] {
    margin: 0
}

.shopping_cart {
    margin: 20px 0 0 0
}

.cart-item {
    float: left;
    width: 100%;
    background-color: #fafafa;
    color: #333;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px;
    margin-bottom: 10px
}

.cart-item-inner {
    padding: 10px
}

.cart-name {
    margin-left: 20px;
    margin-bottom: 5px
}

.cart-extra {
    margin-left: 20px
}

.cart-image {
    padding: 8px;
    background-color: #fff;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.cart-controls {
    margin-left: 20px
}

.cart-price {
    font-size: 26px;
    color: #096;
    font-weight: 700;
    margin-bottom: 5px
}

.cart-quantity {
    float: left;
    padding-top: 5px
}

.cart-update {
    float: left
}

.cart-remove {
    float: left;
    margin-top: 10px;
    clear: both
}

.cart-continue {
    margin-bottom: 10px
}

.cart-sub-text {
    text-align: right
}

.cart-sub-total {
    text-align: right;
    margin-bottom: 10px;
    font-size: 30px;
    color: #000;
    font-weight: bold
}

.cart-checkout {
    text-align: right;
    margin-top: 10px
}

.cart-delivery-message {
    text-align: right;
    margin-top: 10px
}

.cart-delivery-message a {
    text-decoration: underline;
    color: #096
}

.cart-delivery-message span {
    font-size: 12px
}

.cart-continue {
    text-align: right;
    margin-top: 10px
}

.cart_related_outside {
    border: 1px solid #e6e6e6;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    padding: 10px;
    margin: 10px 0
}

.cart_related_outside_inner {
    padding: 0 5px 5px 5px
}

.cart_related_inside {
    background-color: #FFF;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin: 0 5px 5px 5px
}

.cart_related_inside_inner {
    padding: 7px
}

.cart_related_outside h2 {
    padding: 10px 10px 8px 8px;
    margin: 0;
    color: #333
}

.cart_related-products-image {
    padding: 5px 0;
    text-align: center
}

.cart_related-products-text {
    text-align: left;
    margin-bottom: 7px
}

.cart_related-products-text a {
    color: #666;
    text-decoration: underline
}

.cart_related-products-text a:hover {
    color: #036;
    text-decoration: underline
}

.cart_related-products-more {
    float: left;
    text-align: left
}

.cart_related-products-price {
    color: #000;
    font-size: 16px;
    font-weight: bold;
    padding: 10px 0 5px 0
}

.pair_related_outside {
    background-color: #d8d2c2;
    margin: 10px 5px 20px 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.pair_related_outside_inner {
    padding: 0 5px 5px 5px
}

.pair_related_inside {
    background-color: #FFF;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin: 0 5px 5px 5px
}

.pair_related_inside_inner {
    padding: 7px
}

.pair_related_outside h2 {
    padding: 10px 10px 8px 8px;
    margin: 0;
    color: #fff
}

.pair_related-products-image {
    padding: 5px 0;
    text-align: center
}

.pair_related-products-text {
    text-align: left;
    margin-bottom: 7px
}

.pair_related-products-text a {
    color: #666;
    text-decoration: underline
}

.pair_related-products-text a:hover {
    color: #036;
    text-decoration: underline
}

.pair_related-products-more {
    float: left;
    text-align: left
}

.pair_related-products-price {
    color: #000;
    font-size: 16px;
    font-weight: bold;
    padding: 10px 0 5px 0
}

.checkout-red {
    padding: 15px;
    color: #900
}

.checkout-delivery-text {
    float: left;
    margin: 0 0 20px 0
}

.checkout-delivery-radio {
    float: right
}

.checkout-delivery-price {
    float: right
}

.order-confirm-image {
    padding: 10px 10px 0 0
}

.order-confirm-name {
    padding: 10px 10px 0 0
}

.checkout-confirmation-order-total {
    float: right;
    margin-top: 10px
}

.checkout-confirmation-product-total {
    float: right;
    margin-top: 10px
}

.checkout-confirmation-confirm-order {
    float: right;
    margin: 10px 0
}

.checkout-payment-message {
    margin-bottom: 10px
}

.checkout-payment-message2 {
    padding: 15px;
    margin-bottom: 10px
}

.content-wrapper {
    background-color: #FFF;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.content-wrapper2 {
    background-color: #faf9f4;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.content-wrapper-alt {
    background-color: #faf9f4;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.content-wrapper-checkout {
    background-color: #FFF;
    padding: 15px 15px 0 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin-bottom: 10px
}

.content-wrapper-checkout-alt {
    background-color: #faf9f4;
    padding: 15px 15px 0 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin-bottom: 10px
}

.content-wrapper h2 {
    margin-top: 8px
}

.content-wrapper ul {
    margin: 0
}

.content-wrapper-id {
    background-color: #FFF;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin-bottom: 10px
}

.content-wrapper-id-alt {
    background-color: #faf9f4;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin-bottom: 10px
}

.login-icon {
    float: left;
    line-height: 25px;
    margin-right: 5px;
    color: #936
}

.login-span {
    line-height: 25px
}

.cutoffmessage-payment {
    margin-bottom: 10px
}

.cutoffmessage-payment span {
    color: #c00;
    font-weight: 700
}

.reviews_text {
    margin: 10px 0
}

.reviews_price {
    float: left;
    font-size: 2em;
    font-weight: bold;
    color: #c30
}

.cat-desc-cuban {
    padding: 0
}

.cat-desc-footer {
    padding: 20px
}

.messagestack-output {
    background-color: #096;
    padding: 10px;
    color: #fff;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.messagestack-error {
    margin-top: 10px;
    background-color: #900;
    padding: 10px;
    color: #fff;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.messageStackError,
.messageStackWarning {
    background-color: #900
}

.messageStackError,
.messageStackWarning,
.messageStackSuccess {
    padding: 10px;
    margin: 10px 0;
    color: #FFF !important;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.fast-buy-padding {
    padding: 10px 0
}

.re-order-buttons {
    float: left
}

.order-store-address {
    margin-top: 10px
}

.cigar-library-page h1 {
    margin-bottom: 20px
}

.pagination-box {
    float: left;
    width: -webkit-calc(100% - 10px);
    width: -moz-calc(100% - 10px);
    width: calc(100% - 10px);
    margin: 0 5px 10px 5px;
    background-color: #f9f8f2
}

.pagination-box span {
    float: left;
    display: block;
    padding: 15px
}

.new_price {
    float: left;
    display: block;
    clear: both;
    font-weight: 700;
    font-size: 24px;
    color: #333
}

.listing-review-stars {
    float: left;
    display: inline;
    margin-bottom: 10px
}

.listing-review-number {
    float: left;
    display: inline;
    line-height: 24px;
    padding-left: 10px
}

.product-listing-row {
    float: left;
    width: 100%;
    background-color: #fff
}

.product-listing-row .print-container {
    float: left;
    width: -webkit-calc(100% - 100px);
    width: -moz-calc(100% - 100px);
    width: calc(100% - 100px)
}

.product-listing-row .product-image {
    float: left;
    width: 100px
}

.product-listing-row .listing-review-stars,
.product-listing-row .listing-review-number {
    float: left;
    width: 100%
}

.product-listing-row .product-name a {
    float: left;
    text-decoration: none;
    padding: 10px 10px 0 10px
}

.product-listing-row .new_price {
    clear: none;
    padding-left: 10px
}

.product-listing-container {
    margin: 15px 0 12px 0;
    padding: 10px 5px 0 5px;
    background-color: #fff;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.product-listing-box {
    float: left;
    padding: 10px;
    box-sizing: border-box;
    width: -webkit-calc(100% - 10px);
    width: -moz-calc(100% - 10px);
    width: calc(100% - 10px);
    margin: 0 5px 10px 5px;
    background-color: #FFF
}

.product-image {
    float: left;
    position: relative;
    width: 100%;
    background-color: #fff;
    padding: 10px 0 10px 0;
    text-align: center;
    border-bottom: 3px solid #EEE;
}

.product-image img {
    margin-bottom: 10px;
    margin: 0 auto;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;    
}

.prod-list-free-container {
    position: absolute; bottom: 20px; right: 0px;
}
.prod-list-free-ship {
    font-size: 12px; line-height: 18px;color:#FFF;background-color: #3C5775;padding: 5px 8px;border-radius: 2px;margin-right: 6px;
}
.prod-list-free-pouch {
    font-size: 12px; line-height: 18px;color:#FFF;background-color: #333;padding: 5px 8px;border-radius: 2px;margin-right: 6px;
}

.product-name a {
    float: left;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
    color: #3C5775;
    line-height: 25px;
    text-decoration: none
}

.product-date {
    margin-bottom: 10px
}

.product-attribute {
    margin-bottom: 10px
}

.product-buy-now {
    clear: both;
    float: left;
    margin-top: 10px
}

.discount-coupon input {
    width: 180px
}

.listing-heading {
    display: none;
    background-color: #faf9f4;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.loyalty-faqs a {
    color: #099
}

.loyalty-faqs br {
    height: 10px
}

.loyalty-faqs li {
    padding: 5px 5px 0 0
}

.loyalty-faqs ol {
    padding: 0;
    margin: 0
}

.point-note {
    font-weight: 700
}

.pointFaq {
    margin-top: 10px;
    background-color: #f8f8f9;
    display: none;
    position: relative;
    padding: 15px;
    margin: 2px;
    text-align: justify
}

@media screen and (min-width:48em) {
    .pagination-box span.viewall {
        float: right
    }
}

.default_page .et-content-bg,
.product_info_page .et-content-bg {
    text-align: left;
    padding-top: 20px;
    padding-bottom: 40px
}

.main_part_wrapper {
    float: right !important
}

.product_info_page_central {
    margin-top: 22px
}

.subcategory_name_wrapper {
    padding-bottom: 20px;
    overflow: hidden
}

.category_image,
.subcategory_image {
    margin-bottom: 20px
}

.nav-container #menu_block_head {
    display: none;
    font-family: arial
}

.nav-container .menu_block_dropdown {
    display: block
}

.img_link_wrapper {
    float: left
}

#scroll-box {
    float: left;
    position: relative;
    left: 0;
    top: 0;
    z-index: 99999;
    cursor: pointer
}

#drop-box,
.drop-box-subcat,
.drop-box-subsubcat,
.drop-box-3subcat,
.drop-box-4subcat {
    position: absolute;
    left: 0;
    z-index: 100;
    display: none;
    text-align: left;
    padding: 0;
    top: 57px;
    border: 0;
    width: auto;
    background: #fbfbfb;
    padding: 20px 15px 20px 15px;
    -moz-box-shadow: 0 10px 15px 2px #727272;
    -webkit-box-shadow: 0 10px 15px 2px #727272;
    box-shadow: 0 10px 15px 2px #727272
}

.drop-box-subsubcat,
.drop-box-3subcat,
.drop-box-4subcat {
    left: 150px;
    top: -7px
}

.drop-box-4subcat {
    top: 5px
}

#drop-box a,
#drop-box a:hover,
.drop-box-subcat a,
.drop-box-subcat a:hover {
    border: 0;
    font-family: arial;
    font-weight: normal;
    display: block;
    min-width: 11em;
    padding-left: 10px;
    padding-right: 10px;
    color: #2f2f2f !important;
    background: none !important;
    line-height: 16px;
    padding: 6px 10px 5px
}

#drop-box a.first,
#drop-box a.first:hover,
.drop-box-subcat a.first,
.drop-box-subcat a.first:hover {
    background: 0
}

#drop-box a:hover,
.drop-box-subcat a:hover {
    text-decoration: underline
}

#drop-box {
    padding-bottom: 7px
}

.drop-box-subsubcat {
    padding-top: 10px;
    top: -12px
}

.cat-name,
.sub-cat-name,
.subsub-cat-name {
    display: block;
    float: left;
    position: relative;
    left: 0;
    top: 0;
    z-index: 9
}

.cat-name a.main_category {
    line-height: 20px;
    color: #d0a779;
    font-size: 18px;
    padding: 0;
    padding-top: 19px;
    padding-bottom: 18px
}

.cat-name a.main_category .main_category_border {
    border-left: 1px solid #636363;
    padding-left: 18px;
    padding-right: 18px
}

.cat-name a.main_category .item-1 {
    border-left: none !important
}

.infoBox,
.infoBoxCategory {
    margin: 0 0 13px
}

.infoBoxHeading {
    background: #592249;
    color: #fff;
    padding: 10px;
    15px;
    font-size: 18px;
    margin: 0 0 12px;
    font-weight: 700
}

.infoBoxHeading span {
    float: right
}

.infoBoxHeading span a {
    text-decoration: none;
    color: #CCC
}

.infoBoxHeading span a:hover {
    color: #FFF
}

.infoBoxHeading i {
    float: left;
    margin-right: 7px
}

.infoBoxHeading i a {
    text-decoration: none;
    color: #c3a066
}

.infoBoxContents {
    padding: 15px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px;
    background-color: #faf9f4
}

.infoBoxHeading,
.infoBoxContents,
.product-heading,
.product-content,
.rcorner3 {
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.infoBoxCategory {
    background: #fff;
    padding: 10px;
    border: 1px solid #cdcbc4;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.infoBoxCategory ul {
    list-style: none
}

.infoBoxCategory li {
    display: block;
    background: url(/design/generic/images/cat-icon.png) no-repeat 3px center;
    padding-top: 3px;
    padding-right: 0;
    padding-bottom: 0;
    padding-left: 22px
}

.infoBoxCategory li+li {
    border-top: 1px dotted #bfbdb7
}

.infoBoxCategory li a {
    color: #35230f;
    text-decoration: none
}

.infoBoxCategory li a:hover {
    color: #8f311a
}

.infoBoxNotifications img,
.infoBoxReviews .NoReview img {
    display: none
}

.infoBox .infoBoxContents ul {
    margin: 0;
    padding: 0;
    list-style: none
}

.infoBox .infoBoxContents ul li {
    padding: 4px 0 4px 0
}

.infoBox .infoBoxContents ul li a {
    text-transform: uppercase;
    font-family: arial
}

.infoBoxSearch input[type="text"],
.infoBoxSearch input[type="image"] {
    float: left
}

.infoBoxSearch input[type="image"] {
    margin-top: 2px
}

.infoBoxSearch a {
    text-decoration: underline
}

.infoBox_list {
    list-style: none;
    margin: 8px 0 0;
    padding: 0;
    text-align: left
}

.infoBox_list li {
    list-style: none;
    margin: 0;
    padding: 3px 0 0 0;
    text-transform: uppercase;
    line-height: 20px
}

.infoBox_list li a {
    line-height: 14px
}

ol li {
    line-height: 24px
}

ol li a,
ol li a:hover {
    font-weight: normal
}

ol {
    margin: 0;
    padding: 0 0 2px 17px
}

#shopping_cart_mini SPAN.newItemInCart {
    color: #fff
}

.options_form select {
    padding-right: 1px;
    padding-bottom: 1px
}

h2.review_author {
    padding-top: 5px
}

.review_author a {
    text-decoration: underline
}

.review_author a:hover {
    text-decoration: none
}

.review_reply {
    margin-top: 20px;
    padding-top: 10px;
    border-top-width: 1px;
    border-top-style: solid;
    border-top-color: #FFF
}

.also_pursh_slider {
    padding-top: 30px;
    padding-bottom: 10px
}

#also_purchased {
    clear: both
}

h1.also_pursh_title {
    clear: both;
    margin-top: 15px;
    font-size: 22px;
    font-weight: normal
}

.padding_bottom_1 {
    padding-bottom: 13px
}

.padding_right_1 {
    padding-right: 30px
}

.padding_top_1 {
    padding-top: 42px
}

.padding_top_2 {
    padding-top: 15px
}

.padding_sc_1 {
    margin-top: 15px
}

.padding_sc_3 {
    margin-top: 17px;
    margin-left: 10px
}

.cart-remove-button {
    background-image: url(/design/generic/images/shopping_cart_mini_delete_button.png);
    display: block;
    width: 14px;
    height: 14px
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
    .padding_sc_1 {
        margin-top: 13px
    }
}

.space_1 {
    line-height: 18px
}

.space_2 {
    line-height: 6px
}

.space_3 {
    line-height: 1px
}

.space_4 {
    line-height: 13px
}

.padding0 {
    padding: 0;
    margin: 0
}

.padding_pages_2 {
    padding: 0 0 0 5px
}

.form_1 {
    position: absolute;
    top: 30px;
    right: 0
}

.form_1 SELECT {
    padding-right: 1px;
    width: 160px
}

.img_1 {
    padding-right: 21px
}

.vertical {
    vertical-align: middle
}

INPUT {
    margin-right: 8px
}

.contentText .fieldValue input {
    margin-right: 0
}

.productsNotifications {
    color: #fff
}

.ui-widget-header1,
.ui-widget-header {
    font-weight: bold
}

.ui-widget-header {
    background: 0
}

.et_other_style h1 {
    margin: 0 0 0 2px
}

.orderEdit {
    text-decoration: underline
}

.myaccount {
    margin-bottom: 30px
}

.myaccount h2 {
    background-color: #333;
    color: #fff;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px;
    margin-bottom: 5px;
    font-size: 17px;
    padding: 10px;
    padding-left: 15px;
    font-weight: 400;
}
.myaccount ul {
    padding: 15px;
}
.myaccount li {
    line-height: 30px;
}
.gift-voucher-message {
    float: left;
    margin-bottom: 10px;
    padding: 20px;
    background-color: #ececec;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.highlight {
    color: #c9b67c
}

.active_cat a {
    text-decoration: underline
}

.infoBoxCategory li a.purple {
    color: #939;
    font-weight: 700
}

.listing-top-cats {
    padding: 0 20px;
    margin-bottom: 15px
}

.listing-top-cats ul {
    float: left;
    width: 100%;
    -webkit-column-count: 1;
    -moz-column-count: 1;
    column-count: 1
}

.listing-top-cats li {
    float: left
}

.listing-top-cats li a {
    float: left;
    color: #666;
    margin-right: 3px
}

.listing-top-cats li a.purple {
    color: #fc0;
    font-weight: 400
}

.listing-top-cats li a.white {
    color: #FFF;
    font-weight: 400
}

.listing-top-cats-image {
    padding: 10px;
    text-align: center
}

.listing-cat-outer {
    background-color: #fff;
    padding: 0;
    margin: 20px 20px 0 0;
    position: relative;
    max-width: 800px
}

.listing-cat-label {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0 auto;
    max-width: 100%;
    text-align: center
}

.listing-cat-label-inner {
    margin: 0 auto;
    font-weight: 700;
    font-size: 21px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.5)
}

.listing-cat-label a {
    text-decoration: none;
    color: #FFF;
    padding: 0
}

.listing-top-cats .cat_child_a {
    padding: 4px 0 4px 0
}

.listing-top-cats .cat_child_a a {
    float: left;
    width: 100%;
    display: block;
    color: #fff;
    padding: 5px 0;
    text-align: left
}

.listing-top-cats .cat_child_b {
    float: left;
    width: 100%;
    display: block
}

.listing-top-cats .cat_child_b a {
    color: #fff;
    text-align: left;
    line-height: 30px
}

.listing-top-cats .cat_child_c a {
    float: left;
    width: 100%;
    display: block;
    color: #666;
    padding: 5px 0;
    text-align: center
}

.listing-top-cats .cat_child_d {
    float: left;
    width: 100%;
    display: block
}

.listing-top-cats .cat_child_d a {
    color: #fff;
    text-align: left;
    line-height: 30px
}

.addit_box {
    width: 10px;
    height: 250px
}

.font_normal {
    font-weight: normal
}

.space_slider {
    line-height: 5px
}

.span_1 {
    display: block;
    padding-top: 3px;
    float: left
}

.span_2 {
    display: block;
    float: right
}

.span_3 {
    line-height: 8px;
    display: block;
    height: 8px;
    clear: both
}

.margin_bottom_1 {
    margin-bottom: 27px
}

.shoppingcart_img {
    width: 100px;
    height: auto
}

.ui-icon {
    background-image: none;
    width: 5px;
    height: 5px;
    margin-top: 7px
}

.contentText p a,
.contentText p a:hover,
.contact_link a,
.contact_link a:hover,
.padding_sc_3 a,
.padding_sc_3 a:hover {
    text-decoration: underline;
    font-weight: bold
}

.contentText p a:hover,
.contact_link a:hover,
.padding_sc_3 a:hover {
    text-decoration: none
}

.buttonSet {
    padding-top: 10px
}

.et_pager {
    clear: both
}

A.pageResults,
A.pageResults:hover {
    text-decoration: underline
}

.pageResults u {
    text-decoration: none
}

A.pageResults:hover {
    background: 0;
    text-decoration: none
}

.fbError {
    color: #c00;
    font-weight: bold
}

.facebookbutton {
    float: right
}

.ui-widget {
    margin-left: 10px
}

.login_page_line {
    overflow: hidden;
    margin-bottom: 10px
}

.et_box_reviews_default .et_box_cont_new table.ui-widget-content {
    width: 100%
}

.et_box_reviews_default .et_box_cont_new table.ui-widget-content td a img {
    display: none
}

.shopping_cart .img_cart,
.phone p.img_phone,
.icon_bestsellers,
.icon_facebook,
.icon_twitter,
.icon_theme,
.icon_contact,
.icon_facebook,
.icon_twitter,
.icon_footer_theme,
.icon_contact,
.icon_brands,
.icon_box_category,
.icon_box_manufacturers,
.icon_box_new,
.icon_box_special,
.icon_related {
    display: block;
    background-repeat: no-repeat;
    width: 31px;
    height: 31px;
    float: left
}

.icon_box_category,
.icon_box_manufacturers,
.icon_box_new,
.icon_box_special,
.icon_related {
    margin-right: 12px
}

.icon_box_all {
    background-image: url(/design/generic/images/icon-category.png)
}

.icon_box_category {
    background-image: url(/design/generic/images/icon-category.png)
}

.icon_box_manufacturers {
    background-image: url(/design/generic/images/icon-manufacturers.png)
}

.icon_box_new {
    background-image: url(/design/generic/images/icon-new.png)
}

.icon_box_special {
    background-image: url(/design/generic/images/icon-special.png)
}

.icon_related {
    background-image: url(/design/generic/images/icon-related.png)
}

.currency a:hover,
.currency a.selected,
.language a:hover,
.language a.selected {
    border-bottom: 2px solid #e45235
}

#drop-box,
.drop-box-subcat,
.drop-box-subsubcat,
.drop-box-3subcat,
.drop-box-4subcat {
    border-top-width: 3px;
    border-top-style: solid;
    border-top-color: #89301a
}

.productsNotifications {
    background-color: #343434
}

.also_pursh_slider,
.carousel_bestsellers_outer {
    background-color: #f8f8f8
}

.ui-widget-content1,
.ui-widget-header1,
.ui-widget-content2,
.ui-widget-contentAdm,
.ui-widget-content,
.ui-widget-header {
    border: 1px solid #333
}

.ui-widget-header1,
.ui-icon {
    background-color: #333
}

.ui-widget-contentAdm,
.ui-widget-content,
.ui-dialog .ui-icon {
    background-color: #fff
}

.es-nav span.es-nav-next {
    right: 10px
}

.es-nav span {
    right: 46px
}

#carousel_brands .es-nav span.es-nav-next {
    right: 0
}

#carousel_brands .es-nav span {
    right: 35px
}

.moduleRow {
    padding: 10px;
    background-color: #EEE;
    margin-top: 5px
}

.moduleRowOver {
    padding: 10px;
    background-color: #343434;
    color: #fff;
    cursor: pointer;
    cursor: hand;
    margin-top: 5px
}

.moduleRowSelected {
    padding: 10px;
    background-color: #343434;
    color: #fff;
    margin-top: 5px
}

.moduleshipRow {
    padding: 10px;
    background-color: #EEE;
    border-bottom: 1px solid #fff
}

.moduleshipRowOver {
    padding: 10px;
    background-color: #343434;
    color: #fff;
    cursor: pointer;
    cursor: hand;
    border-bottom: 1px solid #fff
}

.moduleRowship_selected {
    padding: 10px;
    background-color: #343434;
    color: #fff;
    border-bottom: 1px solid #fff
}

SPAN.pointWarning {
    color: #900
}

.review_img {
    background-color: #FFF;
    text-align: center;
    border: 4px solid #ccc
}

.contConteiner_listing {
    margin-bottom: 40px
}

.cart-locker-total {
    color: #096;
    font-weight: 700;
    float: right;
    font-size: 26px;
    padding: 2px 0 10px 0
}

.payment-sub {
    font-weight: bold
}

.subcategory_name img {
    width: 100%;
    height: auto
}

a.button {
    background-color: #592049;
    background-image: url(/design/generic/images/cg-logo-small.png);
    background-position: center bottom -3px;
    background-repeat: no-repeat;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 75px;
    padding-top: 18px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    color: white;
    font-size: 22px;
    line-height: 31px;
    text-decoration: none;
    vertical-align: middle;
    text-align: center;
    text-decoration: none;
    display: block;
    height: 80px
}

a.button:hover {
    color: #ffc;
    background-color: #511841;
    background-image: url(/design/generic/images/cg-logo-small.png);
    background-position: center bottom -3px;
    background-repeat: no-repeat
}

a.button:active {
    background-color: #311228;
    background-image: url(/design/generic/images/cg-logo-small.png);
    background-position: center bottom -3px;
    background-repeat: no-repeat
}

.subcategory_name p {
    display: block
}

.currency-select {
    float: left;
    width: 100%;
    text-align: right;
    color: #592249
}

.currency-select form {
    float: right
}

.currency-select span {
    float: right;
    margin-right: 10px;
    text-transform: none
}

.currency-select select {
    float: right;
    margin-top: 10px
}

.listing-view {
    margin-left: 15px
}

.listing-view a {
    text-decoration: underline;
    color: #069
}

.testimonial-bubble {
    border: 1px solid #DDD;
    padding-top: 10px;
    padding-bottom: 10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
    -khtml-border-radius: 10px
}

.contact-us-page-text {
    background-color: #ededed;
    padding: 25px
}

.contact-us-page-number {
    font-size: 28px;
    text-align: center;
    padding-top: 5px;
    padding-right: 25px;
    padding-bottom: 25px;
    padding-left: 25px
}

.image-border {
    padding: 5px;
    border: 1px solid #CCC
}

.or {
    float: left;
    width: 20px;
    padding-top: 9px;
    padding-right: 0;
    padding-bottom: 9px;
    padding-left: 2px
}

.cart-locker-warning {
    float: left;
    color: #c00;
    line-height: 14px;
    height: 14px;
    display: block;
    padding: 17px
}

.address_book_option_text_selected {
    color: #093;
    margin: 5px 0
}

.address_book_option_text {
    margin: 0 0 10px 0
}

.address_book_option_text a {
    color: #069
}

.address_book_option_warning {
    float: right;
    text-align: right;
    color: #900
}

.site-map-tree {
    padding: 25px
}

.site-map-tree h2 {
    color: #936
}

.site-map-tree ul {
    overflow: auto
}

.parent-one {
    font-size: 20px;
    margin-bottom: 20px;
    margin-top: 10px
}

.parent-one li {
    margin-left: 20px
}

.parent-one a {
    color: #333;
    padding-bottom: 3px
}

.parent-two {
    padding-top: 5px;
    font-size: 18px;
    margin-bottom: 20px
}

.parent-two li {
    margin-left: 0
}

.parent-two a {
    color: #936;
    padding: 0;
    padding-left: 25px;
    background-image: url(/design/generic/images/cg-bullet.png);
    background-repeat: no-repeat;
    background-position: left center
}

.parent-three {
    margin-bottom: 10px
}

.parent-three li {
    margin-left: 23px
}

.parent-three a {
    color: #666;
    background-image: url(/design/generic/images/leaf-bullet.gif);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 25px
}

.parent-four a {
    color: #093;
    background: 0
}

.parent-four li {
    margin-left: 10px
}

.parent-five a {
    color: #093;
    background: 0
}

.parent-five li {
    margin-left: 0
}

.news-month {
    float: right
}

.news-title {
    float: left;
    font-weight: bold
}

.cat-desc-best {
    display: none
}

.read-more, .scroll-read-more, .vitola-more {
    color: #093;
    cursor: pointer;
    position: relative;
    padding-top: 3px;
    padding-bottom: 3px;
    text-decoration: underline
}
.scroll-read-more-description {
    padding: 0 10px 40px 20px;
}

#cross-sell-box {
    display: none;
    width: 375px;
    height: 151px;
    text-align: left;
    color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100
}

#cross-sell-box .cross-sell-container {
    width: 375px;
    height: 151px;
    background-image: url(/design/generic/images/Mitchell-Recommends.png);
    background-repeat: no-repeat;
    background-position: right bottom
}

#cross-sell-box .cross-sell-container-lighter {
    width: 375px;
    height: 151px;
    background-image: url(/design/generic/images/Lighter-Warning.png);
    background-repeat: no-repeat;
    background-position: right bottom
}

#cross-sell-box .cross-sell-text {
    padding-top: 5px;
    padding-left: 15px;
    width: 235px;
    color: #fff
}

#cross-sell-box .cross-sell-text a {
    color: #fff
}

#cross-sell-box .cross-sell-text p {
    margin-top: 0
}

#cross-sell-box .cross-sell-text h3 {
    font-size: 18px;
    font-weight: normal;
    color: #ff9;
    height: 25px;
    margin-top: 40px
}

#cross-sell-box .cross-sell-image {
    float: right;
    width: 80px
}

#cross-sell-box .cross-sell-cross {
    float: right;
    width: 32px;
    height: 30px;
    margin-top: 113px;
    margin-right: 5px;
    text-align: right;
    cursor: pointer
}

#cross-sell-box .cross-sell-image-thumb {
    float: left;
    width: 60px;
    height: 60px;
    margin-top: 10px;
    margin-left: 10px
}

.SagePay {
    float: left;
    width: 100%;
    margin-top: 5px;
    margin-bottom: 10px
}

.SagePay-Logo {
    float: left;
    padding: 10px
}

.SagePay-Text {
    float: left;
    padding: 10px
}

.SagePay-Text strong {
    color: #00856a
}

.SagePay-Logo img {
    margin-top: 10px;
    max-width: 100%
}

.SagePay-Sub {
    float: left;
    margin-top: 5px;
    margin-left: 10px
}

.SagePay-Sub span {
    color: #093
}

.SagePay-Sub div {
    margin-top: 5px
}

.option-heading {
    background-color: #f1ede0;
    padding: 2px;
    font-weight: bold
}

.option-row-one {
    background-color: #fbf9f5
}

.option-row-two {
    background-color: #f1ede0
}

.pre-order {
    color: #096;
    font-weight: 700
}

.cart-availability {
    color: #c30
}

.cart-availability a {
    text-decoration: underline;
    color: #c30
}

.dual-left h3,
.dual-right h3 {
    color: #fff;
    background-color: #592249;
    float: left;
    line-height: 50px;
    text-indent: 15px;
    width: 100%
}

.dual-left h3 a,
.dual-right h3 a {
    color: #fff
}

.cat-desc-cuban {
    float: left;
    width: 100%;
    margin-bottom: 15px
}

.cat-desc-cuban img {
    margin-bottom: 10px
}

.cat-desc {
    padding: 20px 40px 20px 20px;
    margin: 0
}

.toggle-icon {
    vertical-align: middle;
}

.davidoff-flex {
    display: flex !important;
    align-items: center
}

.davidoff-cat-desc {
    float: left;
    width: -webkit-calc(100% - 10px);
    width: -moz-calc(100% - 10px);
    width: calc(100% - 10px);
    margin: 16px 5px 10px 5px;
    background-color: #fff;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.davidoff-cat-desc h1 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    padding-left: 5px
}

.davidoff-cat-desc-inner {
    padding: 10px
}

.davidoff-cat-desc-text {
    padding: 0 5px
}

.featured-cuban-list img {
    float: right;
    margin-left: 10px
}

.featured-cuban-outer {
    padding: 0
}

.featured-cuban-list {
    float: left;
    width: 100%;
    background-color: #fff;
    margin: 0 0 10px 0
}

.featured-cuban-inner {
    padding: 10px
}

.featured-cuban-name a {
    color: #592249
}

.featured-cuban-more {
    margin-top: 5px
}

.featured-cuban-more a {
    color: #093;
    text-decoration: underline
}

.featured-cuban-price {
    color: #900;
    font-size: 20px;
    margin-top: 5px
}

.featured-cuban-list img {
    float: right
}

.favourite-text-inner i {
    float: left;
    margin-right: 5px;
    line-height: 30px
}

.favourite-text-inner strong {
    margin: 10px 2px 0 0;
    line-height: 30px
}

.add-to-fav-off {
    border: 0;
    font-size: 1em;
    cursor: pointer;
    color: #222;
    background-color: #fff;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin: 0;
    line-height: 30px;
    padding: 0
}

.add-to-fav-off i {
    color: #c00
}

.add-to-fav-on {
    border: 0;
    font-size: 1em;
    cursor: pointer;
    color: #CCC;
    background-color: #fff;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin: 0;
    line-height: 30px;
    padding: 0
}

#add-to-fav:active,
#add-to-fav:focus,
#add-to-fav.active {
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.rec-fav-box {
    float: left;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 10px;
    margin-left: 0;
    border: 1px solid #c3a066;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.rec-fav-box li {
    padding-top: 4px;
    padding-left: 8px;
    padding-right: 8px;
    padding-bottom: 4px
}

.rec-fav-box li a {
    color: #333
}

.rec-fav-box-even {
    background-color: #ece6d6
}

.rec-fav-title {
    font-size: 17px;
    color: #FFF;
    background-color: #c3a066
}

.product_reviews_date,
.product_reviews_pager {
    margin-top: 20px
}

.reviews_author a {
    text-transform: uppercase;
    font-weight: bold;
    text-decoration: underline
}

.reviews_author a:hover {
    text-decoration: none
}

.reviews_date {
    display: block;
    margin-bottom: 5px
}

.review_img_wrapper {
    float: left;
    width: 300px;
    text-align: center
}

.reviews_sort {
    float: right;
    line-height: 30px
}

.reviews-a-z {
    float: right;
    margin-bottom: 13px
}

.fieldKey {
    font-weight: normal
}

.product_reviews_write_page .contentText table {
    width: 100%
}

.reviews_page .reviews_wrapper {
    margin-bottom: 20px
}

.reviews-box,
.options_form_label,
.options_form {
    margin-bottom: 20px
}

.product-listing-box .now_price {
    float: left;
    display: block;
    width: 100%;
    font-size: 15px;
    font-weight: 700;
    color: #333;
    line-height: 30px
}

.product-listing-box .was_price {
    float: left;
    display: block;
    width: 100%;
    font-weight: 400;
    font-size: 15px;
    line-height: 22px
}

.product-listing-box .saving_price {
    float: left;
    display: block;
    width: 100%;
    font-weight: 400;
    font-size: 15px;
    line-height: 22px
}

.product-listing-box .now_price strong {
    float: right;
    font-size: 19px;
    color: #c30
}

.product-listing-box .was_price strong {
    float: right;
    text-decoration: line-through;
    font-weight: 400
}

.product-listing-box .saving_price strong {
    float: right;
    font-weight: 400
}

.attibute-filter ul,
li {
    list-style: none;
    cursor: pointer
}

.search-filter-box li {
    float: left;
    width: 100%;
    clear: both
}

.attibute-filter .collapsed {
    background-image: url(/design/generic/styles/images/collapsed.png);
    background-position: right 5px top 10px;
    background-repeat: no-repeat
}

.attibute-filter .expanded {
    background-image: url(/design/generic/styles/images/expanded.png);
    background-position: right 5px top 10px;
    background-repeat: no-repeat
}

.attibute-filter {
    padding: 0 10px 10px 10px
}

.attibute-filter ul {
    margin: 0;
    padding: 0
}

.attibute-filter ul li {
    margin: 0;
    padding: 0
}

.menu-toggle {
    display: none
}

#site-navigation {
    display: table-cell;
    vertical-align: middle;
    width: 100%;
    float: left
}

#site-navigation p {
    display: none
}

#site-navigation h2 {
    font-weight: 400;
    color: #fff;
    background: #333;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px;
    margin-bottom: 10px;
    padding-top: 10px;
    padding-right: 13px;
    padding-bottom: 10px;
    padding-left: 13px;
    font-size: 1.2em;
    clear: both
}

.search-filter-box {
    float: left;
    width: 100%;
    padding: 0 0 10px 0;
    margin: 0 0 10px 0;
    background: #fff;
    border: 1px solid #eee;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.search-filter-box label {
    float: left;
    width: 100%;
    clear: both;
    line-height: 30px;
    cursor: pointer;
    color: #333;
    padding: 0;
    display: block
}

.search-filter-box label .checkbox_check {
    width: 13px;
    line-height: 30px;
    padding: 0;
    margin: 0 5px 0 0
}

.search-filter-box span {
    color: #939
}

.search-filter-box .attribute-name {
    float: left;
    color: #592249;
    padding-top: 10px;
    padding-bottom: 10px;
    border-top: 1px solid #EEE
}

.search-filter-box .attribute-name-top {
    float: left;
    color: #592249;
    padding-top: 10px;
    padding-bottom: 10px
}

.search-filter-box .attribute-name ul {
    margin-top: 10px
}

.search-filter-box .attribute-name-top ul {
    margin-top: 10px
}

@media(max-width:767px) {
    #site-navigation {
        display: block;
        float: none;
        text-align: left;
        width: 100%
    }
    #site-navigation p {
        display: block;
        padding: 5px;
        padding-bottom: 0;
        margin-top: 0;
        margin-right: 0;
        margin-bottom: 10px;
        margin-left: 0;
        background: #ece6d6;
        -moz-border-radius: 3px;
        -webkit-border-radius: 3px;
        border-radius: 3px;
        -khtml-border-radius: 3px
    }
    #site-navigation p span {
        display: inline-block;
        padding: 5px 7px 5px 7px;
        background-color: #fffcf2;
        color: #666;
        -moz-border-radius: 3px;
        -webkit-border-radius: 3px;
        border-radius: 3px;
        -khtml-border-radius: 3px;
        margin-bottom: 5px;
        margin-right: 5px
    }
    .menu-toggle {
        display: block;
        position: relative;
        width: 100%;
        font-weight: 400;
        color: #fff;
        background: #592249;
        background-image: url(/design/generic/styles/images/toggle.png);
        background-position: right 7px top 10px;
        background-repeat: no-repeat;
        border: 0;
        -moz-border-radius: 3px;
        -webkit-border-radius: 3px;
        border-radius: 3px;
        -khtml-border-radius: 3px;
        margin-top: 10px;
        margin-bottom: 10px;
        padding-top: 10px;
        padding-right: 15px;
        padding-bottom: 10px;
        padding-left: 10px;
        font-size: 1.2em;
        text-align: left;
        cursor: pointer
    }
    .open .menu-toggle {
        background-image: url(/design/generic/styles/images/button_up.png);
        background-position: right 3px top 3px;
        background-repeat: no-repeat
    }
    #site-navigation>div {
        display: none
    }
    #site-navigation.open>div {
        display: block
    }
}

.news-text {
    margin-top: 10px;
    padding: 0 10px
}

.news-image {
    text-align: center;
    width: 100%
}

.news-image img {
    margin-bottom: 10px
}

.museum-info {
    padding: 0 0 0 20px
}

#museum-search {
    float: left;
    height: 38px;
    background-color: #592249;
    margin-bottom: 10px
}

#museum-search input {
    border: 0;
    background: 0;
    margin: 0;
    padding: 0;
    color: #fff;
    padding: 4px 5px 4px 12px;
    height: 30px;
    padding-left: 12px;
    width: 210px;
    margin-left: -4px;
    float: right
}

#museum-search .submit_button {
    float: right;
    padding: 0;
    margin: 0;
    border: 0;
    cursor: pointer;
    padding-top: 3px;
    padding-right: 3px
}

#museum-search .submit_button input {
    border: 0;
    display: block;
    margin: 0;
    cursor: pointer;
    width: 35px;
    height: 32px;
    background-image: url(/design/generic/styles/images/search-submit-button.png);
    background-repeat: no-repeat;
    background-position: center center
}

.long-banner {
    float: left;
    margin-top: 5px;
    margin-bottom: 10px;
    background-image: url(/design/generic/styles/images/Ramon-Allones-long-banner.jpg);
    background-repeat: no-repeat;
    background-position: right top;
    width: 100%;
    height: 131px
}

.long-banner span {
    display: block;
    float: right;
    width: 490px;
    font-size: 30px;
    color: #f6d375;
    margin-top: 66px
}

.long-banner p {
    display: none
}

.long-banner strong {
    display: block;
    float: right;
    width: 115px;
    margin-right: 25px;
    margin-top: 70px;
    height: 21px;
    font-size: 16px;
    color: #FFF;
    padding: 10px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.long-banner strong a {
    color: #fff
}

.long-banner strong a:hover {
    color: #CCC;
    text-decoration: none
}

.cart-reminder {
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #e6e6e6;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.cart-reminder p {
    border-bottom: 1px solid black;
    padding: 20px
}

.cart-reminder .hide {
    display: none
}

.cart-reminder strong {
    display: block;
    padding: 10px 0 0 0
}

.cart-reminder h3 {
    display: block;
    margin: 0;
    color: #592249
}

.cart-reminder h4 {
    color: #096
}

.cart-reminder .read-more-show,
.cart-reminder .read-more-hide {
    font-weight: 400;
    text-decoration: none;
    display: block;
    padding: 5px 0;
    color: #333
}

.wishlist-label {
    position: absolute;
    top: 0;
    right: 0
}

.subcategory_name {
    padding: 0 0 10px 0
}

.pagination-box {
    float: left;
    width: -webkit-calc(100% - 10px);
    width: -moz-calc(100% - 10px);
    width: calc(100% - 10px);
    margin: 0 5px 10px 5px;
    background: #fafafa;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -khtml-border-radius: 3px
}

.Pay-Outer {
    border: 1px solid #EEE;
    padding: 10px
}

.freeze-image {
    float: left;
    width: 50%
}

.freeze-price {
    padding-top: 15px;
    line-height: 25px;
    float: left;
    width: 50%
}

.freeze-price a {
    color: #222
}

.freeze-price strong {
    color: #228163
}

.freeze-name {
    padding-right: 10px
}

.freeze-name a {
    color: #936;
    line-height: 25px
}

.image-free-delivery {
    position: absolute;
    top: 15px;
    right: 25px;
}

@media(min-width:35.5em) {
    .subcategory_name {
        padding: 0 5px 10px 5px
    }
    .featured-cuban-outer {
        padding: 0 5px
    }
    #museum-search {
        float: right
    }
    .re-order-buttons {
        float: right
    }
    .order-delivery-address-padding {
        padding-right: 10px
    }
    .order-store-address {
        text-align: right;
        margin-top: 0
    }
    .listing-top-cats ul {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2
    }
}

@media screen and (min-width:48em) {
    .featured-cuban-outer {
        padding: 0
    }
    .content-wrapper-id {
        margin-right: 10px
    }
}

@media(min-width:64em) {
    .featured-cuban-outer {
        padding: 0 5px
    }
    .checkout-delivery-text {
        float: left;
        margin: 0 10px 20px 0
    }
    .shopping_cart {
        margin: 20px 10px 0 0
    }
    .fast-buy-padding {
        padding: 0 0 10px 10px
    }
    .point-note {
        display: none
    }
    .listing-heading {
        display: block
    }
    .listing-top-cats ul {
        -webkit-column-count: 3;
        -moz-column-count: 3;
        column-count: 3
    }
    .content-wrapper-checkout {
        margin-right: 10px
    }
    .content-wrapper-checkout-alt {
        margin-right: 10px
    }
    .news-text {
        margin-top: 20px;
        padding: 0 20px
    }
}

@media(min-width:80em) {
    .listing-top-cats ul {
        -webkit-column-count: 4;
        -moz-column-count: 4;
        column-count: 4
    }
}