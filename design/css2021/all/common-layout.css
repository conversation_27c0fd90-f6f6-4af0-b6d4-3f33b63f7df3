html,
body {
    font-size: 1em
}

h1 {
    padding: 8px 10px 0 0;
    margin: 5px 0 10px 0
}

h2 {
    padding: 8px 10px 0 0;
    color: #333
}

h3 {
    margin: 10px 0;
    color: #3C5775
}

h4 {
    font-size: 18px;
    margin: 10px 0
}

a {
    color: #3C5775
}

#inner-wrapper,
.inner-wrapper {
    max-width: 1760px !important;
    margin: 0 auto;
    clear: both
}

.inner-wrapper-pad {
    padding: 0
}

#full-wrapper {
    max-width: 1760px;
    margin: 0 auto;
    clear: both
}

.content-bg {
    background: #fff
}

.material-text {
    float: left;
    margin-bottom: 5px;
    line-height: 25px
}

.material-text i {
    float: left;
    display: inline-block;
    margin-right: 5px;
    color: #093
}

.material-icons i {
    font-style: normal
}

.material-icons .star-on {
    color: #fc0
}

.material-icons .star-off {
    color: #DDD
}

.review_rating {
    float: left
}

.thin-banner {
    background-color: #222;
    height: 36px;
    z-index: 0;
    color: #FFF;
    width: 100%;
  }
  
  .container-news {
    margin: 0 auto;
    height: 30px;
    position: relative; 
    width: 350px;
  }
  
  .container-news img {
    display: none;
    height: 23px;
  }
  
  .thin-banner-left {
    margin-top: 7px;
    margin-right: 100px;
  }
  
  .thin-banner-right {
    margin-top: 7px;
    margin-left: 250px;
  }
  
  .container-news a {
    color:white;
    text-decoration:none;
    position:absolute;
    left: 0px;
    padding:9px;
    width: 326px;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
    color: #EEE;
  }
  
  .on {
    opacity: 1;
    transition: opacity 2s ease-in;
    z-index: 2;
  }
  
  .off {
    opacity: 0;
    transition: opacity 2s ease-out;
    z-index: 1;
  }

.cookieWarning {
    background-color: #900;
    color: #fff;
    padding: 8px 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin: 0 0 10px 0
}

.cookieWarning p {
    margin: 7px 0
}

.cookieWarning span {
    color: #fc0
}

.cookieWarning a {
    color: #ccc
}

.no_bullets .blink a {
    font-weight: 700
}

.no_bullets .blink-color a {
    color: #c00
}

.gold-bg {
    background-color: #c3a066
}

.sand-bg {
    background-color: #ece6d6
}

.sand-dark-bg {
    background-color: #f6f3ea
}

.cream-bg {
    background-color: #f9f8f2
}

.white-bg {
    background-color: #fff
}

.off-white-bg {
    background-color: #fafafa
}

.flash {
    -moz-animation: flash 2s ease-out;
    -moz-animation-iteration-count: 2;
    -webkit-animation: flash 2s ease-out;
    -webkit-animation-iteration-count: 2;
    -ms-animation: flash 2s ease-out;
    -ms-animation-iteration-count: 2
}

@keyframes flash {
    0% {
        color: #fff
    }
    50% {
        color: #c00
    }
    100% {
        color: #fff
    }
}

@-webkit-keyframes flash {
    0% {
        color: #fff
    }
    50% {
        color: #c00
    }
    100% {
        color: #fff
    }
}

@-moz-keyframes flash {
    0% {
        color: #fff
    }
    50% {
        color: #c00
    }
    100% {
        color: #fff
    }
}

@-ms-keyframes flash {
    0% {
        color: #fff
    }
    50% {
        color: #c00
    }
    100% {
        color: #fff
    }
}

.breadcrumbs_box {
    float: left;
    color: #666;
    padding: 14px 0 0 12px;
    list-style: none;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
}

.breadcrumbs_box li {
    display: flex;
    align-items: center;
    line-height: 22px;
    margin: 0 5px 0 0;
}

.breadcrumbs_box a {
    text-decoration: none;
    color: #666;
}

.breadcrumbs_box a:hover {
    text-decoration: none;
    color: #3C5775;
}

.breadcrumbs_box li + li::before {
    font-family: 'Material Icons';
    content: '\e5cc'; /* Unicode for 'chevron_right' */
    font-size: 18px;
    margin: 0 5px;
    color: #666;
}

.link-button {
    padding: 0;
    line-height: 24px;
    background: 0;
    color: #666;
    text-decoration: underline
}

.link-button:hover {
    background: 0
}

.pure-button {
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    -khtml-border-radius: 4px
}

.pure-button div {
    display: inline-block;
    margin: 0 auto;
    padding-top: 5px
}

.pure-button i {
    float: left;
    display: inline-block;
    margin: 2px 10px 0 0
}

.pure-button span {
    float: left;
    display: inline-block
}

.add-to-cart-button {
    width: 100%;
    color: #fff;
    font-size: 1.5em;
    font-weight: 700;
    background-color: #3C5775;
    margin-bottom: 10px;
    letter-spacing: normal
}

.subscription-button {
    float: left;
    color: #fff;
    font-size: 1em;
    background-color: #333;
    width: 100%;
    padding: 8px 10px 3px 5px
}

.subscription-button div {
    display: inline-block;
    margin: 0 auto;
    padding-top: 2px
}

.subscription-button i {
    float: left;
    display: inline-block;
    margin: 2px 10px 0 0;
    font-size: 23px
}

.subscription-button span {
    float: left;
    display: inline-block;
    line-height: 30px
}

.cart-subscription {
    padding-top: 10px;
    margin-top: 15px;
    border-top: 4px #e6e6e6 solid
}

.subscription-cart-button {
    float: right;
    font-size: 18px;
    line-height: 25px
}

.subscription-cart-button button {
    float: left;
    padding: 2px 5px;
    margin: 0 0 0 3px;
    width: 25px;
    font-weight: 700
}

.subscription-cart-button button:hover {
    float: left;
    padding: 2px 5px;
    margin: 0 0 0 3px;
    width: 25px;
    font-weight: 700
}

.subscription-cart-button a {
    float: left;
    margin: 0 2px 0 2px
}

.subscription-cart-button i {
    float: left;
    display: inline-block;
    margin: 1px 0 0 0
}

.subscription-cart-button div {
    float: left;
    margin: 0 5px 0 0
}

.subscription-cart-button span {
    float: left;
    display: inline-block;
    min-width: 25px;
    background-color: #3C5775;
    color: #fff;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    text-align: center;
    margin: 0 0 0 3px
}

.fast-buy-button {
    float: right;
    color: #096;
    font-size: 1.1em;
    background-color: #f9f9f9;
    margin-bottom: 5px;
    padding: .5em .5em
}

.wish-list-button {
    float: left;
    color: #fff;
    font-size: 1em;
    background-color: #666;
    width: 100%;
    padding: .7em .5em
}

.add-review-button {
    float: left;
    color: #fff;
    font-size: 1em;
    background-color: #c3a066;
    width: 100%;
    padding: .7em .5em
}

.log-in-button {
    color: #FFF;
    background-color: #3C5775
}

.checkout-button {
    color: #FFF !important;
    background-color: #3C5775
}

.view-cart-button {
    color: #FFF !important;
    background-color: #666
}

.cart-checkout-button {
    width: 100%;
    color: #fff;
    font-size: 1.5em;
    font-weight: 700;
    background-color: #3C5775;
    margin-bottom: 10px
}

.out-of-stock-button {
    color: #FFF;
    background-color: #069;
    width: 100%
}

.pre-order-button {
    color: #FFF;
    background-color: #066;
    width: 100%
}

.request-price-button {
    color: #FFF;
    background-color: #333;
    width: 100%
}

.sold-out-button {
    color: #FFF;
    background-color: #900;
    width: 100%
}

.share-wishlist-button {
    float: right;
    color: #FFF;
    background-color: #333
}

.upload-button {
    margin-top: 15px
}

.notify-button-off {
    color: #FFF;
    background-color: #069;
    width: 100%;
    text-align: center
}

.notify-button-on {
    color: #FFF;
    background-color: #096;
    width: 100%;
    text-align: center
}

.continue-button {
    color: #FFF;
    background-color: #3C5775;
    width: 100%;
    margin: 20px 0;
    line-height: 24px
}

.continue-button i {
    float: right;
    margin: 0
}

.tag-out-of-stock {
    color: #fff;
    background-color: #069;
    text-transform: uppercase;
    font-size: 11px;
    display: block;
    float: left;
    padding-top: 2px;
    padding-right: 4px;
    padding-bottom: 2px;
    padding-left: 4px;
    margin-top: 4px;
    margin-left: 0;
    clear: both
}

.tag-limited {
    color: #fff;
    background-color: #f93;
    text-transform: uppercase;
    font-size: 11px;
    display: block;
    float: left;
    padding-top: 2px;
    padding-right: 4px;
    padding-bottom: 2px;
    padding-left: 4px;
    margin-top: 4px;
    margin-left: 0;
    clear: both
}

.tag-sold-out {
    color: #fff;
    background-color: #900;
    text-transform: uppercase;
    font-size: 11px;
    display: block;
    float: left;
    padding-top: 2px;
    padding-right: 4px;
    padding-bottom: 2px;
    padding-left: 4px;
    margin-top: 4px;
    margin-left: 0;
    clear: both
}

.cart-round {
    background-color: #333;
    padding: 2px 9px;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
    -khtml-border-radius: 20px
}

.cart-round-mobile {
    background-color: #d2af75;
    padding: 2px 9px;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
    -khtml-border-radius: 20px
}

.cart_separator {
    float: left;
    width: 100%;
    background-color: #f4f4f4;
    padding: 2px 0;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin: 10px 0
}

.pagination-top {
    float: left;
    width: 100%
}

.pagination-bottom {
    float: left;
    width: 100%;
    margin-top: 20px
}

.pagination-pages {
    float: left;
    margin-top: 10px
}

.review-author {
    margin: 20px 0 10px 0
}

.review-text {
    margin-bottom: 10px
}

.review-stars {
    margin-bottom: 20px
}

.review-write textarea {
    width: 96%;
    padding: 2%;
    margin-bottom: 20px
}

.review-note {
    margin-bottom: 10px
}

.review-rating {
    margin-bottom: 20px
}

.box-padding-left {
    padding-left: 10px
}

.box-padding-right {
    padding-right: 10px
}

.box-padding-both {
    padding-left: 10px;
    padding-right: 10px
}

.box-padding-half {
    padding-left: 5px;
    padding-right: 5px
}

.left-pad-5 {
    padding-left: 5px
}

.right-pad-5 {
    padding-right: 5px
}

.cigar-library-page {
    margin: 0;
    padding: 15px 10px 40px 10px;
    background-color: #fff
}

.cigar-library-page h1 {
    padding-left: 0
}

.cigar-library-page table {
    background-color: #fff
}

.share-media-left {
    float: left
}

.share-media-right {
    margin-left: 15px;
    float: left;
    width: 75px;
    height: 36px
}

.share-media-container {
    margin: 20px auto 0 auto;
    width: 290px;
    height: 36px;
    overflow: hidden
}

.article-search {
    background-color: #ece6d6;
    padding: 10px
}

#article-search {
    width: -webkit-calc(100% - 60px);
    width: -moz-calc(100% - 60px);
    width: calc(100% - 60px);
    height: 48px
}

#article-search input {
    border: 0;
    background-color: #fff;
    margin: 0;
    padding: 0;
    height: 48px;
    line-height: 48px;
    text-indent: 20px;
    width: 100%;
    font-size: 1.3ems
}

.article_submit_button {
    float: right;
    padding: 0;
    margin: 0;
    border: 0;
    cursor: pointer
}

.article_submit_button input {
    border: 0;
    display: block;
    margin: 0;
    cursor: pointer;
    width: 48px;
    height: 48px;
    background-image: url(/design/generic/images/search-submit-button.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-color: #c6c0b0
}

.article_submit_button input:hover {
    background-color: #e4dece
}

#article-search {
    position: relative
}

.checkout-warning {
    background-color: #093;
    color: #fff;
    text-align: center;
    padding: 10px
}

.customer-service-page {
    margin: 0;
    padding: 20px 0 40px 0;
    background-color: #fff
}

.customer-service-page h1 {
    padding-left: 0
}

.my_account_dropdown {
    background-color: #faf9f4
}

.my_account_dropdown_open {
    width: 300px;
    background-color: #faf9f4
}

.my_account_dropdown_inner {
    padding: 20px;
    cursor: default
}

.shopping_cart_dropdown {
    width: 500px;
    background-color: #faf9f4
}

.shopping_cart_dropdown_inner {
    padding: 20px;
    cursor: default
}

.checkout-comments textarea {
    width: 100%
}

.manager_special_price {
    margin-bottom: 10px
}

.manager_online_price strong {
    color: #096
}

.manager_reg_price {
    color: #666
}

.manager_reg_price strong {
    text-decoration: line-through
}

.document2 .content_text_box .ship-country img {
    margin: 0;
    padding: 0
}

.document2 .content_text_box .ship-close img {
    margin: 0;
    padding: 0
}

#load-shipping {
    margin-top: 5px;
    margin-bottom: 5px;
    border: 1px solid #CCC;
    padding: 15px
}

.ship-container {
    display: inline-block;
    width: 150px
}

.ship-country {
    cursor: pointer;
    padding: 5px
}

.ship-table {
    display: none;
    float: left;
    position: absolute;
    left: 33px;
    z-index: 10;
    background-color: #e6e5df;
    padding: 10px;
    width: 906px
}

.ship-grid {
    display: inline-block;
    border: 1px solid #999;
    width: 140px;
    padding: 5px;
    margin-left: -1px;
    margin-bottom: -1px;
    background-color: #fefbf2
}

.ship-grid strong {
    display: inline-block;
    float: right;
    color: #900;
    font-weight: 300
}

.ship-close {
    float: right;
    height: 25px;
    width: 25px;
    cursor: pointer
}

.country-selected {
    background-color: #e6e5df
}

#cboxOverlay {
    margin-bottom: 50px
}

#landing-flag {
    height: 50px;
    width: 100%;
    text-align: center;
    color: #fff;
    background-color: #282828;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 9999999999
}

.christmas-shipping {
    border: 10px solid #c00;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f9f9f9
}

.christmas-shipping h1 {
    color: #093;
    font-weight: 700
}

.christmas-shipping h2 {
    font-weight: 700
}

.christmas-shipping-image {
    float: right;
    padding: 0
}

.christmas-shipping-image img {
    margin: 0
}

.meet-team-text {
    padding-left: 20px;
    margin-bottom: 20px
}

#faqs h2 {
    margin: 0
}

#faqs h5 {
    margin-bottom: 0
}

.text-padding-none {
    text-align: justify
}

.text-padding-left {
    text-align: justify;
    margin-top: 20px
}

.text-padding-right {
    text-align: justify;
    margin-bottom: 20px
}

.text-padding-both {
    text-align: justify;
    margin-bottom: 20px
}

.menu-mini {
    margin-top: 10px
}

.menu-mini-top {
    margin-top: 40px
}

.new_in {
    margin-top: 20px
}

.new_in a {
    color: #096 !important
}

.category-advert {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert img {
    display: none
}

.category-advert-cigars {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert-cigars img {
    display: none
}

.category-advert-alcohol {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert-alcohol img {
    display: none
}

.category-advert-humidors {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert-humidors img {
    display: none
}

.category-advert-accessories {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert-accessories img {
    display: none
}

.category-advert-gifts {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert-gifts img {
    display: none
}

.category-advert-pipes-tobacco {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert-pipes-tobacco img {
    display: none
}

.category-advert-best-of {
    position: absolute;
    right: 0;
    bottom: 0
}

.category-advert-best-of img {
    display: none
}

.no_bullets a b {
    color: #c00
}

#snowflakeContainer {
    position: absolute;
    left: 0;
    top: 0
}

.snowflake {
    padding-left: 15px;
    font-family: Cambria, Georgia, serif;
    font-size: 14px;
    line-height: 24px;
    position: fixed;
    color: #fff;
    user-select: none;
    z-index: 5
}

.snowflake:hover {
    cursor: default
}

.checkout-accordian ul {
    list-style: none;
    padding: 0
}

.checkout-accordian .inner {
    overflow: hidden;
    display: none
}

.checkout-accordian li {
    margin: .5em 0
}

.checkout-accordian a.toggle {
    width: 100%;
    display: block;
    background: rgb(0 0 0 / .78);
    color: #fefefe;
    padding: .75em;
    border-radius: .15em;
    transition: background .3s ease
}

.checkout-accordian a:hover {
    background: rgb(0 0 0 / .9)
}

.tooltip {
    display: none;
    position: absolute;
    background-color: #3C5775;
    border-radius: 5px;
    padding: 10px;
    color: #fff
}

#cart-summary .checkout-button {
    display: none
}

#cart-summary .view-cart-button {
    display: none
}

#checkout-gift-voucher h2 {
    font-size: 18px;
    margin-top: 0
}

.not-found-heading {
    text-align: center;
    margin-top: 30px;
    font-size: 38px
}

.not-found-text {
    float: left;
    margin-top: 10px;
    width: 100%;
    text-align: center;
    font-size: 18px
}

.not-found-humidor {
    float: left;
    margin-top: 50px;
    width: 100%;
    text-align: center
}

.not-found-humidor img {
    width: 300px
}

.not-found-search {
    float: left;
    margin-top: 50px;
    width: 100%;
    text-align: center
}

.not-found-buttons {
    float: left;
    margin-top: 50px;
    width: 100%;
    text-align: center
}

.not-found-search-container {
    margin: 0 10px
}

.not-found-search {
    float: left;
    width: 100%;
    padding: 10px 0 0 0;
    background-color: #EEE;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin-top: 30px
}

.not-found-search-heading {
    padding-top: 50px;
    font-family: 'Roboto', FreeSans, Arimo, "Droid Sans", Helvetica, Arial, sans-serif;
    font-weight: 700;
    font-size: 36px;
    text-align: center;
    color: #666
}

.not-found-buttons {
    padding: 0 40px 40px 40px
}

.not-found-buttons a,
.not-found-buttons a:hover {
    color: #666;
    font-family: 'Roboto', FreeSans, Arimo, "Droid Sans", Helvetica, Arial, sans-serif;
    font-size: 20px;
    margin: 10px 0;
    padding: 20px;
    font-weight: 400;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
    -khtml-border-radius: 10px;
    text-decoration: none;
    width: -webkit-calc(100% - 120px);
    width: -moz-calc(100% - 120px);
    width: calc(100% - 120px);
    clear: both;
    float: left
}

.not-found-buttons a {
    background-color: #EEE
}

.not-found-buttons a:hover {
    background-color: #EAEAEA
}

.not-found-advanced {
    float: left;
    width: 100%;
    padding: 30px 0 0 0;
    text-align: center;
    color: #666;
    font-size: 20px
}

.checkout-delivery-van {
    display: none
}

.checkout-delivery-van img {
    width: 100%
}

.id-notice {
    background-color: #faf9f4;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin-top: 5px
}

.id-notice a {
    text-decoration: none
}

.id-notice i {
    float: left;
    margin-right: 7px;
    font-weight: 700
}

.login-notice {
    background-color: #faf9f4;
    padding: 15px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    margin-top: 5px
}

.flexslider {
    padding: 10px 10px 0 10px;
    background: #FFF
}

.sub-banner-bg {
    background: #ece6d6;
    color: #444
}

.sub-banner-inner {
    padding: 0 10px
}

.sub-banner-pad {
    float: left;
    width: 100%;
    padding: 7px 0;
    line-height: 18px
}

.sub-banner-pad a {
    color: #444
}

.sub-banner-pad strong {
    font-size: .8em;
    display: inline-block;
    float: left;
    padding: 0 0 0 5px
}

.sub-banner-pad i {
    font-size: 1.4em;
    display: inline-block;
    float: left;
    padding: 3px 5px
}

.sub-banner-pad span {
    font-size: 1em;
    display: inline-block;
    float: left;
    padding: 5px 0
}

.sub-banner-pad .sub-text {
    font-size: .7em;
    display: inline-block;
    float: left;
    padding: 1px 0 0 10px
}

.davidoff-text {
    display: inline-block;
    float: left;
    padding: 3px 0;
    line-height: 18px;
    color: #444;
    letter-spacing: normal;
    width: -webkit-calc(100% - 120px);
    width: -moz-calc(100% - 120px);
    width: calc(100% - 120px)
}

.davidoff-text .heading-text {
    display: inline-block;
    float: left;
    padding: 3px 0 0 5px;
    font-weight: 700;
    text-transform: capitalize
}

.davidoff-text .lower-text {
    display: inline-block;
    float: left;
    padding: 0 0 0 5px;
    font-size: 13px;
    clear: both
}

.sub-banner-2,
.sub-banner-3,
.sub-banner-4 {
    display: none
}

.mgmenu_container_davidoff .sub-banner-1 {
    padding: 7px 0 0 0
}

.sub-banner-home {
    color: #444
}

.davidoff-logo-small {
    float: left;
    width: 120px;
    height: 50px
}

.davidoff-logo-small img {
    height: 100%
}

.lcdh-logo-small {
    width: 60px;
    margin-top: 2px
}

.lcdh-logo-small img {
    margin-left: 10px;
    width: 40px
}

#dl-menu-logo a {
    padding: 12px 8px 0 12px !important;
    display: none
}

#dl-menu-logo img {
    height: 20px
}

#mobile-trustpilot .trustpilot-widget {
    background: #222;
    height: 24px;
    padding: 5px 0 0 0
}

#head-trustpilot .trustpilot-widget {
    padding-top: 10px;
    height: 30px;
    display: none
}

#foot-trustpilot {
    position: fixed;
    left: 10px;
    bottom: 0;
    width: 430px;
    background-color: #222;
    color: #FFF;
    height: 30px;
    text-align: right;
    -moz-border-radius-topleft: 5px;
    -moz-border-radius-topright: 5px;
    -moz-border-radius-bottomright: 0;
    -moz-border-radius-bottomleft: 0;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
    -webkit-border-bottom-right-radius: 0;
    -webkit-border-bottom-left-radius: 0
}

#foot-trustpilot .trustpilot-widget {
    margin: 4px 10px 0 0
}

#home-trustpilot .trustpilot-widget {
    margin: 0 0 20px 0
}

#BreadPromo i {
    float: left;
    padding: 7px 7px 0 0;
    color: #c00
}

#BreadPromo strong {
    float: left;
    color: #c00
}

#BreadPromo a {
    color: #c00
}

#deliveryCountdown span {
    color: #333
}

.deliveryCountdownTwoAll {
    display: none;
    text-align: right;
    line-height: 40px;
    color: #666;
    font-size: 1.2em;
    color: #333
}

.deliveryCountdownTwoAll i {
    float: right;
    padding: 7px 5px 0 0;
    color: #3C5775
}

.deliveryCountdownTwoAll div {
    float: right;
    margin-right: 10px
}

.deliveryCountdownTwoAll strong {
    color: #c00
}

.deliveryCountdownTwoAll .material-reset {
    float: right;
    margin: 0;
    padding-top: 10px
}

.breadcrumbs_box {
    text-transform: capitalize
}

.remove-at-zero-0 {
    display: none
}

.deliveryCountdownSubtext {
    font-size: 13px;
    line-height: 25px;
    color: #666
}

.deliveryCountdownContainer {
    float: left;
    width: -webkit-calc(100% - 30px);
    width: -moz-calc(100% - 30px);
    width: calc(100% - 30px);
    background-color: #f6f3ea;
    padding: 15px;
    margin: 10px 0 0 0;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.clsearch {
    margin: 0 20px 0 20px;
    padding: 0 20px 20px 20px;
    background-color: #fff;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px
}

.cltext {
    float: left;
    padding: 2px 15px 0 5px;
    line-height: 25px
}

.clsearch-input {
    padding: 0 5px;
    line-height: 25px;
    background-color: #FFF;
    border: 1px #CCC solid
}

.clsearch-submit {
    padding: 0 5px;
    line-height: 25px;
    background-color: #3C5775;
    border: 0;
    color: #FFF;
    border: 1px #3C5775 solid
}

.video-responsive {
    overflow: hidden;
    padding-bottom: 56.25%;
    position: relative;
    height: 0
}

.video-responsive iframe {
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    position: absolute;
    margin: 10px 0
}

.bannerContainer {
    position: relative;
    margin: 25px 10px 0 10px
}

.bannerContainer img {
    width: 100%
}

.btnBannerBuyNow {
    color: #fff;
    font-size: 1em;
    background-color: #099;
    padding: 10px 25px;
    position: absolute;
    right: 15px;
    bottom: 20px;
    text-decoration: none
}

.contactSection {
    background-size: cover;
    background-repeat: no-repeat;
    padding: 100px 0;
    background-position: center
}

.contactSection .contactInnerContainer {
    max-width: 1500px;
    margin: 0 auto;
    width: 100%;
    background: rgb(0 0 0 / .5);
    padding: 50px 30px;
    box-sizing: border-box
}

.contactSection .contactInnerContainer .content-wrapper2 {
    background: #fff0;
    border-radius: 0;
    padding: 0
}

.contactSection .headingBox {
    text-align: center
}

.contactSection .headingBox h1 {
    color: #fff;
    margin: 0 0 30px;
    padding: 0
}

.contactSection .contact-us-page-number {
    font-size: 20px;
    line-height: 20px;
    margin: 0;
    padding: 30px 0;
    color: #fff
}

.contactInfoRow {
    width: 100%;
    color: #fff
}

.contactInfoRow .saprateRow {
    margin: 0 0 10px
}

.contactInfoRow .saprateRow a {
    color: #fff;
    margin: 0
}

.contactInfoRow .saprateRow a:hover {
    color: #3C5775
}

.morInfoLocation {
    width: 100%;
    text-align: center;
    padding: 20px 0
}

.morInfoLocation a {
    font-size: 18px;
    line-height: 24px;
    color: #fff
}

.morInfoLocation a:hover {
    color: #fff
}

.contactSection fieldset.pure-group .col6 {
    width: 50%;
    float: left;
    padding: 0 15px;
    box-sizing: border-box
}

.contactSection fieldset.pure-group .col6 .fieldRow {
    margin: 0 0 15px
}

.contactSection fieldset.pure-group .col6 .fieldRow label {
    font-size: 16px;
    line-height: 20px;
    color: #fff;
    display: block;
    margin: 0 0 10px 0
}

.contactSection fieldset.pure-group .col6 .fieldRow input,
.contactSection fieldset.pure-group .col6 .fieldRow textarea {
    background: #fff;
    border: 0;
    margin: 0;
    border-radius: 2px
}

.contactSection fieldset.pure-group .col6 .g-recaptcha {
    margin-top: 28px;
    margin-bottom: 15px
}

.contactSection fieldset.pure-group button {
    margin: 0;
    background-color: #3C5775;
    color: #fff;
    padding: 12px 50px
}

.contactSection .whatsAppDiv {
    padding: 25px 0 10px
}

.contactSection .whatsAppDiv img {
    max-width: 100%
}

.header-message {
    background-color: #096;
    padding: 10px;
    color: #FFF;
    font-weight: 700;
    text-align: center
}

.header-message a {
    color: #FFF
}

.msgSend {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
    position: relative;
    padding: 1rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid #fff0;
    border-radius: .25rem
}

.privacy-promo {
    color: #FFF;
    background-color: #099;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    -khtml-border-radius: 4px;
    text-align: center;
    padding: 20px;
    margin: 10px 0 30px 0
}

.privacy-promo img {
    margin: 20px auto
}

.privacy-promo h2 {
    color: #FFF
}

.privacy-promo h3 {
    color: #222;
    font-size: 30px
}

.events-filter {
    margin: 10px 0 0 0
}

@media(max-width:1500px) {
    .contactSection .contactInnerContainer {
        max-width: 100%;
        width: 90%
    }
}

@media(max-width:1199px) {
    .contactSection {
        padding: 60px 0
    }
}

@media(max-width:1023px) {
    .contactSection {
        padding: 50px 0
    }
    .contactSection .contactInnerContainer {
        padding: 20px
    }
    .contactSection .contact-us-page-number {
        padding: 10px 0 30px
    }
    .contactInfoRow .saprateRow {
        font-size: 14px
    }
    .bannerContainer {
        margin: 15px 8px 0
    }
    .bannerContainer img {
        max-width: 100%
    }
}

@media(max-width:767px) {
    .contactSection {
        padding: 40px 0
    }
    .contactInfoRow .saprateRow {
        width: 50%;
        float: left
    }
    .contactSection fieldset.pure-group .col6 {
        width: 100%;
        padding: 0
    }
    .contactSection .contact-us-page-number p {
        margin: 0 0 5px;
        font-size: 15px
    }
}

@media(max-width:575px) {
    .contactInfoRow .saprateRow {
        width: 100%;
        float: left
    }
    .contactSection .contactInnerContainer {
        width: 96%;
        padding: 20px 10px
    }
    .contactSection fieldset.pure-group .col6 .g-recaptcha {
        margin-top: 0
    }
    .morInfoLocation a {
        font-size: 16px
    }
}

@media(min-width:35.5em) {
    .container-news {
        width: 510px;
    }
    .container-news img {
        display: inline-block;
    }
    .container-news a {
        left: 85px;
    }
    .events-filter {
        float: right;
        margin: 0 0 0 15px
    }
    .pagination-pages {
        float: right;
        margin-top: 0
    }
    .review-rating {
        float: right
    }
    .wish-list-button {
        font-size: 1.1em
    }
    .add-review-button {
        font-size: 1.1em
    }
    .share-media-left {
        margin-left: 30px
    }
    .share-media-container {
        width: 320px
    }
    .not-found-humidor img {
        width: 450px
    }
    .sub-banner-2 {
        display: block
    }
    .sub-banner-pad strong {
        font-size: inherit;
        padding: 0
    }
    .sub-banner-pad i {
        font-size: inherit;
        width: 60px;
        font-size: 3em;
        padding: 0 0 0 10px
    }
    .sub-banner-pad span {
        font-size: inherit;
        font-size: 1em;
        width: -webkit-calc(100% - 70px);
        width: -moz-calc(100% - 70px);
        width: calc(100% - 70px)
    }
    .sub-banner-pad .sub-text {
        font-size: inherit;
        font-size: 13px;
        clear: both;
        padding: 0
    }
    #home-trustpilot .trustpilot-widget {
        margin: 20px 0 20px 20px
    }
}

@media(min-width:48em) {
    .review-rating {
        margin-right: 20px
    }
    .text-padding-left {
        padding-left: 20px;
        margin-top: 0
    }
    .text-padding-right {
        padding-right: 20px;
        margin-bottom: 0
    }
    .text-padding-both {
        padding-left: 10px;
        padding-right: 10px;
        margin-bottom: 0
    }
    .not-found-humidor img {
        width: 100%
    }
    .sub-banner-3 {
        display: block
    }
}

@media(min-width:64em) {
    h1 {
        font-size: 2.5em
    }
    .category-advert img {
        display: block
    }
    .category-advert-cigars img {
        display: block;
        height: 200px
    }
    .category-advert-alcohol img {
        display: block
    }
    .category-advert-gifts img {
        display: block;
        height: 320px
    }
    .category-advert-best-of img {
        display: block;
        height: 460px
    }
    .not-found-heading {
        text-align: center;
        margin-top: 30px;
        font-size: 48px
    }
    .not-found-text {
        float: left;
        margin-top: 10px;
        width: 100%;
        text-align: center;
        font-size: 18px
    }
    .not-found-humidor {
        float: left;
        margin-top: 50px;
        width: 100%;
        text-align: center
    }
    .checkout-delivery-van {
        display: block
    }
    .sub-banner-4 {
        display: block
    }
    #head-trustpilot .trustpilot-widget {
        margin-top: 10px;
        height: 30px;
        padding-top: 0
    }
    #deliveryCountdownTwo {
        display: block
    }
    .inner-wrapper-pad {
        padding: 0 20px
    }
}

@media(min-width:80em) {
    .category-advert-cigars img {
        height: 200px
    }
    .category-advert-accessories img {
        display: block
    }
    .category-advert-pipes-tobacco img {
        display: block
    }
    .category-advert-humidors img {
        display: block;
        height: 260px
    }
    #dl-menu-logo a {
        display: block
    }
}

@media(min-width:100em) {
    h1 {
        padding: 8px 0 0 0
    }
    .breadcrumbs_box {
        padding-left: 13px
    }
    #head-trustpilot .trustpilot-widget {
        display: block
    }
}

.mgmenu>li.cigar-auction>span a {
    color: #5f1516;
    padding-top: 1px
}

.mgmenu>li.cigar-auction>span i {
    color: #5f1516
}

.remember-me {
    float: left;
    width: 100%;
    height: 30px;
    margin-top: 5px
}

.remember-me input {
    float: left;
    height: 28px;
    line-height: 28px
}

.remember-me strong {
    float: left;
    height: 28px;
    line-height: 28px;
    margin-left: 5px
}

.privacy-settings ul li {
    padding: 3px 0
}

.cigar-library-box-outer {
    max-width: 760px;
    margin: 0 auto;
    padding: 20px;
    line-height: 25px;
}

.cigar-library-box {
    background-color: #FFF;
    overflow: hidden
}

.cigar-library-box h1 {
    text-align: center;
    line-height: 60px;
}
.cigar-library-box h2 {
    margin: 20px 0;
    padding: 0;
    line-height: 40px;
}

.cigar-library-box h2 a {
    text-decoration: none
}

.cigar-library-img {
    max-height: 300px;
    overflow: hidden
}

.cigar-library-image {
    height: 0;
    padding-bottom: 40%
}

.cigar-library-text ul {

}

.cigar-library-text ul li {
    list-style: disc
}

.cigar-library-text h1 {
    margin: 30px 0;
}

.cigar-library-gallery {
    background-color: #EEE;
    padding: 7px 0 0 7px
}

.cigar-library-gallery p {
    margin: 0;
    padding: 0 7px 7px 0
}

@media print {
    @page {
        size: 340mm 427mm
    }
    .container {
        width: 100%
    }
    #top_filter_update {
        display: none
    }
}

.magazine-opt-in {
    text-align: right;
    display: flex;
    justify-content: flex-end; /* Aligns content to the right */
    align-items: center;      /* Centers text and checkbox vertically */
    cursor: pointer;
    border-radius: 3px;
    background-color: #FAFAFA;
    color: #222;
    padding: 10px;
}
.magazine-opt-in input[type="checkbox"] {
    margin-left: 10px; /* Keeps space between text and checkbox */
}

.messagestack-error {
    margin-bottom: 10px
}

.in-store-only strong {
    padding-top: 5px;
    float: left;
    width: 100%;
    display: block;
    margin-bottom: 5px;
    font-weight: 700;
    color: #069;
    font-size: 22px
}

.in-store-only {
    margin-top: 10px;
    border: 4px solid #F3F3F3;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    -khtml-border-radius: 4px;
    padding: 10px;
    background-color: #FFF
}

#chat-button {
    font-weight: 700;
    position: fixed;
    bottom: 14px;
    right: 20px;
    z-index: 1000;
    padding: 10px 15px 10px 50px;
    background-color: #333;
    color: #fff;
    border: 4px solid #666;
    border-radius: 25px;
    cursor: pointer;
    box-shadow: 0 4px 8px rgb(0 0 0 / .2);
    background-image: url(/design/content/images/round-speech-b.png);
    background-repeat: no-repeat;
    background-position: 15px center;
    background-size: 25px
}